import { BACKEND_API_ENDPOINT } from '@/constants';
import apiPathName from './apiPathName';
import { BaseClient } from './baseClient';
import { GroupUser, Users } from './dataType';
import {
  DataResponse,
} from './type';


export class GroupUsersClient extends BaseClient<GroupUser> {
  moduleName = 'GroupUsersClient';
  constructor() {
    super(BACKEND_API_ENDPOINT + apiPathName.groupUser.root);
  }

  override getById(id?: string | number): Promise<DataResponse<GroupUser>> {
    return  this.api.get(`/${id}`) as Promise<DataResponse<GroupUser>>;
  }

  updateUserInGroup(groupId: string, userId: string, payload: Record<string, any> ): Promise<DataResponse<Users>> {
    return this.api.put(`/${groupId}/update-user/${userId}`, payload);
  }

  deleteGroupById(groupId: string): Promise<DataResponse<GroupUser[]>>{
    return  this.api.delete(`/${groupId}`);
  }

  getGroupUsersInOrg(): Promise<GroupUser[]> {
    return  this.api.get('/all')
  }
}
