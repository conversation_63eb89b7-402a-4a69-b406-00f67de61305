{"name": "web-config", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint && tsc --noEmit", "intl-extract": "formatjs extract 'components/**/*.{ts,tsx}' 'app/**/*.{ts,tsx}' 'constants/**/*.{ts,tsx}' 'hooks/**/*.{ts,tsx}' 'utils/**/*.{ts,tsx}' --ignore='**/*.d.ts' --out-file locales/en.json --extract-source-location --format formatter.js", "extract": "formatjs extract", "compile": "formatjs compile"}, "dependencies": {"@formatjs/intl": "^2.10.5", "@formatjs/intl-localematcher": "^0.5.7", "@formatjs/ts-transformer": "^3.13.14", "@logto/next": "^3.7.1", "@phosphor-icons/react": "^2.1.7", "@tanstack/react-query": "^5.59.0", "@tanstack/react-query-devtools": "^5.59.15", "axios": "^1.7.7", "dayjs": "^1.11.12", "google-libphonenumber": "^3.2.40", "negotiator": "^0.6.4", "next": "^15.3.2", "next-runtime-env": "^3.3.0", "nextjs-toploader": "^3.7.15", "nuqs": "^2.2.3", "qs": "^6.13.0", "react-dropzone": "^14.2.9", "react-international-phone": "^4.3.0", "react-intl": "^6.7.0", "server-only": "^0.0.1", "sharp": "^0.33.5", "socket.io-client": "^4.8.1", "ui-components": "gitlab:vietan-software/projects/ilotuslandx-document/ui-components#v2.1.6", "zustand": "^4.5.4"}, "devDependencies": {"@formatjs/cli": "^6.2.12", "@next/eslint-plugin-next": "^14.2.5", "@tanstack/eslint-plugin-query": "^5.58.1", "@types/google-libphonenumber": "^7.4.30", "@types/negotiator": "^0.6.3", "@types/node": "^20", "@types/qs": "^6.9.16", "@typescript-eslint/eslint-plugin": "^8.8.0", "@typescript-eslint/parser": "^8.8.0", "eslint": "^8", "eslint-config-next": "^15.3.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "eslint-plugin-simple-import-sort": "^12.1.1", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.1", "typescript": "5.3.3"}, "overrides": {"react": "19.1.0", "react-dom": "19.1.0", "@types/react": "19.1.0", "@types/react-dom": "19.1.0"}, "resolutions": {"next": "15.3.1"}}