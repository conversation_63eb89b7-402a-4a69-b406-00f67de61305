'use client';

import { useCreateClient } from '@/api/useCreateClient';
import ActionBaseContent from '@/components/commons/ActionBaseContent';
import { messageAlertCommon } from '@/constants/defineMessages';
import { moduleScope, Scopes } from '@/constants/scopes';
import { usePermissionModule } from '@/hook/usePermissionModule';
import { capitalizeFirstLetter } from '@/utils/string';
import { FormattedMessage, useIntl } from 'react-intl';
import { toast } from 'ui-components';

type RemoveRoleAccountProps = {
  onClose?: () => void;
  userId: string;
  dataRow?: Record<string, any>;
};

export function RemoveRoleAccount({ onClose, userId, dataRow }: RemoveRoleAccountProps) {
  const intl = useIntl();
  const actionUser = usePermissionModule(moduleScope.USERS, Scopes.UserConfigScopes);
  const mutationOptions = {
    invalidateQueries: {
      enable: true,
    },
  };

  const { users } = useCreateClient();

  const { mutateAsync: removeUserRole, isPending } = users.deleteUserRole(mutationOptions);
  const handleSubmit = async () => {
    if (!actionUser.UPDATE) return;
    try {
      await removeUserRole([userId, dataRow?.id]);
      const title = messageAlertCommon.removeRoleSuccess;
      toast({
        title: `${capitalizeFirstLetter(intl.formatMessage(title))}`,
        type: 'success',
        options: {
          position: 'top-center',
        },
      });
      onClose?.();
    } catch (error) {
      const title = messageAlertCommon.removeRoleFail;
      toast({
        title: `${capitalizeFirstLetter(intl.formatMessage(title))}`,
        type: 'error',
        options: {
          position: 'top-center',
        },
      });
      onClose?.();
    }
  };

  return (
    <ActionBaseContent onSubmit={handleSubmit} onCancel={onClose} disabled={isPending}>
      <div className="flex flex-col gap-1 items-center justify-center">
        <span className="text-center font-semibold text-lg leading-[27px] text-gray-800">
          <FormattedMessage
            defaultMessage="Xác nhận loại bỏ nhóm quyền"
            id="components.container.accounts.components.RemoveRoleAccount.confirmRoleRemoval"
          />
        </span>
        <span className="text-center font-normal text-sm leading-[21px] text-gray-700">
          <FormattedMessage
            defaultMessage="Bạn chắc chắn muốn loại bỏ nhóm quyền khỏi tài khoản này? Vui lòng xác nhận nếu bạn muốn tiếp tục."
            id="components.container.accounts.components.RemoveRoleAccount.areYouSureRoleRemoval"
          />
        </span>
      </div>
    </ActionBaseContent>
  );
}
