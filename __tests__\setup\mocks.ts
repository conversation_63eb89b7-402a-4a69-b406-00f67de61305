// Mock cho store
export const mockStore = {
  userPermissions: [],
  user: null,
  applicationAccess: null,
};

// Mock cho urlPathName
export const mockUrlPathName = {
  ROOT: '',
  HOME: '/',
  NOT_PERMISSION: '/not-permission',
  ACCESS_DENIED: '/access-denied',
  ACCOUNT: {
    ROOT: '/accounts',
    GROUP_ACCOUNT: '/accounts/group',
    GROUP_ACCOUNT_ID: '/accounts/group/:id',
    AUTHORIZATION: '/accounts/authorization',
  },
  SETTINGS: {
    ROOT: '/settings',
    ATTACHMENT: '/settings/attachment',
    STORAGECAPACITY: '/settings/storage-capacity',
    DIGITALSIGNATURE: '/settings/digital-signature',
    PACKAGEMANAGEMENT: '/settings/package-management',
  },
  MONITORING_STATION: {
    ROOT: '/monitoring-station',
  },
  HEALTH: '/health',
  PROFILE: '/profile',
};

// Mock cho pathWithScopes
export const mockPathWithScopes = {
  '': 'general:read',
  '/': 'general:read',
  '/not-permission': '',
  '/access-denied': '',
  '/accounts': 'user:read',
  '/accounts/group': 'group:read',
  '/accounts/group/:id': 'group:read',
  '/accounts/authorization': 'role:read',
  '/settings': 'setting:read',
  '/settings/attachment': 'setting:read',
  '/settings/storage-capacity': 'setting:read',
  '/settings/digital-signature': 'setting:read',
  '/settings/package-management': 'setting:read',
  '/monitoring-station': 'monitoring:read',
  '/health': '',
  '/profile': '',
};

// Setup mocks
export const setupMocks = () => {
  // Mock store
  jest.doMock('../../store', () => ({
    appStories: jest.fn(() => mockStore),
  }));

  // Mock urlPathName
  jest.doMock('../../constants/urlPathName', () => ({
    urlPathName: mockUrlPathName,
    pathWithScopes: mockPathWithScopes,
  }));

  // Mock next/navigation
  jest.doMock('next/navigation', () => ({
    useRouter: jest.fn(() => ({
      push: jest.fn(),
      replace: jest.fn(),
      pathname: '/',
    })),
    usePathname: jest.fn(() => '/'),
  }));
};

// Helper để update mock store
export const updateMockStore = (updates: Partial<typeof mockStore>) => {
  Object.assign(mockStore, updates);
};

// Helper để tạo mock permissions
export const createMockPermissions = (permissions: string[]) => {
  return permissions.map(keyName => ({ keyName }));
};

// Common permission sets
export const MOCK_PERMISSIONS = {
  ADMIN: createMockPermissions([
    'general:read',
    'user:read',
    'group:read',
    'role:read',
    'setting:read',
    'monitoring:read',
  ]),
  ACCOUNT_MANAGER: createMockPermissions([
    'user:read',
    'group:read',
    'role:read',
  ]),
  SETTINGS_MANAGER: createMockPermissions([
    'setting:read',
  ]),
  VIEWER: createMockPermissions([
    'user:read',
    'group:read',
  ]),
  NO_PERMISSIONS: [],
};

export default {
  setupMocks,
  updateMockStore,
  createMockPermissions,
  MOCK_PERMISSIONS,
  mockUrlPathName,
  mockPathWithScopes,
};
