'use client';

import { cn } from '@/utils/tailwind';
import React, { useEffect, useMemo, useState } from 'react';
import LeftAside from './LeftAside';
import { appStories } from '@/store';
import { Spin } from 'ui-components';
import { usePathAccess } from '@/hook/usePathAccess';
import { usePathname, useRouter } from 'next/navigation';
import { urlPathName } from '@/constants/urlPathName';
import SessionExpiredAlert from '../commons/SessionExpiredAlert';

type LayoutProps = {
  children: React.ReactNode;
  withOutHeader?: boolean;
  headerComponent?: React.ReactNode;
  subHeaderComponent?: React.ReactNode;
  tenantId?: string;
};

export default function LayoutDashboard({ children, headerComponent }: LayoutProps) {
  const router = useRouter();
  const pathName = usePathname();
  const { user } = appStories((state) => state);
  const { loading, unauthorized, redirectIfUnauthorized, firstAccessiblePath, canAccess, accessMap } = usePathAccess();
  const [isNavigating, setIsNavigating] = useState(false);

  console.log({ accessMap, firstAccessiblePath })

  const isAccess = useMemo(() => {
    return canAccess(pathName);
  }, [pathName, accessMap]);

  // Xử lý chuyển hướng khi quyền truy cập thay đổi
  useEffect(() => {
    if (loading || isAccess) return;

    // Bật trạng thái đang chuyển hướng
    setIsNavigating(true);

    // 1. Không có quyền truy cập bất kỳ route nào → về /not-permission
    if (unauthorized && pathName !== urlPathName.NOT_PERMISSION) {
      router.push(urlPathName.NOT_PERMISSION);
      return;
    }

    // 2. Có quyền, nhưng đang ở /not-permission (vô tình hoặc gõ tay) → redirect về trang đầu tiên được phép
    if (pathName === urlPathName.NOT_PERMISSION && !unauthorized && firstAccessiblePath) {
      router.push(firstAccessiblePath);
      return;
    }

    // 3. Có quyền, nhưng đang cố truy cập route không được phép → redirect đến firstAccessiblePath
    if (!canAccess(pathName)) {
      const redirectPath = redirectIfUnauthorized(pathName);
      if (redirectPath) {
        router.push(redirectPath);
        return;
      }
    }
  }, [loading, pathName, unauthorized, firstAccessiblePath, redirectIfUnauthorized, router, canAccess]);

  // Theo dõi sự thay đổi của pathName để biết khi nào chuyển hướng hoàn tất
  useEffect(() => {
    if (isNavigating) {
      // Đặt timeout nhỏ để đảm bảo UI đã cập nhật
      const timeoutId = setTimeout(() => {
        setIsNavigating(false);
      }, 300);

      return () => clearTimeout(timeoutId);
    }
  }, [pathName, isNavigating]);

  // Hiển thị loading khi đang kiểm tra quyền hoặc đang chuyển hướng
  if (!isAccess || loading || isNavigating) {
    return (
      <div className="h-screen w-screen relative flex flex-row items-center justify-center overflow-hidden">
        <Spin loading />
      </div>
    );
  }

  return (
    <div className="h-screen w-screen relative flex flex-row overflow-hidden">
      <SessionExpiredAlert />
      <LeftAside />
      {user &&
        (headerComponent ? (
          <div className={cn('flex-1 flex flex-col bg-gray-200')}>
            {headerComponent}
            <div className={cn('flex overflow-y-auto')}>{children}</div>
          </div>
        ) : (
          <>{children}</>
        ))}
    </div>
  );
}
