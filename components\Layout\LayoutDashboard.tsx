'use client';

import { cn } from '@/utils/tailwind';
import React, { useEffect, useMemo, useState } from 'react';
import LeftAside from './LeftAside';
import { appStories } from '@/store';
import { Spin } from 'ui-components';
import { usePathAccess } from '@/hook/usePathAccess';
import { usePathname, useRouter } from 'next/navigation';
import { urlPathName } from '@/constants/urlPathName';
import SessionExpiredAlert from '../commons/SessionExpiredAlert';

type LayoutProps = {
  children: React.ReactNode;
  withOutHeader?: boolean;
  headerComponent?: React.ReactNode;
  subHeaderComponent?: React.ReactNode;
  tenantId?: string;
};

export default function LayoutDashboard({ children, headerComponent }: LayoutProps) {
  const router = useRouter();
  const pathName = usePathname();
  const { user } = appStories((state) => state);
  const { loading, canAccess, redirectIfUnauthorized, isPathLoading } = usePathAccess();

  useEffect(() => {
    if (!loading) {
      const currentPath = pathName;

      if (!canAccess(currentPath)) {
        const redirectPath = redirectIfUnauthorized(currentPath);
        if (redirectPath) {
          router.replace(redirectPath);
        }
      }
    }
  }, [loading, pathName]);

  // Hiển thị loading khi đang kiểm tra quyền hoặc đang chuyển hướng
  if (loading) {
    return (
      <div className="h-screen w-screen relative flex flex-row items-center justify-center overflow-hidden">
        <Spin loading />
      </div>
    );
  }

  return (
    <div className="h-screen w-screen relative flex flex-row overflow-hidden">
      <SessionExpiredAlert />
      <LeftAside />
      {user &&
        (headerComponent ? (
          <div className={cn('flex-1 flex flex-col bg-gray-200')}>
            {headerComponent}
            <div className={cn('flex overflow-y-auto')}>{children}</div>
          </div>
        ) : (
          <>{children}</>
        ))}
    </div>
  );
}
