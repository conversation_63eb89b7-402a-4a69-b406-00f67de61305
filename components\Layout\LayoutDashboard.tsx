'use client';

import { cn } from '@/utils/tailwind';
import React, { useEffect, useMemo, useState } from 'react';
import LeftAside from './LeftAside';
import { appStories } from '@/store';
import { Spin } from 'ui-components';
import { usePathAccess } from '@/hook/usePathAccess';
import { usePathname, useRouter } from 'next/navigation';
import { urlPathName } from '@/constants/urlPathName';
import SessionExpiredAlert from '../commons/SessionExpiredAlert';

type LayoutProps = {
  children: React.ReactNode;
  withOutHeader?: boolean;
  headerComponent?: React.ReactNode;
  subHeaderComponent?: React.ReactNode;
  tenantId?: string;
};

export default function LayoutDashboard({ children, headerComponent }: LayoutProps) {
  const router = useRouter();
  const pathName = usePathname();
  const { user } = appStories((state) => state);
  const {
    loading,
    canAccess,
    canAccessWithLoading,
    redirectIfUnauthorized,
    getDefaultPathForCurrentModule,
    getModuleDefaultPath,
    isPathLoading,
    pathLoadingState,
    unauthorized,
    accessMap,
    accessiblePaths
  } = usePathAccess();

  console.log({ accessMap, accessiblePaths });

  const [isRedirecting, setIsRedirecting] = useState(false);
  const [accessCheckComplete, setAccessCheckComplete] = useState(false);

  // Enhanced route protection với module-based fallback
  useEffect(() => {
    if (!loading && !isRedirecting) {
      const checkAndRedirect = async () => {
        const currentPath = pathName;

        // Kiểm tra quyền truy cập với loading simulation
        try {
          setIsRedirecting(true);
          const hasAccess = await canAccessWithLoading(currentPath, 200);

          if (!hasAccess) {
            // Thử tìm đường dẫn mặc định trong cùng module trước
            const moduleDefaultPath = getDefaultPathForCurrentModule(currentPath);

            if (moduleDefaultPath && moduleDefaultPath !== currentPath) {
              console.log(`🔄 Redirecting to module default: ${moduleDefaultPath}`);
              router.replace(moduleDefaultPath);
              return;
            }

            // Nếu không có đường dẫn mặc định trong module, dùng redirect logic cũ
            const redirectPath = redirectIfUnauthorized(currentPath);
            if (redirectPath) {
              console.log(`🔄 Redirecting to: ${redirectPath}`);
              router.replace(redirectPath);
              return;
            }
          }

          setAccessCheckComplete(true);
        } catch (error) {
          console.error('❌ Error checking access:', error);
          // Fallback to synchronous check
          if (!canAccess(currentPath)) {
            const redirectPath = redirectIfUnauthorized(currentPath);
            if (redirectPath) {
              router.replace(redirectPath);
            }
          }
          setAccessCheckComplete(true);
        } finally {
          setIsRedirecting(false);
        }
      };

      checkAndRedirect();
    }
  }, [loading, pathName, canAccessWithLoading, getDefaultPathForCurrentModule, redirectIfUnauthorized]);

  // Reset access check khi path thay đổi
  useEffect(() => {
    setAccessCheckComplete(false);
  }, [pathName]);

  // Hiển thị loading states với thông tin chi tiết
  const isMainLoading = loading || isRedirecting;
  const hasPathLoading = Array.from(pathLoadingState.values()).some((loading) => loading);

  if (isMainLoading) {
    return (
      <div className="h-screen w-screen relative flex flex-row items-center justify-center overflow-hidden">
        <div className="flex flex-col items-center gap-4">
          <Spin loading />
          <div className="text-sm text-gray-600">
            {loading && 'Đang tải quyền truy cập...'}
            {isRedirecting && 'Đang chuyển hướng...'}
          </div>
          {hasPathLoading && <div className="text-xs text-blue-600">Đang kiểm tra quyền cho đường dẫn: {pathName}</div>}
        </div>
      </div>
    );
  }

  // Hiển thị trang unauthorized nếu user không có quyền truy cập bất kỳ trang nào
  if (unauthorized) {
    return (
      <div className="h-screen w-screen relative flex flex-row items-center justify-center overflow-hidden">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-800 mb-2">Không có quyền truy cập</h2>
          <p className="text-gray-600">Bạn không có quyền truy cập bất kỳ trang nào trong hệ thống.</p>
        </div>
      </div>
    );
  }

  // Hiển thị loading nếu chưa hoàn thành kiểm tra quyền truy cập
  if (!accessCheckComplete && !canAccess(pathName)) {
    return (
      <div className="h-screen w-screen relative flex flex-row items-center justify-center overflow-hidden">
        <div className="flex flex-col items-center gap-4">
          <Spin loading />
          <div className="text-sm text-gray-600">Đang xác thực quyền truy cập...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen w-screen relative flex flex-row overflow-hidden">
      <SessionExpiredAlert />

      <LeftAside />

      {user &&
        (headerComponent ? (
          <div className={cn('flex-1 flex flex-col bg-gray-200')}>
            {headerComponent}
            <div className={cn('flex overflow-y-auto')}>
              {/* Hiển thị loading indicator nếu current path đang loading */}
              {isPathLoading(pathName) && (
                <div className="absolute top-4 right-4 z-50">
                  <div className="bg-white rounded-lg shadow-lg p-3 flex items-center gap-2">
                    <span className="text-sm text-gray-600">Đang kiểm tra quyền...</span>
                  </div>
                </div>
              )}
              {children}
            </div>
          </div>
        ) : (
          <>{children}</>
        ))}
    </div>
  );
}
