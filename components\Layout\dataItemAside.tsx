import { FormattedMessage } from 'react-intl';
import { urlPathName } from '@/constants/urlPathName';
import type { UrlObject } from 'url';
import { Access, Scopes } from '@/constants/scopes';
type Url = string | UrlObject;
export const dataItemAside: AsideItemProps[] = [
  {
    name: <FormattedMessage defaultMessage="Tổng quan" id="components.Layout.dataItemAside.*********" />,
    icon: 'House',
    path: urlPathName.HOME,
    access: Scopes.GeneralConfigScopes.READ,
  },
  {
    name: <FormattedMessage defaultMessage="Người dùng" id="components.Layout.dataItemAside.**********" />,
    icon: 'User',
    path: urlPathName.ACCOUNT.ROOT,
    access: [Scopes.UserConfigScopes.READ, Scopes.RoleConfigScopes.READ, Scopes.GroupUserConfigScopes.READ],
  },
  {
    name: <FormattedMessage defaultMessage="Trạm quan trắc" id="components.Layout.dataItemAside.**********" />,
    icon: 'AirTrafficControl',
    path: urlPathName.MONITORING_STATION.ROOT,
    access: Scopes.MonitoringStationConfigScopes.READ,
  },
  {
    name: <FormattedMessage defaultMessage="Cài đặt" id="components.Layout.dataItemAside.**********" />,
    icon: 'Gear',
    path: urlPathName.SETTINGS.ROOT,
    access: Scopes.SettingConfigScopes.READ,
  },
];
export interface AsideItemProps {
  name: string | React.ReactNode;
  icon: string;
  path: Url;
  title?: any;
  access: Access;
}
