'use client';

import { Plus } from '@phosphor-icons/react';
import { FormattedMessage, useIntl } from 'react-intl';
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
} from 'ui-components';
import { AuthorizationTable } from './AuthorizationTable';
import { useState } from 'react';
import BaseFormData from './components/BaseFormData';
import ContentChangedBase from '@/components/commons/ContentChangedBase';
import { usePermissionModule } from '@/hook/usePermissionModule';
import { moduleScope, Scopes } from '@/constants/scopes';

export default function AuthorizationContainer() {
  const intl = useIntl();
  const actionRoles = usePermissionModule(moduleScope.ROLES, Scopes.RoleConfigScopes);
  const [stateModal, setStateModal] = useState({
    isOpenModalForm: false,
    isOpenModalChanged: false,
    isChangedData: false,
  });

  const onOpenChange = (open: boolean) => {
    if (stateModal.isChangedData) {
      return setStateModal((prevState) => ({
        ...prevState,
        isOpenModalForm: true,
        isOpenModalChanged: true,
      }));
    }
    return setStateModal((prevState) => ({
      ...prevState,
      isOpenModalChanged: false,
      isOpenModalForm: open,
    }));
  };
  return (
    <div className="block w-full h-full">
      <div className="h-[64px] flex flex-row items-center justify-between px-5 border-b border-gray-200">
        <div className="font-inter text-lg font-semibold leading-[27px] text-left text-gray-700">
          <FormattedMessage
            defaultMessage="Nhóm quyền"
            id="components.container.account-managenment.dataSiderbar.*********"
          />
        </div>
        {actionRoles.CREATE && (
          <Button className="flex flex-row items-center gap-2 text-white" onClick={() => onOpenChange(true)}>
            <Plus className="text-current" size={20} weight="regular" />
            <span>
              <FormattedMessage
                defaultMessage="Tạo nhóm quyền"
                id="components.container.authorization.AuthorizationContainer.********"
              />
            </span>
          </Button>
        )}
      </div>

      <AuthorizationTable />

      <Dialog open={stateModal.isOpenModalForm} onOpenChange={onOpenChange}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[680px] max-h-[610px] sm:rounded-xl">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <BaseFormData onClose={() => onOpenChange(false)} setStateModal={setStateModal} stateModal={stateModal} />
          </DialogContent>
        </DialogPortal>
      </Dialog>

      <Dialog
        open={stateModal.isOpenModalChanged}
        onOpenChange={(open) =>
          setStateModal((prevState) => ({
            ...prevState,
            isOpenModalChanged: open,
          }))
        }
      >
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[420px] max-h-[190px] sm:rounded-xl">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onCancel={() =>
                setStateModal((prevState) => ({
                  ...prevState,
                  isOpenModalChanged: false,
                }))
              }
              onSubmit={() =>
                setStateModal((prevState) => ({
                  ...prevState,
                  isOpenModalChanged: false,
                  isOpenModalForm: false,
                  isChangedData: false,
                }))
              }
              heading={intl.formatMessage({
                id: 'components.container.authorization.AuthorizationContainer.1403827211',
                defaultMessage: 'Hủy tạo nhóm quyền mới',
              })}
              description={intl.formatMessage({
                id: 'components.container.authorization.AuthorizationContainer.1686394713',
                defaultMessage:
                  'Bạn có chắc muốn hủy tạo nhóm quyền mới? Các thông tin mà bạn vừa nhập sẽ không được lưu lại.',
              })}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </div>
  );
}
