import { LIMIT_ITEM_PER_PAGE } from '@/constants';
import { getPagination } from '@/utils';
import { CaretLeft, CaretRight } from '@phosphor-icons/react';
import { useQueryState } from 'nuqs';
import { FormattedMessage } from 'react-intl';
import { Pagination, PaginationParams } from 'ui-components';

export default function PaginationTable({
  totalItems,
  limit = LIMIT_ITEM_PER_PAGE,
}: {
  totalItems: number;
  limit: number;
}) {
  const [page, setPage] = useQueryState('page', {
    defaultValue: '1',
    clearOnDefault: true,
  });

  const pagination = {
    total: totalItems || 0,
    pageSize: limit,
    pageIndex: Math.max(Number(page), 1),
    nextIcon: <CaretRight size={20} className="text-current" weight="regular" />,
    prevIcon: <CaretLeft size={20} className="text-current" weight="regular" />,
    onChange: (pageParams: PaginationParams) => {
      setPage(pageParams.pageIndex.toString());
    },
  };
  const { from, to } = getPagination(Math.max(Number(pagination.pageIndex) - 1, 0), limit);

  return (
    <div className="bg-gray-50 p-4 h-[68px] flex-shrink-0 border-t border-gray-200">
      <div className="flex justify-between items-center">
        <div className="font-normal text-sm">
          <FormattedMessage
            defaultMessage="Hiển thị <strong>{value}</strong> của <strong>{total}</strong>"
            id="components.commons.PaginationTable.1163538826"
            values={{
              strong: (chunks: React.ReactNode) => <strong key={'strong' + chunks?.toString()}>{chunks}</strong>,
              total: pagination.total,
              value: `${from + 1} ${from + 1 < pagination.total ? `- ${Math.min(to + 1, pagination.total)}` : ''}`,
            }}
          />
        </div>
        <Pagination {...pagination} />
      </div>
    </div>
  );
}
