import { pathWithScopes, urlPathName } from '@/constants/urlPathName';
import { appStories } from '@/store';
import { matchDynamicPath } from '@/utils/string';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';

// Các đường dẫn luôn được phép truy cập
const PUBLIC_PATHS: string[] = [urlPathName.ACCESS_DENIED, urlPathName.PROFILE, urlPathName.HEALTH];

// Mapping các module và các đường dẫn con của chúng
// Thứ tự trong mảng quyết định độ ưu tiên khi chọn đường dẫn mặc định cho module
const MODULE_PATHS = {
  general: [urlPathName.HOME],
  accounts: [
    urlPathName.ACCOUNT.ROOT,
    urlPathName.ACCOUNT.GROUP_ACCOUNT,
    urlPathName.ACCOUNT.GROUP_ACCOUNT_ID,
    urlPathName.ACCOUNT.AUTHORIZATION,
  ],
  settings: [
    urlPathName.SETTINGS.ROOT,
    urlPathName.SETTINGS.ATTACHMENT,
    urlPathName.SETTINGS.STORAGECAPACITY,
    urlPathName.SETTINGS.DIGITALSIGNATURE,
    urlPathName.SETTINGS.PACKAGEMANAGEMENT,
  ],
  'monitoring-station': [urlPathName.MONITORING_STATION.ROOT],
} as const;

// Hàm lấy module từ path
const getModuleFromPath = (path: string): string | null => {
  for (const [module, paths] of Object.entries(MODULE_PATHS)) {
    if (paths.some((p) => path.startsWith(p.split(':')[0]))) {
      return module;
    }
  }
  return null;
};

// Hàm kiểm tra xem một đường dẫn có phải là đường dẫn động hay không
// Truyền vào 1 path, check xem path trong pathWithScopes có chứa ':' hay không

const isDynamicPath = (path: string): boolean => {
  return Object.keys(pathWithScopes).some((p) => p.includes(':') && matchDynamicPath(p, path));
};

//get pattern dynamic
// Truyền vào 1 path, trả về pattern dynamic
const getPatternDynamic = (path: string): string => {
  return Object.keys(pathWithScopes).find((p) => p.includes(':') && matchDynamicPath(p, path))!;
};

// Hàm so sánh để sắp xếp đường dẫn, ưu tiên đường dẫn tĩnh hơn đường dẫn động
const comparePaths = (a: string, b: string): number => {
  const aDynamic = isDynamicPath(a);
  const bDynamic = isDynamicPath(b);

  // Nếu a là tĩnh và b là động, a được ưu tiên
  if (!aDynamic && bDynamic) return -1;
  // Nếu a là động và b là tĩnh, b được ưu tiên
  if (aDynamic && !bDynamic) return 1;

  // Nếu cả hai cùng loại, sắp xếp theo độ dài đường dẫn (ngắn hơn trước)
  const aSegments = a.split('/').filter(Boolean).length;
  const bSegments = b.split('/').filter(Boolean).length;

  if (aSegments !== bSegments) return aSegments - bSegments;

  // Nếu cùng độ dài, sắp xếp theo bảng chữ cái
  return a.localeCompare(b);
};

export const usePathAccess = () => {
  const { userPermissions } = appStories((state) => state);
  const [loading, setLoading] = useState(false);
  const [accessState, setAccessState] = useState<{
    accessMap: Map<string, boolean>;
    accessiblePaths: string[];
    unauthorized: boolean;
    firstAccessiblePath: string;
    moduleDefaultPaths: Map<string, string>;
  } | null>(null);

  // State để theo dõi loading của từng path
  const [pathLoadingState, setPathLoadingState] = useState<Map<string, boolean>>(new Map());
  const pathLoadingRef = useRef<Map<string, NodeJS.Timeout>>(new Map());

  // Memoize pathEntries để tránh tính toán lại
  const pathEntries = useMemo(() => Object.entries(pathWithScopes), []);

  // Lấy tất cả các đường dẫn từ urlPathName
  const allPaths = useMemo(() => {
    const paths: string[] = [];

    // Thêm các đường dẫn cấp cao nhất
    Object.values(urlPathName).forEach((value) => {
      if (typeof value === 'string') {
        paths.push(value);
      } else if (typeof value === 'object') {
        // Thêm các đường dẫn con
        Object.values(value).forEach((subPath) => {
          if (typeof subPath === 'string') {
            paths.push(subPath);
          }
        });
      }
    });

    return paths;
  }, []);

  // Xử lý dữ liệu quyền truy cập khi userPermissions thay đổi
  useEffect(() => {
    // Bắt đầu xử lý
    setLoading(true);

    // Lấy danh sách quyền của người dùng
    const permissionKeys = userPermissions?.map((p) => p.keyName) || [];

    // Tạo accessMap cho tất cả các đường dẫn
    const accessMap = new Map<string, boolean>();

    // Kiểm tra nếu người dùng không có quyền
    const hasNoPermissions = permissionKeys.length === 0;

    // Xử lý tất cả các đường dẫn
    allPaths.forEach((path) => {
      // Các đường dẫn public luôn được phép truy cập
      if (PUBLIC_PATHS.includes(path)) {
        accessMap.set(path, true);
      }
      // Trang NOT_PERMISSION chỉ được phép truy cập khi người dùng không có quyền
      else if (path === urlPathName.NOT_PERMISSION) {
        accessMap.set(path, hasNoPermissions);
      }
      // Kiểm tra các đường dẫn thông thường
      else if (path in pathWithScopes) {
        const requiredScope = pathWithScopes[path as keyof typeof pathWithScopes];
        accessMap.set(path, permissionKeys.includes(requiredScope));
      }
      // Kiểm tra các đường dẫn động
      else {
        let hasAccess = false;
        for (const [pattern, requiredScope] of pathEntries) {
          if (matchDynamicPath(pattern, path)) {
            hasAccess = permissionKeys.includes(requiredScope);
            break;
          }
        }
        accessMap.set(path, hasAccess);
      }
    });

    // Lọc các đường dẫn có quyền truy cập (không bao gồm NOT_PERMISSION và PUBLIC_PATHS)
    const accessiblePaths = Array.from(accessMap.entries())
      .filter(([path, hasAccess]) => hasAccess && path !== urlPathName.NOT_PERMISSION && !PUBLIC_PATHS.includes(path))
      .map(([path]) => path)
      // Sắp xếp đường dẫn, ưu tiên đường dẫn tĩnh hơn đường dẫn động
      .sort(comparePaths);

    // Tạo map các đường dẫn mặc định cho từng module
    const moduleDefaultPaths = new Map<string, string>();

    Object.entries(MODULE_PATHS).forEach(([module, modulePaths]) => {
      // Tìm đường dẫn đầu tiên trong module mà user có quyền truy cập
      const defaultPath = modulePaths.find((path) => accessMap.get(path) === true);
      if (defaultPath) {
        moduleDefaultPaths.set(module, defaultPath);
      }
    });

    // Kiểm tra nếu người dùng không có quyền truy cập bất kỳ đường dẫn nào (ngoài public paths)
    const unauthorized = accessiblePaths.length === 0;

    // Đường dẫn đầu tiên có quyền truy cập
    const firstAccessiblePath = unauthorized ? '' : accessiblePaths[0];

    // Cập nhật state
    setAccessState({
      accessMap,
      accessiblePaths,
      unauthorized,
      firstAccessiblePath,
      moduleDefaultPaths,
    });

    // Đặt timeout nhỏ để đảm bảo UI đã cập nhật
    const timeoutId = setTimeout(() => {
      setLoading(false);
    }, 100);

    return () => clearTimeout(timeoutId);
  }, [userPermissions, pathEntries, allPaths]);

  // Cache access logic với Map để tối ưu performance
  const accessCacheRef = useRef<Map<string, boolean>>(new Map());

  // Xóa cache khi userPermissions thay đổi
  useEffect(() => {
    accessCacheRef.current.clear();
    // Xóa tất cả loading states và timeouts
    pathLoadingRef.current.forEach((timeout) => clearTimeout(timeout));
    pathLoadingRef.current.clear();
    setPathLoadingState(new Map());
  }, [userPermissions]);

  // Cleanup timeouts khi component unmount
  useEffect(() => {
    return () => {
      pathLoadingRef.current.forEach((timeout) => clearTimeout(timeout));
      pathLoadingRef.current.clear();
    };
  }, []);

  // Hàm để set loading state cho một path
  const setPathLoading = useCallback((path: string, isLoading: boolean) => {
    setPathLoadingState((prev) => {
      const newMap = new Map(prev);
      if (isLoading) {
        newMap.set(path, true);
      } else {
        newMap.delete(path);
      }
      return newMap;
    });
  }, []);

  // Hàm kiểm tra xem path có đang loading không
  const isPathLoading = useCallback(
    (path: string): boolean => {
      return pathLoadingState.get(path) || false;
    },
    [pathLoadingState],
  );

  // Kiểm tra quyền truy cập đường dẫn (đồng bộ)
  const canAccess = useCallback(
    (path: string): boolean => {
      // Nếu không có accessState, không thể kiểm tra quyền
      if (!accessState) return false;
      const isDynamic = isDynamicPath(path);
      if (isDynamic) {
        const pattern = getPatternDynamic(path);
        return Boolean(accessState.accessMap.get(pattern));
      }
      return Boolean(accessState.accessMap.get(path));
    },
    [accessState, pathEntries, userPermissions],
  );

  // Kiểm tra quyền truy cập đường dẫn với loading simulation (bất đồng bộ)
  const canAccessWithLoading = useCallback(
    async (path: string, delay: number = 300): Promise<boolean> => {
      // Nếu đã có kết quả trong cache và không đang loading, trả về ngay
      if (accessCacheRef.current.has(path) && !isPathLoading(path)) {
        return accessCacheRef.current.get(path)!;
      }

      // Nếu đang loading, đợi cho đến khi hoàn thành
      if (isPathLoading(path)) {
        return new Promise((resolve) => {
          const checkInterval = setInterval(() => {
            if (!isPathLoading(path)) {
              clearInterval(checkInterval);
              resolve(canAccess(path));
            }
          }, 50);
        });
      }

      // Bắt đầu loading
      setPathLoading(path, true);

      // Simulate processing delay
      const timeoutId = setTimeout(() => {
        const result = canAccess(path);
        accessCacheRef.current.set(path, result);
        setPathLoading(path, false);

        // Cleanup timeout reference
        pathLoadingRef.current.delete(path);
      }, delay);

      // Store timeout reference for cleanup
      pathLoadingRef.current.set(path, timeoutId);

      // Return promise that resolves when processing is complete
      return new Promise((resolve) => {
        const checkInterval = setInterval(() => {
          if (!isPathLoading(path)) {
            clearInterval(checkInterval);
            resolve(accessCacheRef.current.get(path)!);
          }
        }, 50);
      });
    },
    [accessState, canAccess, isPathLoading, setPathLoading],
  );

  // Lấy đường dẫn mặc định có thể truy cập cho một module
  const getModuleDefaultPath = useCallback(
    (moduleName: string): string | null => {
      if (!accessState) return null;
      return accessState.moduleDefaultPaths.get(moduleName) || null;
    },
    [accessState],
  );

  // Lấy đường dẫn mặc định cho module dựa trên path hiện tại
  const getDefaultPathForCurrentModule = useCallback(
    (currentPath: string): string | null => {
      const moduleName = getModuleFromPath(currentPath);
      if (!moduleName) return null;
      return getModuleDefaultPath(moduleName);
    },
    [getModuleDefaultPath],
  );

  // Lấy tất cả đường dẫn có quyền trong một module
  const getAccessiblePathsInModule = useCallback(
    (moduleName: string): string[] => {
      if (!accessState) return [];

      const modulePaths = MODULE_PATHS[moduleName as keyof typeof MODULE_PATHS];
      if (!modulePaths) return [];

      return modulePaths.filter(path => canAccess(path));
    },
    [accessState, canAccess],
  );

  // Tìm đường dẫn thay thế tốt nhất trong cùng module
  const findAlternativePathInModule = useCallback(
    (currentPath: string): string | null => {
      const moduleName = getModuleFromPath(currentPath);
      if (!moduleName) return null;

      const accessiblePaths = getAccessiblePathsInModule(moduleName);
      if (accessiblePaths.length === 0) return null;

      // Loại bỏ path hiện tại khỏi danh sách
      const alternatives = accessiblePaths.filter(path => path !== currentPath);
      if (alternatives.length === 0) return null;

      // Tìm path có độ tương đồng cao nhất
      return alternatives.reduce((best, current) => {
        const currentSimilarity = calculatePathSimilarity(currentPath, current);
        const bestSimilarity = calculatePathSimilarity(currentPath, best);
        return currentSimilarity > bestSimilarity ? current : best;
      });
    },
    [getAccessiblePathsInModule],
  );

  // Kiểm tra xem có đường dẫn nào khả dụng trong module không
  const hasAccessiblePathsInModule = useCallback(
    (moduleName: string): boolean => {
      return getAccessiblePathsInModule(moduleName).length > 0;
    },
    [getAccessiblePathsInModule],
  );

  // Lấy danh sách module có quyền truy cập
  const getAccessibleModules = useCallback((): string[] => {
    if (!accessState) return [];
    return Array.from(accessState.moduleDefaultPaths.keys());
  }, [accessState]);

  // Kiểm tra nhiều đường dẫn cùng lúc với loading
  const canAccessMultiple = useCallback(
    async (paths: string[], delay: number = 300): Promise<Map<string, boolean>> => {
      const results = new Map<string, boolean>();

      // Process paths in parallel
      const promises = paths.map(async (path) => {
        const result = await canAccessWithLoading(path, delay);
        return { path, result };
      });

      const resolvedResults = await Promise.all(promises);
      resolvedResults.forEach(({ path, result }) => {
        results.set(path, result);
      });

      return results;
    },
    [canAccessWithLoading],
  );

  // Tìm đường dẫn tốt nhất để điều hướng dựa trên chiến lược ưu tiên
  const findBestRedirectPath = useCallback(
    (currentPath: string): string | null => {
      if (!accessState) return null;

      // Chiến lược 1: Tìm đường dẫn thay thế trong cùng module
      const alternativePath = findAlternativePathInModule(currentPath);
      if (alternativePath) {
        return alternativePath;
      }

      // Chiến lược 2: Tìm đường dẫn mặc định trong cùng module
      const moduleDefaultPath = getDefaultPathForCurrentModule(currentPath);
      if (moduleDefaultPath && canAccess(moduleDefaultPath)) {
        return moduleDefaultPath;
      }

      // Chiến lược 3: Tìm module có quyền truy cập theo thứ tự ưu tiên
      const moduleOrder = ['general', 'accounts', 'settings', 'monitoring-station'];

      for (const moduleName of moduleOrder) {
        if (hasAccessiblePathsInModule(moduleName)) {
          const defaultPath = getModuleDefaultPath(moduleName);
          if (defaultPath && canAccess(defaultPath)) {
            return defaultPath;
          }
        }
      }

      // Chiến lược 4: Tìm bất kỳ module nào có quyền truy cập
      const accessibleModules = getAccessibleModules();
      for (const moduleName of accessibleModules) {
        const defaultPath = getModuleDefaultPath(moduleName);
        if (defaultPath && canAccess(defaultPath)) {
          return defaultPath;
        }
      }

      // Chiến lược 5: Fallback đến đường dẫn đầu tiên có quyền
      if (accessState.firstAccessiblePath) {
        return accessState.firstAccessiblePath;
      }

      return null;
    },
    [
      accessState,
      canAccess,
      findAlternativePathInModule,
      getDefaultPathForCurrentModule,
      hasAccessiblePathsInModule,
      getModuleDefaultPath,
      getAccessibleModules
    ],
  );

  // Tính độ tương đồng giữa hai đường dẫn
  const calculatePathSimilarity = (path1: string, path2: string): number => {
    const segments1 = path1.split('/').filter(Boolean);
    const segments2 = path2.split('/').filter(Boolean);

    let commonSegments = 0;
    const minLength = Math.min(segments1.length, segments2.length);

    for (let i = 0; i < minLength; i++) {
      if (segments1[i] === segments2[i]) {
        commonSegments++;
      } else {
        break;
      }
    }

    return commonSegments / Math.max(segments1.length, segments2.length);
  };

  // Tìm đường dẫn tốt nhất để truy cập một module
  const getBestPathForModule = useCallback(
    (moduleName: string): string | null => {
      if (!accessState) return null;

      // Kiểm tra xem module có tồn tại không
      if (!MODULE_PATHS[moduleName as keyof typeof MODULE_PATHS]) {
        return null;
      }

      // Lấy tất cả đường dẫn có quyền trong module
      const accessiblePaths = getAccessiblePathsInModule(moduleName);

      if (accessiblePaths.length === 0) {
        return null;
      }

      // Ưu tiên đường dẫn mặc định của module
      const defaultPath = getModuleDefaultPath(moduleName);
      if (defaultPath && accessiblePaths.includes(defaultPath)) {
        return defaultPath;
      }

      // Nếu không có đường dẫn mặc định, lấy đường dẫn đầu tiên có quyền
      return accessiblePaths[0];
    },
    [accessState, getAccessiblePathsInModule, getModuleDefaultPath],
  );

  // Chuyển hướng thông minh với nhiều chiến lược
  const redirectIfUnauthorized = useCallback(
    (currentPath: string): string | null => {
      // Nếu có quyền truy cập, không cần chuyển hướng
      if (canAccess(currentPath)) return null;

      // Nếu không có accessState, không thể quyết định chuyển hướng
      if (!accessState) return null;

      // Tìm đường dẫn tốt nhất để điều hướng
      const bestPath = findBestRedirectPath(currentPath);

      if (bestPath) {
        return bestPath;
      }

      // Nếu không tìm được đường dẫn nào, chuyển đến trang không có quyền
      return urlPathName.NOT_PERMISSION;
    },
    [canAccess, accessState, findBestRedirectPath],
  );

  // Điều hướng thông minh đến module với đường dẫn tốt nhất
  const navigateToModule = useCallback(
    (moduleName: string): string | null => {
      const bestPath = getBestPathForModule(moduleName);

      if (!bestPath) {
        // Nếu không có quyền truy cập module này, tìm module thay thế
        const fallbackPath = findBestRedirectPath(`/${moduleName}`);
        return fallbackPath || urlPathName.NOT_PERMISSION;
      }

      return bestPath;
    },
    [getBestPathForModule, findBestRedirectPath],
  );

  return {
    // Trạng thái cơ bản
    loading,
    unauthorized: accessState?.unauthorized || false,
    firstAccessiblePath: accessState?.firstAccessiblePath || '',
    accessiblePaths: accessState?.accessiblePaths || [],
    accessMap: accessState?.accessMap || new Map<string, boolean>(),
    moduleDefaultPaths: accessState?.moduleDefaultPaths || new Map<string, string>(),
    pathLoadingState,

    // Kiểm tra quyền truy cập
    canAccess,
    canAccessWithLoading,
    canAccessMultiple,
    isPathLoading,

    // Điều hướng thông minh
    redirectIfUnauthorized,
    navigateToModule,
    findBestRedirectPath,

    // Quản lý module
    getModuleDefaultPath,
    getDefaultPathForCurrentModule,
    getBestPathForModule,
    getAccessiblePathsInModule,
    getAccessibleModules,
    hasAccessiblePathsInModule,
    findAlternativePathInModule,
  };
};
