'use client';

import IconCommon from '@/components/commons/IconLayout';
import { BaseTable } from '@/components/ui/base-table';
import { LIMIT_ITEM_PER_PAGE, QueryOperations } from '@/constants';
import { useQueryParams } from '@/hook/useQueryParams';
import { generateNums } from '@/utils';
import { cn } from '@/utils/tailwind';
import dayjs from 'dayjs';
import Image from 'next/image';
import { Fragment, MouseEvent, PropsWithChildren, useEffect, useMemo, useRef, useState } from 'react';
import {
  Badge,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Spin,
} from 'ui-components';
import { AccountForm } from './components/AccountForm';
import { ActivateAccount } from './components/ActivateAccount';
import { DeleteAccount } from './components/DeleteAccount';
import { UpdatePassword } from './components/UpdatePassword';
import { appStories } from '@/store';
import { LinkBreak, LockKey, Trash, UserCircle, UserMinus, X } from '@phosphor-icons/react';
import { defineMessages, FormattedMessage, useIntl } from 'react-intl';
import { messageStatusCommon } from '@/constants/defineMessages';
import { DisableAccount } from './components/DisableAccount';
import ContentChangedBase from '@/components/commons/ContentChangedBase';
import PaginationTable from '@/components/commons/PaginationTable';
import { ListPermissionGroup } from './components/ListPermissionGroup';
import { Users } from '@/api/dataType';
import { useCreateClient } from '@/api/useCreateClient';
import { IQueryOperations } from '@/components/commons/HeaderFilter/types';
import { FilterOptions, PaginationOptions, SortEnum, SortValue } from '@/api/type';
import { removeAccents } from '@/utils/string';
import { RemoveUserGroupAccount } from '../group-account/components/RemoveUserGroupAccount';
import { FilterAndSortProps } from './AccountContainer';
import OverflowTooltip from '../../commons/OverflowTooltip';
import { usePermissionModule } from '@/hook/usePermissionModule';
import { moduleScope, Scopes } from '@/constants/scopes';

const messagePage = defineMessages({
  firstName: {
    defaultMessage: 'Tên tài khoản',
    id: 'components.container.accounts.UserAccountPage.153f7b5e',
  },
  role: {
    defaultMessage: 'Nhóm quyền',
    id: 'components.container.accounts.UserAccountPage.eb9aedb7',
  },
  email: {
    defaultMessage: 'Email',
    id: 'components.container.accounts.UserAccountPage.c207467b',
  },
  status: {
    defaultMessage: 'Trạng thái',
    id: 'components.container.accounts.UserAccountPage.466e9033',
  },
  lastSignInAt: {
    defaultMessage: 'Đăng nhập cuối',
    id: 'components.container.accounts.UserAccountPage.ad8125d8',
  },
});
export default function UserAccountPage({
  groupId,
  dataFilterAndSortProps,
}: {
  groupId?: string;
  dataFilterAndSortProps?: FilterAndSortProps;
}) {
  const recordAccRef = useRef<Users>(null);
  const { user } = appStories((state) => state);
  const intl = useIntl();
  const actionUser = usePermissionModule(moduleScope.USERS, Scopes.UserConfigScopes);
  const actionUserGroup = usePermissionModule(moduleScope.USER_GROUPS, Scopes.GroupUserConfigScopes);

  const limit = LIMIT_ITEM_PER_PAGE;

  const locale = intl.locale as 'vi' | 'en';

  const [modalState, setModalState] = useState({
    isOpenAccountForm: false,
    isChangedData: false,
    isOpenConfirm: false,
    isChangeDataUpdatePass: false,
    isConfirmUpdatePass: false,
    isOpenDeleteAccount: false,
    deleteMe: false,
    disconnectAccountMe: false,
    disconnectAccount: false,
    connectAccount: false,
    isOpenChangePass: false,
    isLastAdminActiveDelete: false,
    isLastAdminActiveDisconnect: false,
    isOpenListGroupPer: false,
    isOpenRemoveUserGroup: false,
  });

  const orderBy = dataFilterAndSortProps?.sort as IQueryOperations<Users> | null;
  const paginationOptions: PaginationOptions = useMemo(
    () => ({
      ...(dataFilterAndSortProps?.page &&
        ({
          pageIndex: Number(dataFilterAndSortProps?.page),
          pageSize: LIMIT_ITEM_PER_PAGE,
        } as any)),
    }),
    [dataFilterAndSortProps?.page],
  );
  const filterCondition = dataFilterAndSortProps?.filters as IQueryOperations<Users>[] | null;

  const filterOptions: FilterOptions<Users> = useMemo(
    () => ({
      ...(dataFilterAndSortProps?.search && {
        $or: [
          {
            usernameVi: { $ilike: `%${dataFilterAndSortProps?.search}%` },
          },
          {
            usernameEn: { $ilike: `%${dataFilterAndSortProps?.search}%` },
          },
          {
            email: {
              $ilike: `%${removeAccents(dataFilterAndSortProps?.search)}%`,
            },
          },
        ],
      }),
      ...(filterCondition && {
        $and: filterCondition.map((item) => ({
          [item.key]: { $in: item.value },
        })),
      }),
      ...((groupId && {
        groups: groupId,
      }) as any),
    }),
    [dataFilterAndSortProps?.search, dataFilterAndSortProps?.filters],
  );

  const sortOptions: SortValue<Users> = useMemo(
    () => ({
      ...(orderBy && {
        [orderBy?.key === 'username' ? (locale === 'vi' ? 'usernameVi' : 'usernameEn') : orderBy?.key]: orderBy?.value,
      }),
    }),
    [orderBy?.key, orderBy?.value],
  );

  const { users } = useCreateClient();
  const { data: accounts, isFetching, isLoading } = users.find(filterOptions, paginationOptions, sortOptions);

  const { data: listUser } = users.find();
  const adminUsers = listUser?.data.list.filter(
    (user) => user.isActive === true && user.roles.some((role) => role.isAdmin === true),
  );
  const dataAccount = accounts?.data?.list ?? [];

  const handleDisconnect = async (event: MouseEvent<HTMLDivElement, globalThis.MouseEvent>, record: Users) => {
    event.stopPropagation();
    if (!actionUser.UPDATE) return;
    const checkCurentUser = user?.logtoId === record?.logtoId;

    if (checkCurentUser) {
      setModalState((prevState) => ({
        ...prevState,
        disconnectAccountMe: true,
      }));
    } else {
      if (adminUsers?.length === 1 && Boolean(adminUsers.find((u) => u.id === record.id))) {
        setModalState((prevState) => ({
          ...prevState,
          isLastAdminActiveDisconnect: true,
        }));
      } else {
        setModalState((prevState) => ({
          ...prevState,
          disconnectAccount: true,
        }));
        recordAccRef.current = record;
      }
    }
  };

  const handleDeleteAccount = async (event: MouseEvent<HTMLDivElement, globalThis.MouseEvent>, record: Users) => {
    event.stopPropagation();
    if (!actionUser.DELETE) return;

    const checkCurentUser = user?.logtoId === record?.logtoId;
    if (checkCurentUser) {
      setModalState((prevState) => ({ ...prevState, deleteMe: true }));
    } else {
      if (adminUsers?.length === 1 && adminUsers?.[0].id === record.id) {
        setModalState((prevState) => ({
          ...prevState,
          isLastAdminActiveDelete: true,
        }));
      } else {
        setModalState((prevState) => ({
          ...prevState,
          isOpenDeleteAccount: true,
        }));
        recordAccRef.current = record;
      }
    }
  };

  const handleRemoveFromAccountGroup = async (
    event: MouseEvent<HTMLDivElement, globalThis.MouseEvent>,
    record: Users,
  ) => {
    event.stopPropagation();
    if (!actionUserGroup.UPDATE) return;
    setModalState((prevState) => ({
      ...prevState,
      isOpenRemoveUserGroup: true,
    }));
    recordAccRef.current = record;
  };

  const columns = useMemo(
    () => [
      {
        title: intl.formatMessage(messagePage.firstName),
        key: 'id',
        className: 'bg-white flex-1 cursor-pointer',
        render: (record: Record<string, any>) => {
          const num = generateNums(record?.logtoId);
          const urlAvatar = record?.avatar ? record?.avatar : `/config/images/avatars/avatar-${num}.svg`;
          return (
            <div className="flex flex-row gap-2 w-full items-center">
              <Image
                src={urlAvatar}
                alt="avatar"
                width={26}
                height={26}
                className="rounded-full flex-shrink-0 h-[26px] w-[26px] object-contain"
                unoptimized
                priority
              />
              <OverflowTooltip text={`${record.username[locale]} ${record.givenName[locale]}`} className="w-fit" />
            </div>
          );
        },
      },
      {
        title: intl.formatMessage(messagePage.email),
        dataIndex: 'email',
        key: 'email',
        className: 'bg-white cursor-pointer',
        render: (value: string) => {
          return (
            <div className="flex flex-row gap-2 items-center w-full p-2">
              <OverflowTooltip text={value} className="w-fit" />
            </div>
          );
        },
      },
      {
        title: intl.formatMessage(messagePage.role),
        key: 'roles',
        className: 'bg-white cursor-pointer',
        render: (record: Users) => {
          if (record?.roles?.length > 0) {
            return (
              <div className="flex flex-row">
                <div
                  className="flex p-1 gap-1 rounded-[6px] hover:bg-gray-200 duration-300"
                  onClick={(event) => {
                    event.stopPropagation();
                    setModalState((prevState) => ({
                      ...prevState,
                      isOpenListGroupPer: true,
                    }));
                    recordAccRef.current = record;
                  }}
                >
                  {record?.roles.slice(0, 2).map((val) => (
                    <Badge
                      key={val.id}
                      variant="secondary"
                      className="shadow-none text-sm text-gray-700 max-w-[115px] font-medium"
                    >
                      <OverflowTooltip text={val.name[locale]} />
                    </Badge>
                  ))}
                  {record?.roles.length > 2 && (
                    <Badge
                      variant="secondary"
                      className="shadow-none text-sm text-gray-700 max-w-[45px] font-medium truncate block"
                    >
                      +{record?.roles.length - 2}
                    </Badge>
                  )}
                </div>
              </div>
            );
          }

          return null;
        },
      },
      {
        title: intl.formatMessage(messagePage.status),
        dataIndex: 'isActive',
        key: 'status',
        className: 'bg-white w-[130px] cursor-pointer',
        render: (value: boolean) => {
          let badgeContent;
          if (value) {
            badgeContent = (
              <Badge className="shadow-none text-sm">{intl.formatMessage(messageStatusCommon.activate)}</Badge>
            );
          } else if (value === false) {
            badgeContent = (
              <Badge variant="secondary" className="shadow-none text-sm font-medium">
                {intl.formatMessage(messageStatusCommon.disable)}
              </Badge>
            );
          }
          return badgeContent;
        },
      },
      {
        title: intl.formatMessage(messagePage.lastSignInAt),
        dataIndex: 'lastSignInAt',
        key: 'lastSignInAt',
        className: 'bg-white w-[180px] cursor-pointer',
        render: (value: string) => {
          return <span className="line-clamp-1">{value ? dayjs(value).format('DD/MM/YYYY HH:mm') : ''}</span>;
        },
      },
      ...((groupId ? actionUserGroup.UPDATE : actionUser.UPDATE || actionUser.DELETE || actionUser.CHANGE_PASSWORK)
        ? [
            {
              key: 'action',
              width: 60,
              minWidth: 60,
              className: 'bg-white text-white items-center justify-center h-full min-w-[60px] cursor-pointer',
              render: (record: Users) => {
                const status = record?.isActive;
                return (
                  <Popover>
                    <PopoverTrigger onClick={(event) => event.stopPropagation()} className="flex m-auto">
                      <div className="flex items-center justify-center text-gray-500 hover:bg-gray-200 hover:rounded-lg">
                        <IconCommon name="DotsThreeVertical" size={24} className="block text-current" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent
                      align="center"
                      side="left"
                      className="bg-white w-auto min-w-[260px] flex flex-col rounded-xl"
                    >
                      {actionUser.CHANGE_PASSWORK && (
                        <div
                          className="flex flex-row items-center text-gray-700 gap-2 cursor-pointer p-2 hover:bg-gray-100 hover:rounded-lg"
                          onClick={async (event) => {
                            event.stopPropagation();
                            if (!actionUser.CHANGE_PASSWORK) return;
                            setModalState((prevState) => ({
                              ...prevState,
                              isOpenChangePass: true,
                            }));
                            recordAccRef.current = record;
                          }}
                        >
                          <LockKey size={20} className="text-current" />
                          <span className="text-sm font-normal text-current">
                            <FormattedMessage
                              defaultMessage="Đổi mật khẩu tài khoản"
                              id="components.container.accounts.UserAccountPage.7fdbdb0d"
                            />
                          </span>
                        </div>
                      )}
                      {actionUser.UPDATE && (
                        <div className="flex flex-row items-center gap-2 text-gray-700 cursor-pointer hover:bg-gray-100 hover:rounded-lg">
                          {status ? (
                            <div className="flex gap-2 w-full p-2" onClick={(event) => handleDisconnect(event, record)}>
                              <LinkBreak size={20} className="text-current" />
                              <span className="text-sm font-normal text-current">
                                <FormattedMessage
                                  defaultMessage="Vô hiệu hóa tài khoản"
                                  id="components.container.accounts.UserAccountPage.0ed1adb9"
                                />
                              </span>
                            </div>
                          ) : (
                            <div
                              className="flex gap-2 w-full p-2"
                              onClick={async (event) => {
                                event.stopPropagation();
                                if (!actionUser.UPDATE) return;
                                setModalState((prevState) => ({
                                  ...prevState,
                                  connectAccount: true,
                                }));
                                recordAccRef.current = record;
                              }}
                            >
                              <UserCircle size={20} className="text-current" />
                              <span className="text-sm font-normal text-current">
                                <FormattedMessage
                                  defaultMessage="Kích hoạt tài khoản"
                                  id="components.container.accounts.UserAccountPage.18f6fa07"
                                />
                              </span>
                            </div>
                          )}
                        </div>
                      )}
                      {groupId && actionUserGroup.UPDATE && (
                        <div
                          className="flex flex-row items-center gap-2 text-red-500 cursor-pointer p-2 hover:bg-gray-100 hover:rounded-lg"
                          onClick={(event) => handleRemoveFromAccountGroup(event, record)}
                        >
                          <UserMinus size={20} className="text-current" />
                          <span className="text-sm font-normal text-current">
                            <FormattedMessage
                              defaultMessage="Loại bỏ khỏi nhóm tài khoản"
                              id="components.container.group-account.UserAccountGroupPage.removeFromAccGroup"
                            />
                          </span>
                        </div>
                      )}
                      {actionUser.DELETE && (
                        <div
                          className="flex flex-row items-center gap-2 text-red-500 cursor-pointer p-2 hover:bg-gray-100 hover:rounded-lg"
                          onClick={(event) => handleDeleteAccount(event, record)}
                        >
                          <Trash size={20} className="text-current" />
                          <span className="text-sm font-normal text-current">
                            <FormattedMessage
                              defaultMessage="Xóa tài khoản"
                              id="components.container.accounts.UserAccountPage.97d565ed"
                            />
                          </span>
                        </div>
                      )}
                    </PopoverContent>
                  </Popover>
                );
              },
            },
          ]
        : []),
    ],
    [actionUser.CHANGE_PASSWORK, actionUser.DELETE, actionUser.UPDATE],
  );

  const totalItems = accounts?.data?.pagination?.totalItems ?? 0;

  const onClickRow: any = async (record: Users) => {
    setModalState((prevState) => ({ ...prevState, isOpenAccountForm: true }));
    recordAccRef.current = record;
  };

  const onOpenChange = (open: boolean) => {
    if (modalState.isChangedData) {
      setModalState((prevState) => ({ ...prevState, isOpenConfirm: true }));
    } else {
      setModalState((prevState) => ({ ...prevState, isOpenAccountForm: open }));
    }
  };

  const onOpenChangeUpdatePass = (open: boolean) => {
    if (modalState.isChangeDataUpdatePass) {
      setModalState((prevState) => ({
        ...prevState,
        isConfirmUpdatePass: true,
      }));
    } else {
      setModalState((prevState) => ({ ...prevState, isOpenChangePass: open }));
    }
  };

  if (isLoading) {
    return <Spin loading></Spin>;
  }

  return (
    <>
      <div className="bg-white h-full w-full flex flex-col">
        <div
          className={cn('flex flex-col h-full w-full', {
            'h-[calc(100%_-_68px)]': totalItems > limit,
          })}
        >
          <div className="w-full h-auto flex-shrink-0">
            <BaseTable
              columns={columns}
              dataSource={dataAccount}
              tableLayout="fixed"
              striped={false}
              classNameRoot="flex-shink-0 h-auto"
              className="[&_.rc-table-content]:overflow-hidden [&_thead]:h-[49px] [&_.rc-table-thead]:border-0"
              rowKey={(record) => record.id}
              showHeader={dataAccount?.length > 0}
              components={{
                header: {
                  cell: CustomHeader,
                },
                body: {
                  cell: () => null,
                },
              }}
            />
          </div>
          <div className="table-body-custom flex-1 overflow-y-auto">
            <BaseTable
              columns={columns}
              loading={isLoading || isFetching}
              dataSource={dataAccount}
              tableLayout="fixed"
              striped={false}
              className="h-full [&_.rc-table-row:nth-child(1)]:border-y-0 [&_.rc-table-row:hover_.rc-table-cell]:bg-gray-50"
              rowKey={(record) => record.id}
              showHeader={false}
              components={{
                header: {
                  cell: CustomHeader,
                },
                body: {
                  cell: CustomBody,
                },
              }}
              onRow={(record, index) => ({
                onClick: () => onClickRow(record),
              })}
            />
          </div>
        </div>
        {totalItems > limit && <PaginationTable totalItems={totalItems} limit={limit} />}
      </div>
      <Dialog open={modalState.isOpenAccountForm} onOpenChange={onOpenChange}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className={cn('p-0 border-none gap-0 max-w-[1100px]')}>
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <AccountForm
              dataAccount={recordAccRef.current}
              adminUsers={adminUsers}
              onClose={() => {
                setModalState((prevState) => ({
                  ...prevState,
                  isOpenAccountForm: false,
                  isOpenConfirm: false,
                  isChangedData: false,
                }));
              }}
              isChangedData={modalState.isChangedData}
              setIsChangedData={(isChanged) =>
                setModalState((prevState) => ({
                  ...prevState,
                  isChangedData: Boolean(isChanged),
                }))
              }
              isOpenConfirm={modalState.isOpenConfirm}
              setIsOpenConfirm={(isOpen) =>
                setModalState((prevState) => ({
                  ...prevState,
                  isOpenConfirm: Boolean(isOpen),
                }))
              }
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>

      <Dialog open={modalState.isOpenChangePass} onOpenChange={onOpenChangeUpdatePass}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className={cn('p-0 border-none gap-0 max-w-[550px] max-h-[calc(100vh_-_100px)]')}>
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <UpdatePassword
              dataAccount={recordAccRef.current}
              isOpenChangePass={modalState.isOpenChangePass}
              onClose={() => {
                setModalState((prevState) => ({
                  ...prevState,
                  isOpenChangePass: false,
                  isConfirmUpdatePass: false,
                  isChangeDataUpdatePass: false,
                }));
              }}
              isChanged={modalState.isChangeDataUpdatePass}
              setIsChanged={(isChanged) =>
                setModalState((prevState) => ({
                  ...prevState,
                  isChangeDataUpdatePass: Boolean(isChanged),
                }))
              }
              isConfirmUpdatePass={modalState.isConfirmUpdatePass}
              setIsConfirmUpdatePass={(isConfirm) =>
                setModalState((prevState) => ({
                  ...prevState,
                  isConfirmUpdatePass: Boolean(isConfirm),
                }))
              }
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>

      <Dialog open={modalState.isOpenDeleteAccount}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[380px]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <DeleteAccount
              dataRow={recordAccRef.current!}
              onClose={() => {
                setModalState((prevState) => ({
                  ...prevState,
                  isOpenDeleteAccount: false,
                }));
              }}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>

      <Dialog open={modalState.connectAccount}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[380px]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ActivateAccount
              dataRow={recordAccRef.current!}
              onClose={() => {
                setModalState((prevState) => ({
                  ...prevState,
                  connectAccount: false,
                }));
              }}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
      <Dialog open={modalState.disconnectAccount}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[380px]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <DisableAccount
              dataRow={recordAccRef.current!}
              onClose={() => {
                setModalState((prevState) => ({
                  ...prevState,
                  disconnectAccount: false,
                }));
              }}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>

      <Dialog
        open={
          modalState.deleteMe ||
          modalState.disconnectAccountMe ||
          modalState.isLastAdminActiveDelete ||
          modalState.isLastAdminActiveDisconnect
        }
      >
        <DialogPortal>
          <DialogOverlay />
          <DialogContent
            className={cn('p-0 border-none gap-0 max-w-[350px]', {
              'max-w-[400px]': modalState.isLastAdminActiveDelete,
            })}
          >
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onSubmit={() => {
                setModalState((prevState) => ({
                  ...prevState,
                  deleteMe: false,
                  disconnectAccountMe: false,
                  isLastAdminActiveDelete: false,
                  isLastAdminActiveDisconnect: false,
                }));
              }}
              heading={
                modalState.deleteMe
                  ? intl.formatMessage({
                      defaultMessage: 'Không thể xóa tài khoản',
                      id: 'components.container.accounts.components.AccountAction.donotDeleteAccountAction',
                    })
                  : modalState.disconnectAccountMe
                    ? intl.formatMessage({
                        defaultMessage: 'Không thể vô hiệu hóa tài khoản',
                        id: 'components.container.accounts.components.AccountAction.donotDisableAccountAction',
                      })
                    : modalState.isLastAdminActiveDelete
                      ? intl.formatMessage({
                          defaultMessage: 'Không thể xóa tài khoản admin cuối cùng',
                          id: 'components.container.accounts.components.AccountAction.donotDeleteLastAdminAccountAction',
                        })
                      : modalState.isLastAdminActiveDisconnect
                        ? intl.formatMessage({
                            defaultMessage: 'Không thể vô hiệu hóa tài khoản admin cuối cùng',
                            id: 'components.container.accounts.components.AccountAction.donotDisableLastAdminAccountAction',
                          })
                        : ''
              }
              description={
                modalState.deleteMe
                  ? intl.formatMessage({
                      defaultMessage: 'Rất tiếc, bạn không thể xóa tài khoản của chính mình.',
                      id: 'components.container.accounts.components.AccountAction.donotDeleteMeAccount',
                    })
                  : modalState.disconnectAccountMe
                    ? intl.formatMessage({
                        defaultMessage: 'Rất tiếc, bạn không thể vô hiệu hóa tài khoản của chính mình.',
                        id: 'components.container.accounts.components.AccountAction.donotDisableMe',
                      })
                    : modalState.isLastAdminActiveDelete
                      ? intl.formatMessage({
                          defaultMessage:
                            'Rất tiếc, bạn không thể xóa tài khoản admin cuối cùng. Để đảm bảo hệ thống hoạt động ổn định và an toàn, ít nhất một tài khoản admin phải được giữ lại.',
                          id: 'components.container.accounts.components.AccountAction.donotDeleteLastAdminDescription',
                        })
                      : modalState.isLastAdminActiveDisconnect
                        ? intl.formatMessage({
                            defaultMessage:
                              'Rất tiếc, bạn không thể vô hiệu hóa tài khoản admin cuối cùng. Để đảm bảo hệ thống luôn có ít nhất một tài khoản admin hoạt động, chức năng này không được phép thực hiện.',
                            id: 'components.container.accounts.components.AccountAction.donotDisableLastAdminDescription',
                          })
                        : ''
              }
              showButton={true}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>

      <Dialog open={modalState.isOpenListGroupPer}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[420px]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ListPermissionGroup
              dataRow={recordAccRef.current!}
              adminUsers={adminUsers}
              onClose={() => {
                setModalState((prevState) => ({
                  ...prevState,
                  isOpenListGroupPer: false,
                }));
              }}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
      <Dialog open={modalState.isOpenRemoveUserGroup}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[380px]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <RemoveUserGroupAccount
              dataRow={recordAccRef.current!}
              groupId={groupId}
              onClose={() => {
                setModalState((prevState) => ({
                  ...prevState,
                  isOpenRemoveUserGroup: false,
                }));
              }}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
}

const CustomHeader = ({ children, ...restProps }: PropsWithChildren) => {
  const { className }: { className?: string } = restProps;
  return (
    <th {...restProps} className={cn(className, '!bg-gray-50')}>
      <span className="w-full line-clamp-1">{children}</span>
    </th>
  );
};

const CustomBody = ({ children, ...restProps }: PropsWithChildren) => {
  const { title }: { title?: string; className?: string } = restProps;
  if (title) {
    return (
      <td {...restProps} className="hover:bg-gray-300">
        <span className="line-clamp-1">{children}</span>
      </td>
    );
  }
  return <td {...restProps}>{children}</td>;
};
