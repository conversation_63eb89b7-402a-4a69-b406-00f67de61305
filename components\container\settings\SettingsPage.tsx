'use client';

import <PERSON>con<PERSON><PERSON>mon from '@/components/commons/IconLayout';
import { BaseTable } from '@/components/ui/base-table';
import { BASE_PATH } from '@/constants';
import { cn } from '@/utils/tailwind';

import { PropsWithChildren, useRef, useState } from 'react';
import {
  Badge,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Spin,
  Switch,
  toast,
} from 'ui-components';

import { Paperclip, PencilSimple, Wrench } from '@phosphor-icons/react';
import { FormattedMessage, useIntl } from 'react-intl';
import { ApplicationFileType } from '@/api/dataType';
import { useCreateClient } from '@/api/useCreateClient';
import { FileConfig, FileType } from '@/api/fileConfigClient';
import { ContentModalFileConfig } from './components/contentModalFileConfig';
import Image from 'next/image';
import { ContentModalFileType } from './components/contentModalFileType';

export default function SettingsPage() {
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';

  const recordFileConfigRef = useRef<FileConfig>(null);
  const recordFileTypeRef = useRef<FileType>(null);

  const [stateModal, setStateModal] = useState({
    isOpenModalFileConfig: false,
    isChangedData: false,
    isOpenModalConfirm: false,
    isOpenModalFileType: false,
    isChangedDataFileType: false,
    isOpenModalConfirmFileType: false,
  });

  const { fileConfig } = useCreateClient();

  const {
    data: dataFileConfig,
    isLoading: loadingFileConfig,
    isFetching: fetchingFileConfig,
    isPending: pendingFileConfig,
  } = fileConfig.getFileConfig();

  const {
    data: dataFileType,
    isLoading: loadingFileType,
    isFetching: fetchingFileType,
    isPending: pendingFileType,
  } = fileConfig.getFileType();

  const mutationOptions = {
    invalidateQueries: { enable: true },
  };

  const { mutateAsync: uploadPermissionAppAsync, isPending: pendingUploadPer } =
    fileConfig.uploadPermissionApp(mutationOptions);

  const { mutateAsync: updateFileTypeStatusAsync } = fileConfig.updateFileTypeStatus(mutationOptions);

  const handleAppPermissionFileType = async (values: ApplicationFileType) => {
    try {
      const payload = {
        fileTypeId: values?.fileType,
        applicationId: values?.application?.id!,
        enabled: !values.enabled,
      };

      await uploadPermissionAppAsync(payload);
      if (values?.enabled) {
        toast({
          title: `${intl.formatMessage({
            defaultMessage: 'Đã tắt sản phẩm áp dụng thành công.',
            id: 'components.container.settings.SettingsPage.appProductUnActiveSuccess',
          })}`,
          type: 'success',
          options: {
            position: 'top-center',
          },
        });
      } else {
        toast({
          title: `${intl.formatMessage({
            defaultMessage: 'Đã bật sản phẩm áp dụng thành công.',
            id: 'components.container.settings.SettingsPage.appProductActiveSuccess',
          })}`,
          type: 'success',
          options: {
            position: 'top-center',
          },
        });
      }
    } catch (error) {
      console.log('eror:', error);
      if (values?.enabled) {
        toast({
          title: `${intl.formatMessage({
            defaultMessage: 'Không thể tắt sản phẩm áp dụng. Vui lòng thử lại sau.',
            id: 'components.container.settings.SettingsPage.appProductUnActiveFail',
          })}`,
          type: 'error',
          options: {
            position: 'top-center',
          },
        });
      } else {
        toast({
          title: `${intl.formatMessage({
            defaultMessage: 'Không thể bật sản phẩm áp dụng. Vui lòng thử lại sau.',
            id: 'components.container.settings.SettingsPage.appProductActiveFail',
          })}`,
          type: 'error',
          options: {
            position: 'top-center',
          },
        });
      }
    }
  };

  const handleChangeStatusFileType = async (values: FileType) => {
    try {
      const payload = {
        fileTypeId: values?.id,
        status: values.status,
      };

      await updateFileTypeStatusAsync(payload);
      if (values.status) {
        toast({
          title: `${intl.formatMessage({
            defaultMessage: 'Đã tắt định dạng tệp tin cho phép đính kèm thành công.',
            id: 'components.container.settings.SettingsPage.turnOffExtentionFileSuccess',
          })}`,
          type: 'success',
          options: {
            position: 'top-center',
          },
        });
      } else {
        toast({
          title: `${intl.formatMessage({
            defaultMessage: 'Đã kích hoạt định dạng tệp tin cho phép đính kèm thành công.',
            id: 'components.container.settings.SettingsPage.turnOnExtentionFileSuccess',
          })}`,
          type: 'success',
          options: {
            position: 'top-center',
          },
        });
      }
    } catch (error) {
      console.log('eror:', error);
      if (values?.status) {
        toast({
          title: `${intl.formatMessage({
            defaultMessage: 'Không thể tắt định dạng tệp tin cho phép đính kèm. Vui lòng thử lại sau.',
            id: 'components.container.settings.SettingsPage.turnOffExtentionFileFail',
          })}`,
          type: 'error',
          options: {
            position: 'top-center',
          },
        });
      } else {
        toast({
          title: `${intl.formatMessage({
            defaultMessage: 'Không thể kích hoạt định dạng tệp tin cho phép đính kèm. Vui lòng thử lại sau.',
            id: 'components.container.settings.SettingsPage.turnOnExtentionFileFail',
          })}`,
          type: 'error',
          options: {
            position: 'top-center',
          },
        });
      }
    }
  };

  const columnsConfig = [
    {
      title: intl.formatMessage({
        defaultMessage: 'Thiết lập',
        id: 'components.container.authorization.components.BaseFormData.216090699',
      }),
      key: 'config',
      render: () => (
        <div className="font-medium text-gray-700">
          <FormattedMessage
            defaultMessage="Giới hạn mỗi tệp"
            id="components.container.settings.components.contentModalFileConfig.limitPerFile"
          />
        </div>
      ),
    },
    {
      title: intl.formatMessage({
        defaultMessage: 'Mô tả',
        id: 'components.container.authorization.AuthorizationContainer.78422312',
      }),
      key: 'des',
      render: () => (
        <div className="font-normal text-gray-700">
          <FormattedMessage
            defaultMessage="Dung lượng giới hạn cho từng tệp tin"
            id="components.container.settings.SettingsPage..limitedCapacityForEachFile"
          />
        </div>
      ),
    },
    {
      title: intl.formatMessage({
        defaultMessage: 'Giá trị',
        id: 'components.container.settings.components.contentModalFileConfig.valueFileConfig',
      }),
      key: 'valueFile',
      dataIndex: 'value',
      render: (val: string) => {
        return <div className="font-normal text-sm text-gray-700">{val}</div>;
      },
    },
    {
      title: intl.formatMessage({
        defaultMessage: 'Đơn vị',
        id: 'components.container.settings.components.contentModalFileConfig.unit',
      }),
      key: 'unit',
      dataIndex: 'unit',
      render: (val: string) => {
        return <div className="font-normal text-sm text-gray-700">{val}</div>;
      },
    },
    {
      title: intl.formatMessage({
        defaultMessage: 'Trạng thái',
        id: 'components.container.accounts.UserAccountPage.466e9033',
      }),
      key: 'status',
      dataIndex: 'status',
      render: (value: boolean) => {
        return <Switch size="sm" checked={value} disabled={true} />;
      },
    },
    {
      key: 'action',
      width: 60,
      minWidth: 60,
      className: 'bg-white text-white items-center justify-center h-full min-w-[60px] cursor-pointer',
      render: (record: FileConfig) => {
        return (
          <Popover>
            <PopoverTrigger onClick={(event) => event.stopPropagation()} className="flex m-auto">
              <div className="flex items-center justify-center text-gray-500 hover:bg-gray-200 hover:rounded-lg">
                <IconCommon name="DotsThreeVertical" size={24} className="block text-current" />
              </div>
            </PopoverTrigger>
            <PopoverContent
              align="center"
              side="bottom"
              className="bg-white w-auto min-w-[183px] flex flex-col rounded-xl p-2"
            >
              <div
                className="flex flex-row items-center text-gray-700 gap-2 cursor-pointer p-2 hover:bg-gray-100 hover:rounded-lg"
                onClick={async (event) => {
                  event.stopPropagation();
                  recordFileConfigRef.current = record;
                  setStateModal((prevState) => ({
                    ...prevState,
                    isOpenModalFileConfig: true,
                  }));
                }}
              >
                <PencilSimple size={20} className="text-current" />
                <span className="text-sm font-normal text-current">
                  <FormattedMessage
                    defaultMessage="Cập nhật giá trị"
                    id="components.container.settings.SettingsPage.updateValue"
                  />
                </span>
              </div>
            </PopoverContent>
          </Popover>
        );
      },
    },
  ];

  const columnsFileType = [
    {
      title: intl.formatMessage({
        defaultMessage: 'Định dạng',
        id: 'components.container.settings.SettingsPage.format',
      }),
      key: 'extensions',
      dataIndex: 'extensions',
      render: (val: string) => {
        return <div className="font-medium text-gray-700">{val}</div>;
      },
    },
    {
      title: intl.formatMessage({
        defaultMessage: 'Mô tả',
        id: 'components.container.authorization.AuthorizationContainer.78422312',
      }),
      key: 'description',
      dataIndex: 'description',
      render: (val: { vi: string; en: string }) => {
        return <div className="font-normal text-sm text-gray-700">{val[locale]}</div>;
      },
    },
    {
      title: intl.formatMessage({
        defaultMessage: 'Loại',
        id: 'components.container.settings.SettingsPage.typeFile',
      }),
      key: 'type',
      dataIndex: 'type',
      render: (val: { vi: string; en: string }) => {
        return <div className="font-normal text-sm text-gray-700">{val[locale]}</div>;
      },
    },
    {
      title: intl.formatMessage({
        defaultMessage: 'Sản phẩm áp dụng',
        id: 'components.container.settings.SettingsPage.productApplicable',
      }),
      key: 'application',
      dataIndex: 'applications',
      render: (record: ApplicationFileType[]) => {
        const sortedApps = [...record].sort((a, b) => a.application.order! - b.application.order!);
        return (
          <div className="flex gap-2">
            {sortedApps.map((item) => (
              <div
                key={`applications-${item.application.id}`}
                className="flex flex-row cursor-pointer"
                onClick={async () => await handleAppPermissionFileType(item)}
              >
                <Image
                  src={`${BASE_PATH}/images/branding/branding-${item?.enabled ? item?.application?.signature + '-active' : item?.application?.signature + '-unactive'}.svg`}
                  alt="logo"
                  width={32}
                  height={32}
                  unoptimized
                  priority
                  className="h-8 w-8 rounded-full"
                />
              </div>
            ))}
          </div>
        );
      },
    },
    {
      title: intl.formatMessage({
        defaultMessage: 'Trạng thái',
        id: 'components.container.accounts.UserAccountPage.466e9033',
      }),
      key: 'status',
      render: (record: FileType) => {
        return (
          <Switch size="sm" checked={record.status} onClick={async () => await handleChangeStatusFileType(record)} />
        );
      },
    },
    {
      key: 'action',
      width: 60,
      minWidth: 60,
      className: 'bg-white text-white items-center justify-center h-full min-w-[60px] cursor-pointer',
      render: (record: FileType) => {
        return (
          <Popover>
            <PopoverTrigger onClick={(event) => event.stopPropagation()} className="flex m-auto">
              <div className="flex items-center justify-center text-gray-500 hover:bg-gray-200 hover:rounded-lg">
                <IconCommon name="DotsThreeVertical" size={24} className="block text-current" />
              </div>
            </PopoverTrigger>
            <PopoverContent
              align="center"
              side="bottom"
              className="bg-white w-auto min-w-[183px] flex flex-col rounded-xl p-2"
            >
              <div
                className="flex flex-row items-center text-gray-700 gap-2 cursor-pointer p-2 hover:bg-gray-100 hover:rounded-lg"
                onClick={async (event) => {
                  event.stopPropagation();
                  recordFileTypeRef.current = record;
                  setStateModal((prevState) => ({
                    ...prevState,
                    isOpenModalFileType: true,
                  }));
                }}
              >
                <PencilSimple size={20} className="text-current" />
                <span className="text-sm font-normal text-current">
                  <FormattedMessage
                    defaultMessage="Cập nhật giá trị"
                    id="components.container.settings.SettingsPage.updateValue"
                  />
                </span>
              </div>
            </PopoverContent>
          </Popover>
        );
      },
    },
  ];

  const onOpenChange = (open: boolean) => {
    if (stateModal.isChangedData) {
      setStateModal((prevState) => ({ ...prevState, isOpenModalConfirm: true }));
    } else {
      setStateModal((prevState) => ({ ...prevState, isOpenModalFileConfig: open }));
    }
  };
  const onOpenChangeFileType = (open: boolean) => {
    if (stateModal.isChangedDataFileType) {
      setStateModal((prevState) => ({ ...prevState, isOpenModalConfirmFileType: true }));
    } else {
      setStateModal((prevState) => ({ ...prevState, isOpenModalFileType: open }));
    }
  };

  if (loadingFileConfig || loadingFileType) {
    return <Spin loading></Spin>;
  }

  return (
    <>
      <div className="h-full w-full flex flex-col gap-5">
        {/* Table File Config */}
        <div className="bg-white flex flex-col border border-gray-200 rounded-xl">
          <div className="flex items-center gap-3 p-3 border-b border-b-gray-200 text-gray-700">
            <span>
              <Wrench size={20} className="block text-current" weight="regular" />
            </span>
            <span className="font-medium">
              <FormattedMessage defaultMessage="Cấu hình" id="components.container.settings.SettingsPage.config" />
            </span>
          </div>
          <div className="w-full h-auto flex-shrink-0">
            <BaseTable
              columns={columnsConfig}
              dataSource={dataFileConfig?.data ? [dataFileConfig.data] : []}
              tableLayout="fixed"
              striped={false}
              classNameRoot="flex-shink-0 h-auto"
              className="[&_.rc-table-content]:overflow-hidden [&_thead]:h-[49px] [&_.rc-table-thead]:border-0"
              rowKey={(record) => Math.random()}
              showHeader={dataFileType?.data?.length! > 0}
              components={{
                header: {
                  cell: CustomHeader,
                },
                body: {
                  cell: () => null,
                },
              }}
            />
          </div>
          <div className="table-body-custom flex-1 overflow-y-auto rounded-b-xl">
            <BaseTable
              columns={columnsConfig}
              dataSource={dataFileConfig?.data ? [dataFileConfig.data] : []}
              tableLayout="fixed"
              striped={false}
              className="h-full [&_.rc-table-row:nth-child(1)]:border-y-0 [&_.rc-table-row:hover_.rc-table-cell]:bg-gray-50 [&_.rc-table-row:hover_.rc-table-cell]:rounded-b-xl"
              rowKey={(record) => Math.random()}
              showHeader={false}
              components={{
                header: {
                  cell: CustomHeader,
                },
                body: {
                  cell: CustomBody,
                },
              }}
            />
          </div>
        </div>
        {/* End Table File Config */}

        {/* Table File Type */}
        <div className="bg-white flex flex-col h-full border border-gray-200 rounded-xl overflow-y-auto scroll-hidden">
          <div className="flex items-center gap-3 p-3 border-b border-b-gray-200 text-gray-700 min-h-0">
            <span>
              <Paperclip size={20} className="block text-current" weight="regular" />
            </span>
            <span className="font-medium">
              <FormattedMessage
                defaultMessage="Định dạng tệp tin cho phép đính kèm"
                id="components.container.settings.SettingsPage.forrmatFileAccept"
              />
            </span>
          </div>
          <div className="flex flex-col w-full h-[calc(100%_-_48px)]">
            <div className="w-full h-auto flex-shrink-0">
              <BaseTable
                columns={columnsFileType}
                dataSource={dataFileType?.data || []}
                tableLayout="fixed"
                striped={false}
                classNameRoot="flex-shink-0 h-auto"
                className="[&_.rc-table-content]:overflow-hidden [&_thead]:h-[49px] [&_.rc-table-thead]:border-0"
                rowKey={(record) => record.id}
                showHeader={dataFileType?.data?.length! > 0}
                components={{
                  header: {
                    cell: CustomHeader,
                  },
                  body: {
                    cell: () => null,
                  },
                }}
              />
            </div>
            <div className="table-body-custom flex-1 overflow-y-auto rounded-b-xl">
              <BaseTable
                columns={columnsFileType}
                loading={loadingFileType || fetchingFileType}
                dataSource={dataFileType?.data || []}
                tableLayout="fixed"
                striped={false}
                className="h-full [&_.rc-table-row:nth-child(1)]:border-y-0 [&_.rc-table-row:last-child]:border-b-0 [&_.rc-table-row:hover_.rc-table-cell]:bg-gray-50"
                rowKey={(record) => record.id}
                showHeader={false}
                components={{
                  header: {
                    cell: CustomHeader,
                  },
                  body: {
                    cell: CustomBody,
                  },
                }}
              />
            </div>
          </div>
        </div>
        {/* End table File Type */}
      </div>
      <Dialog open={stateModal.isOpenModalFileConfig} onOpenChange={onOpenChange}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[420px]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentModalFileConfig
              dataFileConfig={recordFileConfigRef.current!}
              onClose={() => {
                setStateModal((prevState) => ({
                  ...prevState,
                  isOpenModalFileConfig: false,
                }));
              }}
              isChangedData={stateModal.isChangedData}
              setIsChangedData={(isChanged) =>
                setStateModal((prevState) => ({
                  ...prevState,
                  isChangedData: Boolean(isChanged),
                }))
              }
              isOpenModalConfirm={stateModal.isOpenModalConfirm}
              setIsOpenModalConfirm={(isOpen) =>
                setStateModal((prevState) => ({
                  ...prevState,
                  isOpenModalConfirm: Boolean(isOpen),
                }))
              }
              pendingFileConfig={pendingFileConfig}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
      <Dialog open={stateModal.isOpenModalFileType} onOpenChange={onOpenChangeFileType}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[520px]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentModalFileType
              dataFileType={recordFileTypeRef.current!}
              onClose={() => {
                setStateModal((prevState) => ({
                  ...prevState,
                  isOpenModalFileType: false,
                }));
              }}
              isChangedData={stateModal.isChangedDataFileType}
              setIsChangedData={(isChanged) =>
                setStateModal((prevState) => ({
                  ...prevState,
                  isChangedDataFileType: Boolean(isChanged),
                }))
              }
              isOpenModalConfirm={stateModal.isOpenModalConfirmFileType}
              setIsOpenModalConfirm={(isOpen) =>
                setStateModal((prevState) => ({
                  ...prevState,
                  isOpenModalConfirmFileType: Boolean(isOpen),
                }))
              }
              pendingFileType={pendingFileType || fetchingFileType}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
}

const CustomHeader = ({ children, ...restProps }: PropsWithChildren) => {
  const { className }: { className?: string } = restProps;
  return (
    <th {...restProps} className={cn(className, '!bg-gray-50')}>
      <span className="w-full line-clamp-1">{children}</span>
    </th>
  );
};

const CustomBody = ({ children, ...restProps }: PropsWithChildren) => {
  const { title }: { title?: string; className?: string } = restProps;
  if (title) {
    return (
      <td {...restProps} className="hover:bg-gray-300">
        <span className="line-clamp-1">{children}</span>
      </td>
    );
  }
  return <td {...restProps}>{children}</td>;
};
