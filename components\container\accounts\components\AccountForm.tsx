'use-client';

import IconCommon from '@/components/commons/IconLayout';
import { HeaderModal } from '@/components/ui/modal/header-modal';
import { emailRegExp } from '@/constants/regex';
import { cn } from '@/utils/tailwind';
import { useQueryClient } from '@tanstack/react-query';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';
import {
  Badge,
  Button,
  Combobox,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Form,
  FormItem,
  IDatePicker,
  Input,
  MultipleSelect,
  OptionItemType,
  toast,
} from 'ui-components';
import { ShowStrengthPassword } from '../CheckPassword';
import { UpdatePassword } from './UpdatePassword';
import InputLanguage from '../../../ui/input-language';
import { defaultCountries, parseCountry, PhoneInput } from 'react-international-phone';
import { CalendarBlank, EnvelopeSimple, X } from '@phosphor-icons/react';
import dayjs from 'dayjs';
import { FormattedMessage, useIntl } from 'react-intl';
import { messageAlertCommon, messageErrorCommon, messageStatusCommon } from '@/constants/defineMessages';
import ContentChangedBase from '@/components/commons/ContentChangedBase';
import { capitalizeFirstLetter } from '@/utils/string';
import { useCreateClient } from '@/api/useCreateClient';
import { GroupUser, Roles, Users } from '@/api/dataType';
import { PhoneNumberUtil } from 'google-libphonenumber';
import { usePermissionModule } from '@/hook/usePermissionModule';
import { moduleScope, Scopes } from '@/constants/scopes';
import { useParams } from 'next/navigation';
import { SortEnum } from '@/api/type';

interface AccountFormProps {
  onClose?: () => void;
  adminUsers?: Users[];
  dataAccount?: Users | undefined | null;
  isChangedData: boolean;
  setIsChangedData: Dispatch<SetStateAction<boolean>>;
  isOpenConfirm: boolean;
  setIsOpenConfirm: Dispatch<SetStateAction<boolean>>;
}

export function AccountForm({
  onClose,
  adminUsers,
  dataAccount,
  isChangedData,
  setIsChangedData,
  isOpenConfirm,
  setIsOpenConfirm,
}: AccountFormProps) {
  const [form] = Form.useForm();
  const intl = useIntl();
  const actionUser = usePermissionModule(moduleScope.USERS, Scopes.UserConfigScopes);
  console.log({ actionUser });
  const [isOpenChangePass, setIsOpenChangePass] = useState(false);
  const [isChangeDataUpdatePass, setIsChangeDataUpdatePass] = useState(false);
  const [isConfirmUpdatePass, setIsConfirmUpdatePass] = useState(false);

  const { users, roles, groupUsers } = useCreateClient();
  const locale = intl.locale as 'vi' | 'en';
  const isUpdate = Boolean(dataAccount?.id);

  const activeAdminUsers = adminUsers?.length === 1 && Boolean(adminUsers.find((u) => u.id === dataAccount?.id));

  const initValue = {
    givenName: {
      vi: '',
      en: '',
    },
    username: {
      vi: '',
      en: '',
    },
    description: {
      vi: '',
      en: '',
    },
    status: 'active',
    email: '',
    password: '',
    phone: '',
    role_id: [],
    group_account: [],
  };

  const initialValues = useMemo(() => {
    if (dataAccount && dataAccount?.id) {
      return {
        status: dataAccount.isActive === true ? 'active' : 'inactive',
        givenName: {
          vi: dataAccount?.givenName?.vi,
          en: dataAccount?.givenName?.en,
        },
        username: {
          vi: dataAccount?.username?.vi,
          en: dataAccount?.username?.en,
        },
        phone: dataAccount?.phone === '' || dataAccount?.phone === null ? '+84' : dataAccount?.phone,
        email: dataAccount?.email,
        role_id: dataAccount?.roles?.map((item: Roles) => item.id) || [],
        group_account: dataAccount?.groups?.map((gr: GroupUser) => gr?.id) || [],
        description: {
          vi: dataAccount?.description?.vi,
          en: dataAccount?.description?.en,
        },
        created_at: dayjs(dataAccount.createdAt).format('DD/MM/YYYY HH:mm'),
      };
    }
    return initValue;
  }, [dataAccount?.id]);

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues]);

  const mutationOptions = {
    invalidateQueries: { enable: true },
  };

  const { data: dataRole } = roles.find() ?? [];

  const { mutateAsync: createUserAsync, isPending: isPendingCreate } = users.create(mutationOptions);

  const { mutateAsync: updateUserAsync, isPending: isPendingUpdate } = users.update(mutationOptions);

  const phoneUtil = PhoneNumberUtil.getInstance();
  const queryClient = useQueryClient();

  const isPhoneValid = (phone: string) => {
    const countries = defaultCountries.map((country) => {
      const { dialCode } = parseCountry(country);
      return dialCode;
    });

    try {
      if (phone.length === 5) return true;
      return phoneUtil.isValidNumber(phoneUtil.parseAndKeepRawInput(phone));
    } catch (error) {
      const value = phone.startsWith('+') ? phone.slice(1) : phone;
      // Tách mã vùng và số liền kề
      let regionCode = '';
      for (let i = 1; i <= value.length; i++) {
        const prefix = value.slice(0, i);
        if (countries.includes(prefix)) {
          regionCode = prefix;
          break;
        }
      }
      const nextDigit = value[regionCode.length];

      if (nextDigit === '0') {
        return false;
      }
      return undefined;
    }
  };

  const listGroupUsers = groupUsers.getGroupUsersInOrg() ?? ([] as GroupUser[]);
  const dataGroupAccount = listGroupUsers.data ?? [];

  const optionsDataRole: OptionItemType[] | undefined = useMemo(
    () =>
      dataRole?.data?.list?.map((role: Roles) => {
        return {
          title: `${locale === 'vi' ? role.name.vi : role.name.en}`,
          value: role.id,
          disabled: activeAdminUsers && Boolean(dataAccount?.roles.find((c) => c.isAdmin === true && c.id === role.id)),
        };
      }),
    [dataRole, locale, dataAccount],
  );

  const optionsDataGroupAccount: OptionItemType[] = useMemo(() => {
    if (!dataGroupAccount) return [];
    return dataGroupAccount?.map((grAcc) => {
      return {
        title: `${grAcc.name[locale]}`,
        value: grAcc.id,
      };
    });
  }, [dataGroupAccount]);

  const handleSubmit = async (value: Record<string, any>) => {
    if (actionUser.CREATE || actionUser.UPDATE) {
      try {
        const body = {
          email: value.email,
          password: value.password,
          givenName: value.givenName,
          username: value.username,
          phone: value.phone === '+84' ? null : value.phone,
          role_id: value.role_id,
          group_account: value.group_account,
          description: value.description,
          birthdate: value.birthdate,
        };
        if (isUpdate) {
          await updateUserAsync([dataAccount?.id!, body]);
          queryClient.invalidateQueries({
            queryKey: ['RolesClient', 'find'],
            refetchType: 'all',
          });
        } else {
          await createUserAsync(body);
        }
        const title = isUpdate ? messageAlertCommon.updateSuccess : messageAlertCommon.createSuccess;
        toast({
          title: `${capitalizeFirstLetter(
            intl.formatMessage(title, {
              type: intl.formatMessage({
                defaultMessage: 'tài khoản',
                id: 'components.container.accounts.components.AccountForm.account',
              }),
            }),
          )}`,
          type: 'success',
          options: {
            position: 'top-center',
          },
        });
        onClose?.();
      } catch (error: any) {
        const { email, phone }: { email: boolean; phone: boolean } = error;
        if (phone || email) {
          if (isUpdate) {
            const fieldPhone = phone
              ? {
                  name: 'phone',
                  errors: [
                    intl.formatMessage({
                      defaultMessage: 'Số điện thoại đã tồn tại trong hệ thống.',
                      id: 'components.container.accounts.components.AccountForm.phoneAlreadyExists',
                    }),
                  ],
                }
              : null;
            const error: any = [fieldPhone].filter(Boolean);
            return form.setFields(error);
          } else {
            const fieldPhone = phone
              ? {
                  name: 'phone',
                  errors: [
                    intl.formatMessage({
                      defaultMessage: 'Số điện thoại đã tồn tại trong hệ thống.',
                      id: 'components.container.accounts.components.AccountForm.phoneAlreadyExists',
                    }),
                  ],
                }
              : null;
            const fieldEmail = email
              ? {
                  name: 'email',
                  errors: [
                    intl.formatMessage({
                      defaultMessage: 'Email đã tồn tại trong hệ thống.',
                      id: 'components.container.accounts.components.AccountForm.emailAlreadyExists',
                    }),
                  ],
                }
              : null;
            const error: any = [fieldPhone, fieldEmail].filter(Boolean);
            return form.setFields(error);
          }
        } else {
          const title = isUpdate ? messageAlertCommon.updateFail : messageAlertCommon.createFail;
          toast({
            title: `${capitalizeFirstLetter(
              intl.formatMessage(title, {
                type: intl.formatMessage({
                  defaultMessage: 'tài khoản',
                  id: 'components.container.accounts.components.AccountForm.account',
                }),
              }),
            )}`,
            type: 'error',
            options: {
              position: 'top-center',
            },
          });
          onClose?.();
        }
      }
    }
  };

  const handleClose = useCallback(() => {
    if (isChangedData) {
      setIsOpenConfirm(true);
    } else {
      onClose?.();
    }
  }, [isChangedData, onClose, setIsOpenConfirm]);

  const onOpenChange = useCallback(
    (open: boolean) => {
      if (isChangeDataUpdatePass) {
        setIsConfirmUpdatePass(true);
      } else {
        setIsOpenChangePass(open);
      }
    },
    [isChangeDataUpdatePass],
  );
  const titleModal: any = isUpdate ? (
    <FormattedMessage
      defaultMessage="Thông tin tài khoản"
      id="components.container.accounts.components.AccountForm.af223b70"
    />
  ) : (
    <FormattedMessage
      defaultMessage="Tạo tài khoản mới"
      id="components.container.accounts.components.AccountForm.ebba7d73"
    />
  );

  const disableForm = isUpdate ? !actionUser.UPDATE : !actionUser.CREATE;

  return (
    <>
      <Form
        name="accountForm"
        form={form}
        onFinish={handleSubmit}
        initialValues={initialValues}
        onFinishFailed={(err) => console.log(err)}
        onFieldsChange={(_, allFields) => {
          const isValid = allFields.some((field, index) => {
            if (isUpdate) {
              const fieldName = field.name[0] as
                | 'username'
                | 'givenName'
                | 'role_id'
                | 'description'
                | 'email'
                | 'phone'
                | 'group_account';
              return initialValues[fieldName] !== field.value;
            }
            return field.touched && field.value !== '';
          });
          setIsChangedData?.(isValid);
        }}
      >
        <HeaderModal
          title={titleModal}
          showButton={isUpdate ? actionUser.UPDATE : actionUser.CREATE}
          buttonForm={{
            content: isUpdate ? (
              <FormattedMessage
                defaultMessage="Cập nhật"
                id="components.container.authorization.components.BaseFormData.**********"
              />
            ) : (
              <FormattedMessage
                defaultMessage="Tạo mới"
                id="components.container.authorization.components.BaseFormData.**********"
              />
            ),
            type: 'submit',
            isPending: isUpdate ? isPendingUpdate : isPendingCreate,
          }}
          onClose={handleClose}
        />
        {/* Body Modal */}
        <div
          className={cn(
            'p-5 flex flex-row gap-4 self-stretch bg-gray-100 rounded-b-xl max-h-[calc(100vh_-_136px)] overflow-auto',
          )}
        >
          <div className={cn('flex flex-1 flex-col gap-4 p-4 rounded-xl bg-white h-full min-w-0 max-w-[650px]')}>
            <span className="text-base font-semibold text-gray-700">
              <FormattedMessage
                defaultMessage="Thông tin tài khoản"
                id="components.container.accounts.components.AccountForm.af223b70"
              />
            </span>
            <div className="flex flex-row gap-4 min-w-0">
              <div className="flex flex-col items-start gap-2 flex-grow flex-shink-0 basis-0">
                <div className="items-center space-x-2">
                  <span className="text-sm text-gray-800 font-medium">
                    <FormattedMessage
                      defaultMessage="Họ và tên đệm"
                      id="components.container.accounts.components.AccountForm.ab9a0cc1"
                    />
                  </span>
                  <span className="text-sm text-red-500 font-medium">*</span>
                </div>
                <Form.Field
                  name="givenName"
                  rules={[
                    {
                      validator(rule, value, callback) {
                        if (!(value.vi.length > 0)) {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage(messageErrorCommon.fieldRequired, {
                                field: intl.formatMessage({
                                  defaultMessage: 'Họ và tên đệm',
                                  id: 'components.container.accounts.components.AccountForm.ab9a0cc1',
                                }),
                              }),
                            ),
                          );
                        }

                        if (value.vi.length > 64) {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage(messageErrorCommon.fieldMax, {
                                max: 64,
                              }),
                            ),
                          );
                        }

                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  {({ value, onChange }, meta) => (
                    <InputLanguage
                      placeholder={intl.formatMessage({
                        defaultMessage: 'Nhập họ và tên đệm',
                        id: 'components.container.accounts.components.AccountForm.fb1a03fd',
                      })}
                      name="givenName"
                      meta={meta}
                      value={value}
                      onChange={onChange}
                      maxLenght={64}
                      disabled={disableForm}
                    />
                  )}
                </Form.Field>
              </div>
              <div className="flex flex-col items-start gap-2 flex-grow flex-shink-0 basis-0">
                <div className="items-center space-x-2">
                  <span className="text-sm text-gray-800 font-medium">
                    <FormattedMessage
                      defaultMessage="Tên tài khoản"
                      id="components.container.accounts.components.AccountForm.5d7d978b"
                    />
                  </span>
                  <span className="text-sm text-red-500 font-medium">*</span>
                </div>
                <Form.Field
                  name="username"
                  rules={[
                    {
                      validator(rule, value, callback) {
                        if (!(value.vi.length > 0)) {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage(messageErrorCommon.fieldRequired, {
                                field: intl.formatMessage({
                                  defaultMessage: 'Tên tài khoản',
                                  id: 'components.container.accounts.components.AccountForm.5d7d978b',
                                }),
                              }),
                            ),
                          );
                        }

                        if (value.vi.length > 64) {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage(messageErrorCommon.fieldMax, {
                                max: 64,
                              }),
                            ),
                          );
                        }

                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  {({ value, onChange }, meta) => (
                    <InputLanguage
                      placeholder={intl.formatMessage({
                        defaultMessage: 'Nhập tên tài khoản',
                        id: 'components.container.accounts.components.AccountForm.29ee7312',
                      })}
                      name="username"
                      meta={meta}
                      value={value}
                      onChange={onChange}
                      maxLenght={64}
                      disabled={disableForm}
                    />
                  )}
                </Form.Field>
              </div>
            </div>
            <div className="flex flex-col items-start gap-2 flex-grow flex-shink-0 basis-0">
              <div className="items-center space-x-2">
                <span className="text-sm text-gray-800 font-medium">
                  <FormattedMessage
                    defaultMessage="Nhóm quyền"
                    id="components.container.accounts.UserAccountPage.eb9aedb7"
                  />
                </span>
                <span className="text-sm text-red-500 font-medium">*</span>
              </div>
              <FormItem
                name="role_id"
                rules={[
                  {
                    validator(rule, value, callback) {
                      if (!(value.length > 0)) {
                        return Promise.reject(
                          new Error(
                            intl.formatMessage(messageErrorCommon.fieldRequired, {
                              field: intl.formatMessage({
                                defaultMessage: 'Nhóm quyền',
                                id: 'components.container.accounts.UserAccountPage.eb9aedb7',
                              }),
                            }),
                          ),
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                renderItem={({ control, meta, form, isError }) => {
                  return (
                    <MultipleSelect
                      options={optionsDataRole || []}
                      variant={isError ? 'error' : 'default'}
                      onChange={control.onChange}
                      maxTagCount={2}
                      modalPopover
                      placeholder={intl.formatMessage({
                        defaultMessage: 'Chọn nhóm quyền',
                        id: 'components.container.accounts.components.AccountForm.1afbc45c',
                      })}
                      sideOffset={-25}
                      size="md"
                      {...control}
                      customValue={(value, options, handleRemove) => {
                        const getRoles = adminUsers?.flatMap((item) => item.roles);
                        return value.map((val: string, index) => {
                          return (
                            <Badge
                              key={index}
                              className={cn('shadow-none max-w-[150px] truncate flex flex-row items-center gap-2')}
                            >
                              <span className="truncate flex-1">
                                {options.find((option) => option.value === val)?.title}
                              </span>
                              {activeAdminUsers &&
                              Boolean(getRoles?.find((c) => c.isAdmin === true && c.id === val)) ? (
                                ''
                              ) : (
                                <X
                                  size={14}
                                  onClick={(event: any) => {
                                    event.stopPropagation();
                                    handleRemove(val);
                                  }}
                                />
                              )}
                            </Badge>
                          );
                        });
                      }}
                      disabled={disableForm}
                    />
                  );
                }}
              />
            </div>
            <div className="flex flex-col items-start gap-2 flex-grow flex-shink-0 basis-0">
              <div className="items-center space-x-2">
                <span className="text-sm text-gray-800 font-medium">
                  <FormattedMessage
                    defaultMessage="Nhóm tài khoản"
                    id="components.container.accounts.UserAccountPage.accountGroup"
                  />
                </span>
              </div>
              <FormItem
                name="group_account"
                renderItem={({ control, meta, form, isError }) => {
                  return (
                    <MultipleSelect
                      name="group_account"
                      options={optionsDataGroupAccount}
                      variant={isError ? 'error' : 'default'}
                      onChange={control.onChange}
                      maxTagCount={2}
                      modalPopover
                      placeholder={intl.formatMessage({
                        defaultMessage: 'Chọn nhóm tài khoản',
                        id: 'components.container.accounts.components.AccountForm.selectGroupAccount',
                      })}
                      size="md"
                      sideOffset={-25}
                      textEmpty={intl.formatMessage({
                        defaultMessage: 'Chưa tạo nhóm tài khoản',
                        id: 'components.container.accounts.components.AccountForm.noAccountGroupsCreatedYet',
                      })}
                      disabled={disableForm}
                      {...control}
                    />
                  );
                }}
              />
            </div>
            <div className="flex flex-col items-start gap-2 flex-grow flex-shink-0 basis-0">
              <div className="items-center space-x-2">
                <span className="text-sm text-gray-800 font-medium">
                  <FormattedMessage
                    defaultMessage="Trạng thái"
                    id="components.container.accounts.UserAccountPage.466e9033"
                  />
                </span>
              </div>
              <FormItem
                name="status"
                renderItem={({ control, meta, form, isError }) => (
                  <Combobox
                    size="md"
                    placeholder={intl.formatMessage({
                      defaultMessage: 'Chọn trạng thái',
                      id: 'components.container.accounts.components.AccountForm.6378d56c',
                    })}
                    searchable={false}
                    triggerClassName="w-full"
                    variant={isError ? 'error' : 'default'}
                    disabled={true}
                    options={[
                      {
                        title: `${intl.formatMessage(messageStatusCommon.activate)}`,
                        value: 'active',
                      },
                      {
                        title: `${intl.formatMessage(messageStatusCommon.disable)}`,
                        value: 'inactive',
                      },
                    ]}
                    customValue={(value, options) => {
                      const title = options?.find((item) => item.value === value)?.title || '';
                      return (
                        <Badge
                          className={`${value === 'active' ? 'bg-blue-50 hover:bg-blue-50 shadow-none' : 'bg-gray-100 hover:bg-gray-100 shadow-none text-gray-700'}`}
                        >
                          {title}
                        </Badge>
                      );
                    }}
                    {...control}
                  />
                )}
              />
            </div>
            <div className="flex flex-col items-start gap-2 self-stretch">
              <div className="space-x-2">
                <span className="text-sm text-gray-800 font-medium">
                  <FormattedMessage
                    defaultMessage="Ghi chú"
                    id="components.container.accounts.components.AccountForm.5b1e5596"
                  />
                </span>
              </div>
              <Form.Field
                name="description"
                rules={[
                  {
                    validator(rule, value, callback) {
                      if (value?.vi?.length > 256) {
                        return Promise.reject(
                          new Error(
                            intl.formatMessage(messageErrorCommon.fieldMax, {
                              max: 256,
                            }),
                          ),
                        );
                      }

                      return Promise.resolve();
                    },
                  },
                ]}
              >
                {({ value, onChange }, meta) => (
                  <InputLanguage
                    placeholder={intl.formatMessage({
                      defaultMessage: 'Thêm ghi chú về tài khoản.',
                      id: 'components.container.accounts.components.AccountForm.5517185b',
                    })}
                    name="description"
                    meta={meta}
                    value={value}
                    onChange={onChange}
                    type="textarea"
                    maxLenght={256}
                    disabled={disableForm}
                  />
                )}
              </Form.Field>
            </div>
          </div>
          <div className={cn('flex flex-col gap-4 flex-1 h-full min-w-0')}>
            <div className="flex flex-1 flex-col gap-4 p-4 rounded-xl bg-white min-w-0">
              <div className="flex flex-col gap-1">
                <span className="text-base font-semibold text-gray-700">
                  <FormattedMessage
                    defaultMessage="Thông tin đăng nhập"
                    id="components.container.accounts.components.AccountForm.384afe01"
                  />
                </span>
                <span className="text-sm font-normal text-gray-500">
                  <FormattedMessage
                    defaultMessage="Email và số điện thoại có thể được sử dụng để đăng nhập vào hệ thống và nhận các thông báo, cảnh báo qua email và tin nhắn."
                    id="components.container.accounts.components.AccountForm.4072213d"
                  />
                </span>
              </div>
              <div className="flex flex-col items-start gap-2">
                <div className="items-center space-x-2">
                  <span className="text-sm text-gray-800 font-medium">
                    <FormattedMessage
                      defaultMessage="Email"
                      id="components.container.accounts.UserAccountPage.c207467b"
                    />
                  </span>
                  <span className="text-sm text-red-500 font-medium">*</span>
                </div>
                <FormItem
                  name="email"
                  rules={[
                    {
                      validator(rule, value, callback) {
                        if (!(value.length > 0)) {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage(messageErrorCommon.fieldRequired, {
                                field: intl.formatMessage({
                                  defaultMessage: 'Email',
                                  id: 'components.container.accounts.UserAccountPage.c207467b',
                                }),
                              }),
                            ),
                          );
                        }

                        if (!new RegExp(emailRegExp).test(value)) {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage(messageErrorCommon.fieldNotFormat, {
                                field: intl.formatMessage({
                                  defaultMessage: 'Email',
                                  id: 'components.container.accounts.UserAccountPage.c207467b',
                                }),
                              }),
                            ),
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                  renderItem={({ control, meta, form, isError }) => (
                    <div title={control.value} className="w-full">
                      <Input
                        placeholder={intl.formatMessage({
                          defaultMessage: 'Nhập email',
                          id: 'components.container.accounts.components.AccountForm.eff648a4',
                        })}
                        className={cn('placeholder-gray-500 text-sm [&_input:disabled]:text-gray-700', {
                          'cursor-not-allowed [&_input:disabled]:opacity-50': disableForm,
                        })}
                        onSubmitValue={control.onChange}
                        variant={isError ? 'error' : 'default'}
                        autoComplete="email"
                        prefix={<EnvelopeSimple size={16} />}
                        disabled={isUpdate || disableForm}
                        {...control}
                      />
                    </div>
                  )}
                />
              </div>
              <div className="flex flex-col items-start gap-2 w-full">
                <div className="items-center space-x-2">
                  <span className="text-sm text-gray-800 font-medium">
                    <FormattedMessage
                      defaultMessage="Số điện thoại"
                      id="components.container.accounts.components.AccountForm.1372d3c0"
                    />
                  </span>
                </div>
                <FormItem
                  name="phone"
                  rules={[
                    {
                      validator(rule, value) {
                        const checkPhone = isPhoneValid(value);
                        if (checkPhone === false && value !== '') {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage({
                                defaultMessage: 'Số điện thoại không hợp lệ.',
                                id: 'components.container.accounts.components.AccountForm.phoneNumberIsInvalid',
                              }),
                            ),
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                  renderItem={({ control, meta, form, isError }) => {
                    const checkPhone = isPhoneValid(control.value);
                    return (
                      <PhoneInput
                        defaultCountry="vn"
                        onChange={(value) => control.onChange(value)}
                        placeholder={intl.formatMessage({
                          defaultMessage: 'Nhập số điện thoại',
                          id: 'components.container.accounts.components.AccountForm.enterPhoneNumber',
                        })}
                        {...control}
                        className={cn(
                          'hover:ring-2 hover:ring-primary-300',
                          'rounded-lg h-11 w-full',
                          'transition-all ease-in duration-300',
                          '[&.react-international-phone-input-container_.react-international-phone-input]:w-full ',
                          '[&.react-international-phone-input-container_.react-international-phone-input]:h-full ',
                          '[&.react-international-phone-input-container_.react-international-phone-input]:block ',
                          '[&.react-international-phone-input-container_.react-international-phone-input]:rounded-r-lg ',
                          '[&.react-international-phone-input-container_.react-international-phone-country-selector-button]:h-full',
                          '[&.react-international-phone-input-container_.react-international-phone-country-selector-button]:rounded-l-lg',
                          '[&.react-international-phone-input-container_.react-international-phone-country-selector-button]:px-2',
                          '[&_.react-international-phone-country-selector-button]:w-[70px]',
                          '[&_.react-international-phone-country-selector-button_.react-international-phone-flag-emoji]:w-[35px]',
                          '[&_.react-international-phone-country-selector-button_.react-international-phone-flag-emoji]:h-[31px]',
                          '[&_.react-international-phone-input]:text-gray-700',
                          {
                            '[&_.react-international-phone-country-selector_.react-international-phone-country-selector-dropdown]:w-[475px]':
                              isUpdate === false,
                            '[&_.react-international-phone-country-selector_.react-international-phone-country-selector-dropdown]:w-[430px]':
                              isUpdate === true,
                          },
                          '[&_.react-international-phone-country-selector_.react-international-phone-country-selector-dropdown]:rounded-lg',
                          {
                            'ring-1 ring-red-400 hover:ring-red-400 hover:ring-1':
                              (checkPhone === false && control.value !== '') || meta.errors.length > 0,
                          },
                          {
                            'hover:ring-0 cursor-not-allowed [&_.react-international-phone-input]:text-gray-400':
                              disableForm,
                          },
                        )}
                        disabled={disableForm}
                      />
                    );
                  }}
                />
              </div>

              {isUpdate ? (
                actionUser.CHANGE_PASSWORK && (
                  <Button
                    onClick={() => setIsOpenChangePass(true)}
                    type="button"
                    outline
                    variant="neutral"
                    className="w-full hover:ring-2 hover:ring-primary-300 transition-all ease-in duration-300"
                  >
                    <div className="flex gap-2 w-full">
                      <div className="flex gap-2 self-stretch w-full">
                        <IconCommon name="LockKeyOpen" size={20} color="#3F3F46" />
                        <span className="text-gray-800 text-sm font-normal">
                          <FormattedMessage
                            defaultMessage="Đổi mật khẩu tài khoản"
                            id="components.container.accounts.components.AccountForm.6b8ea5ae"
                          />
                        </span>
                      </div>
                      <IconCommon name="CaretRight" size={20} color="#3F3F46" />
                    </div>
                  </Button>
                )
              ) : (
                <ShowStrengthPassword form={form} isOpenChangePass={isOpenChangePass} />
              )}
            </div>
            {isUpdate && (
              <div className="flex flex-1 flex-col gap-4 p-4 rounded-xl bg-white h-full min-w-0">
                <span className="text-base font-semibold text-gray-700">
                  <FormattedMessage
                    defaultMessage="Thông tin bổ sung"
                    id="components.container.accounts.components.AccountForm.255fbb25"
                  />
                </span>
                <div className="flex flex-col items-start gap-2 h-[145px]">
                  <div className="items-center space-x-2">
                    <span className="text-sm text-gray-700 font-medium">
                      <FormattedMessage
                        defaultMessage="Thời gian tạo"
                        id="components.container.accounts.components.AccountForm.c50f0b6f"
                      />
                    </span>
                  </div>
                  <div className="flex border border-gray-200 bg-gray-100 rounded-lg py-2 pr-[18px] pl-4 w-full h-11 items-center cursor-not-allowed">
                    <span className="flex-1 font-normal text-sm text-gray-700">
                      {dayjs(dataAccount?.createdAt).format('DD/MM/YYYY HH:mm')}
                    </span>
                    <CalendarBlank size={16} className="text-gray-400" />
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </Form>
      <Dialog open={isOpenConfirm}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="max-w-[420px] p-0">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onCancel={() => setIsOpenConfirm(false)}
              onSubmit={() => {
                setIsOpenConfirm(false);
                onClose?.();
              }}
              heading={
                isUpdate
                  ? intl.formatMessage({
                      defaultMessage: 'Hủy cập nhật thông tin tài khoản',
                      id: 'components.container.accounts.components.AccountAction.cancelUpdateInformationAccount',
                    })
                  : intl.formatMessage({
                      defaultMessage: 'Hủy tạo tài khoản mới',
                      id: 'components.container.accounts.components.AccountAction.cancelNewCreateAccount',
                    })
              }
              description={
                isUpdate
                  ? intl.formatMessage({
                      defaultMessage:
                        'Bạn có chắc muốn hủy cập nhật thông tin tài khoản? Các thông tin mà bạn vừa nhập sẽ không được lưu lại.',
                      id: 'components.container.accounts.components.AccountAction.areYouSureCancelUpdateAccount',
                    })
                  : intl.formatMessage({
                      defaultMessage:
                        'Bạn có chắc muốn hủy tạo tài khoản mới? Các thông tin mà bạn vừa nhập sẽ không được lưu lại.',
                      id: 'components.container.accounts.components.AccountAction.areYouSureCancelNewCreateAccount',
                    })
              }
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>

      <Dialog open={isOpenChangePass} onOpenChange={onOpenChange}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className={cn('p-0 border-none gap-0 max-w-[550px] ')}>
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <UpdatePassword
              dataAccount={dataAccount}
              isOpenChangePass={isOpenChangePass}
              onClose={() => {
                setIsOpenChangePass(false);
                setIsConfirmUpdatePass(false);
              }}
              isChanged={isChangeDataUpdatePass}
              setIsChanged={setIsChangeDataUpdatePass}
              isConfirmUpdatePass={isConfirmUpdatePass}
              setIsConfirmUpdatePass={setIsConfirmUpdatePass}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
}
