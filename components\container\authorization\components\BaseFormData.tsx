import { useCreateClient } from '@/api/useCreateClient';
import { Label } from '@/components/commons/LabelText';
import InputLanguage from '@/components/ui/input-language';
import { BASE_PATH } from '@/constants';
import { messageAlertCommon, messageErrorCommon } from '@/constants/defineMessages';
import { appStories } from '@/store';
import { capitalizeFirstLetter } from '@/utils/string';
import { cn } from '@/utils/tailwind';
import { X } from '@phosphor-icons/react';
import { useQueryClient } from '@tanstack/react-query';
import { Dispatch, SetStateAction, useEffect, useMemo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Alert, Badge, Button, Form, Spin, toast } from 'ui-components';
import { ConfigApplication } from './ConfigApplication';
import { usePermissionModule } from '@/hook/usePermissionModule';
import { moduleScope, Scopes } from '@/constants/scopes';

type IPropBaseForm = {
  onClose?: () => void;
  data?: Record<string, any> | null;
  setStateModal?: Dispatch<
    SetStateAction<{
      isOpenModalForm: boolean;
      isOpenModalChanged: boolean;
      isChangedData: boolean;
      [key: string]: any;
    }>
  >;
  stateModal: {
    isOpenModalForm: boolean;
    isOpenModalChanged: boolean;
    isChangedData: boolean;
    [key: string]: any;
  };
};

type IFormValue = {
  name: { vi: string; en: string };
  description: { vi: string; en: string };
  applications: {
    applicationId: string;
    name: string;
    status: string;
    allowWeb: boolean;
    allowMobile: boolean;
    permissions: {
      id: string;
      key: string;
    }[];
  }[];
};

const initLanguage = {
  vi: '',
  en: '',
};

const invalidateQueries = [
  {
    enable: true,
    queryKey: [
      'RolesClient',
      'find',
      {},
      {
        pageSize: 20,
        pageIndex: 0,
      },
    ],
    exact: true,
  },
  {
    enable: true,
    queryKey: [
      'UsersClient',
      'find',
      {
        $and: [
          {
            roles: {
              $in: [],
            },
          },
          {
            isActive: {
              $in: [],
            },
          },
        ],
      },
      {
        pageIndex: 1,
        pageSize: 20,
      },
      {
        createdAt: 'desc',
      },
    ],
    exact: true,
  },
  {
    enable: true,
  },
];

export default function BaseFormData({ onClose, data, setStateModal, stateModal }: IPropBaseForm) {
  const queryClient = useQueryClient();
  const intl = useIntl();
  const actionRoles = usePermissionModule(moduleScope.ROLES, Scopes.RoleConfigScopes);
  const [form] = Form.useForm();
  const { applicationPermissions } = appStories((state) => state);
  const isUpdate = Boolean(data?.id);
  const { roles } = useCreateClient();

  const { data: roleDetail, isLoading } = roles.getById(data?.id);
  const { mutateAsync: createRoleAsync, isPending: isPendingCreate } = roles.create({ invalidateQueries });
  const { mutateAsync: updateRoleAsync, isPending } = roles.update({ invalidateQueries });

  const initialValues = useMemo(() => {
    if (roleDetail) {
      const applications = roleDetail?.data?.applications;
      return {
        name: roleDetail?.data?.name,
        description: roleDetail?.data?.description || initLanguage,
        applications: applicationPermissions?.map((app) => {
          const application = applications?.find((item: any) => item.id === app.id);
          return {
            applicationId: app.id,
            name: app.name,
            signature: app.signature,
            allowWeb: application?.allowWeb || false,
            allowMobile: application?.allowMobile || false,
            permissions:
              application?.permissions?.map((per: { keyName: string; logtoId: string }) => ({
                key: per.keyName,
                id: per.logtoId,
              })) ?? [],
          };
        }),
      };
    }
    return {
      name: initLanguage,
      description: initLanguage,
      applications: applicationPermissions?.map((app) => {
        return {
          applicationId: app.id,
          name: app.name,
          signature: app.signature,
          allowWeb: false,
          allowMobile: false,
          permissions: [],
        };
      }),
    };
  }, [roleDetail, isLoading, applicationPermissions]);

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues.name, initialValues.description, initialValues.applications]);

  const applications = Form.useWatch('applications', form) as IFormValue['applications'];

  const applicationsActive = applications?.filter((item) => item.allowMobile || item.allowWeb);

  const onSubmit = async (values: IFormValue) => {
    if (actionRoles.CREATE || actionRoles.UPDATE) {
      const dataPrepare = { ...values };
      try {
        if (isUpdate) {
          await updateRoleAsync([data?.id, dataPrepare]);
        } else {
          await createRoleAsync(dataPrepare);
        }
        const title = isUpdate ? messageAlertCommon.updateSuccess : messageAlertCommon.createSuccess;
        toast({
          title: `${capitalizeFirstLetter(
            intl.formatMessage(title, {
              type: intl.formatMessage({
                defaultMessage: 'nhóm quyền',
                id: 'components.container.authorization.components.BaseFormData.1859212970',
              }),
            }),
          )}`,
          type: 'success',
          options: {
            position: 'top-center',
          },
        });
        queryClient.invalidateQueries({
          queryKey: ['UsersClient'],
          refetchType: 'all',
        });
      } catch (error) {
        console.error({ error });
        const title = isUpdate ? messageAlertCommon.updateFail : messageAlertCommon.createFail;
        toast({
          title: `${capitalizeFirstLetter(
            intl.formatMessage(title, {
              type: intl.formatMessage({
                defaultMessage: 'nhóm quyền',
                id: 'components.container.authorization.components.BaseFormData.1859212970',
              }),
            }),
          )}`,
          type: 'error',
          options: {
            position: 'top-center',
          },
        });
      }
      return setStateModal?.((prevState) => ({
        ...prevState,
        isChangedData: false,
        isOpenModalChanged: false,
        isOpenModalForm: false,
      }));
    }
  };

  const onCloseForm = () => {
    if (stateModal.isChangedData) {
      setStateModal?.((prevState) => ({
        ...prevState,
        isOpenModalChanged: true,
      }));
      return;
    }
    return onClose?.();
  };

  const disabledForm = isUpdate ? !actionRoles.UPDATE : !actionRoles.CREATE;

  return (
    <Spin loading={isLoading || isPending || isPendingCreate} className="[&>div]:bg-white/50 [&>div]:rounded-xl">
      <Form
        name="roleForm"
        form={form}
        initialValues={initialValues}
        onFinish={onSubmit}
        autoComplete="off"
        onFieldsChange={(_, allFields) => {
          const changeValue = allFields.some((item) => item.touched);
          setStateModal?.((prevState) => ({
            ...prevState,
            isChangedData: changeValue,
          }));
        }}
      >
        <div className="h-[64px] py-3 pr-3 pl-6 flex flex-row items-center justify-between gap-3 border-b border-gray-200">
          <span className="flex-1 font-semibold text-lg leading-[27px] text-gray-800">
            {isUpdate ? (
              <FormattedMessage
                defaultMessage="Thông tin nhóm quyền"
                id="components.container.authorization.components.BaseFormData.936352676"
              />
            ) : (
              <FormattedMessage
                defaultMessage="Tạo nhóm quyền mới"
                id="components.container.authorization.components.BaseFormData.924493073"
              />
            )}
          </span>
          {!disabledForm && (
            <Button type="submit" disabled={isPending || isPendingCreate}>
              {isUpdate ? (
                <FormattedMessage
                  defaultMessage="Cập nhật"
                  id="components.container.authorization.components.BaseFormData.1416668261"
                />
              ) : (
                <FormattedMessage
                  defaultMessage="Tạo mới"
                  id="components.container.authorization.components.BaseFormData.1512414659"
                />
              )}
            </Button>
          )}
          <Button variant="gray" icon type="button" onClick={onCloseForm}>
            <X size={20} className="text-current" weight="regular" />
          </Button>
        </div>
        <div className={cn('min-h-20 p-4 gap-4 flex flex-col bg-gray-100 rounded-b-xl', { 'gap-0': data?.isAdmin })}>
          <div className="flex flex-col gap-3 p-4 rounded-xl bg-white">
            <Label
              label={
                <FormattedMessage
                  defaultMessage="Thông tin tổng quan"
                  id="components.container.authorization.components.BaseFormData.1263009975"
                />
              }
              className="text-base"
            />
            {data && data?.isAdmin && (
              <Alert isVisible={true}>
                <FormattedMessage
                  defaultMessage="Đây là nhóm quyền có quyền quản trị cao nhất, do đó bạn sẽ không thể thiết lập quyền đối với nhóm quyền này."
                  id="components.container.authorization.components.BaseFormData.1439763285"
                />
              </Alert>
            )}
            <div className="flex flex-col gap-2">
              <Label
                label={
                  <FormattedMessage
                    defaultMessage="Tên nhóm quyền"
                    id="components.container.authorization.components.BaseFormData.1666962850"
                  />
                }
                required
                className="font-medium"
              />
              <div>
                <Form.Field
                  name="name"
                  rules={[
                    {
                      validator(rule, value, callback) {
                        if (!(value.vi.length > 0)) {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage(messageErrorCommon.fieldRequired, {
                                field: intl.formatMessage({
                                  defaultMessage: 'Tên nhóm quyền',
                                  id: 'components.container.authorization.components.BaseFormData.1666962850',
                                }),
                              }),
                            ),
                          );
                        }

                        if (value.vi.length > 64) {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage(messageErrorCommon.fieldMax, {
                                max: 64,
                              }),
                            ),
                          );
                        }

                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  {(control, meta) => (
                    <InputLanguage
                      placeholder={intl.formatMessage({
                        defaultMessage: 'Nhập tên nhóm quyền',
                        id: 'components.container.authorization.components.BaseFormData.1281850879',
                      })}
                      name="name"
                      meta={meta}
                      value={control.value}
                      onChange={control.onChange}
                      maxLenght={64}
                      disabled={disabledForm}
                      {...control}
                    />
                  )}
                </Form.Field>
              </div>
            </div>
            <div className="flex flex-col gap-2">
              <Label
                label={
                  <FormattedMessage
                    defaultMessage="Mô tả nhóm quyền"
                    id="components.container.authorization.components.BaseFormData.1302620338"
                  />
                }
                className="font-medium"
              />
              <div>
                <Form.Field
                  name="description"
                  rules={[
                    {
                      validator(rule, value, callback) {
                        if (value.vi.length > 256) {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage(messageErrorCommon.fieldMax, {
                                max: 256,
                              }),
                            ),
                          );
                        }

                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  {(control, meta) => {
                    return (
                      <InputLanguage
                        name="description"
                        placeholder={intl.formatMessage({
                          defaultMessage: 'Nhập mô tả nhóm quyền',
                          id: 'components.container.authorization.components.BaseFormData.577203663',
                        })}
                        type="textarea"
                        value={control.value}
                        onChange={control.onChange}
                        meta={meta}
                        maxLenght={256}
                        disabled={disabledForm}
                        {...control}
                      />
                    );
                  }}
                </Form.Field>
              </div>
            </div>
          </div>
          <div
            className={cn('flex flex-col gap-3 p-4 rounded-xl bg-white visible h-auto', {
              'invisible h-0 p-0 gap-0': data?.isAdmin,
            })}
          >
            <div className="flex flex-row items-center justify-between gap-2">
              <Label
                label={
                  <FormattedMessage
                    defaultMessage="Cấu hình truy cập"
                    id="components.container.authorization.components.BaseFormData.286541757"
                  />
                }
                className="font-semibold text-base block flex-1"
              />
              <Badge
                variant={applicationsActive?.length === applications?.length ? 'default' : 'secondary'}
                className="text-sm font-medium shadow-none"
              >
                {applicationsActive?.length}/{applications?.length}
              </Badge>
            </div>
            <Form.List name="applications">
              {(fields) => {
                return (
                  <div className="border border-gray-200 rounded-xl [&>*:nth-child(n):not(:first-child)]:border-t [&>*:nth-child(n):not(:first-child)]:border-t-gray-200">
                    {fields.map((field, index) => {
                      const image = `${BASE_PATH}/images/branding/branding-${applicationPermissions[index]?.signature}.svg`;
                      return (
                        <ConfigApplication
                          key={field.key}
                          image={image}
                          index={index}
                          appData={applicationPermissions[index]}
                          disabled={disabledForm}
                        />
                      );
                    })}
                  </div>
                );
              }}
            </Form.List>
          </div>
        </div>
      </Form>
    </Spin>
  );
}
