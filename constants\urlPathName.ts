import { Scopes } from './scopes';

export const urlPathName = Object.freeze({
  ROOT: '',
  HOME: '/',
  NOT_PERMISSION: '/not-permission',
  ACCESS_DENIED: '/access-denied',
  ACCOUNT: {
    ROOT: '/accounts',
    GROUP_ACCOUNT_ID: '/accounts/group/:id',
    GROUP_ACCOUNT: '/accounts/group',
    AUTHORIZATION: '/accounts/authorization',
  },
  MONITORING_STATION: {
    ROOT: '/monitoring-station',
  },
  SETTINGS: {
    ROOT: '/settings',
    ATTACHMENT: '/settings/attachment',
    STORAGECAPACITY: '/settings/storage-capacity',
    DIGITALSIGNATURE: '/settings/digital-signature',
    PACKAGEMANAGEMENT: '/settings/package-management',
  },
  HEALTH: '/health',
  PROFILE: '/profile',
});

export type UrlPathName = keyof typeof urlPathName;

export const pathWithScopes = Object.freeze({
  [urlPathName.HOME]: Scopes.GeneralConfigScopes.READ,
  [urlPathName.ACCOUNT.ROOT]: Scopes.UserConfigScopes.READ,
  [urlPathName.ACCOUNT.AUTHORIZATION]: Scopes.RoleConfigScopes.READ,
  [urlPathName.ACCOUNT.GROUP_ACCOUNT]: Scopes.GroupUserConfigScopes.READ,
  [urlPathName.ACCOUNT.GROUP_ACCOUNT_ID]: Scopes.GroupUserConfigScopes.READ,
  [urlPathName.MONITORING_STATION.ROOT]: Scopes.MonitoringStationConfigScopes.READ,
  [urlPathName.SETTINGS.ROOT]: Scopes.SettingConfigScopes.READ,
  [urlPathName.SETTINGS.ATTACHMENT]: Scopes.SettingConfigScopes.READ,
  [urlPathName.SETTINGS.STORAGECAPACITY]: Scopes.SettingConfigScopes.READ,
  [urlPathName.SETTINGS.DIGITALSIGNATURE]: Scopes.SettingConfigScopes.READ,
  [urlPathName.SETTINGS.PACKAGEMANAGEMENT]: Scopes.SettingConfigScopes.READ,
});
