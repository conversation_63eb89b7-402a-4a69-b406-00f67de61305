version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    ports:
      - '3000:3000'
    environment:
      - PORT=3000
      - NODE_ENV=production
      - NEXT_PUBLIC_CONFIG_BASE_URL=http://0.0.0.0:3000/config
      - NEXT_PUBLIC_CONFIG_BACKEND_API_ENDPOINT=http://localhost:8000/api/config/v1
      - NEXT_PUBLIC_WEB_CORE_URL=http://0.0.0.0:3003
      - NEXT_SERVER_ACTIONS_ENCRYPTION_KEY=zYltaT1Gx5n8C0Vl9SOB2KZJZm3F2Yvmx7l1c/vp5Gk=
