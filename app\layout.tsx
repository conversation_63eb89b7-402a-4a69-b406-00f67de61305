import type { Metadata } from 'next';
import { Inter } from 'next/font/google';
import { PublicEnvScript } from 'next-runtime-env';
import { BASE_URL, CONFIG_LANGUAGE } from '@/constants';
import NextTopLoader from 'nextjs-toploader';
import getIntl from '@/libraries/intl-server';
import 'react-international-phone/style.css';
import 'ui-components/dist/esm/index.css';
import './globals.css';
import ToasterWrapper from '@/components/ui/toaster';
import dynamic from 'next/dynamic';
import { cookies } from 'next/headers';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import { unstable_noStore as noStore } from 'next/cache';

const InitialLayout = dynamic(() => import('@/components/Layout/InitialLayout'));
const WrapIntlProvider = dynamic(() => import('@/components/Layout/WrapIntlProvider'));

const GuardAuth = dynamic(() => import('@/components/Layout/guardAuth'));

const inter = Inter({ subsets: ['latin'] });

export function generateMetadata(): Metadata {
  return {
    metadataBase: new URL(BASE_URL),
    title: 'iLotusLand for Config',
    description: 'Quản lý tài khoản, nhóm quyền, thiết lập cài đặt, theo dõi hệ thống.',
    icons: {
      icon: '/config/images/branding/branding-config.svg',
      apple: '/config/images/branding/branding-config.svg',
      shortcut: '/config/images/branding/branding-config.svg',
      other: {
        rel: '/config/images/branding/branding-config.svg',
        url: '/config/images/branding/branding-config.svg',
      },
    },
    openGraph: {
      title: 'iLotusLand for Config',
      description: 'Quản lý tài khoản, nhóm quyền, thiết lập cài đặt, theo dõi hệ thống.',
      images: '/images/branding/branding-config.svg',
      type: 'website',
    },
    twitter: {
      title: 'iLotusLand for Config',
      description: 'Quản lý tài khoản, nhóm quyền, thiết lập cài đặt, theo dõi hệ thống.',
      images: '/images/branding/branding-config.svg',
      card: 'summary_large_image',
    },
  };
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  noStore();
  const cookieStore = await cookies();
  const locale = cookieStore.get(CONFIG_LANGUAGE.cookieName)?.value || CONFIG_LANGUAGE.defaultLocale;
  const { messages } = await getIntl(locale);

  return (
    <html lang={locale} suppressHydrationWarning className={inter.className}>
      <head>
        <PublicEnvScript nextScriptProps={{ strategy: 'beforeInteractive' }} />
      </head>
      <body className={inter.className} suppressHydrationWarning>
        <NextTopLoader color="#3B82F6" showSpinner={false} />
        <NuqsAdapter>
          <InitialLayout>
            <WrapIntlProvider locale={locale} messages={messages}>
              <GuardAuth>{children}</GuardAuth>
            </WrapIntlProvider>
            <ToasterWrapper />
          </InitialLayout>
        </NuqsAdapter>
      </body>
    </html>
  );
}
