'use client';

import IconC<PERSON>mon from '@/components/commons/IconLayout';
import { HeaderModal } from '@/components/ui/modal/header-modal';
import { generateNums } from '@/utils';
import Image from 'next/image';
import { Dispatch, SetStateAction } from 'react';
import {
  Alert,
  Badge,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Form,
  toast,
} from 'ui-components';
import { ShowStrengthPassword } from '../CheckPassword';
import { FormattedMessage, useIntl } from 'react-intl';
import ContentChangedBase from '@/components/commons/ContentChangedBase';
import { messageAlertCommon, messageStatusCommon } from '@/constants/defineMessages';
import { capitalizeFirstLetter } from '@/utils/string';
import { useCreateClient } from '@/api/useCreateClient';
import OverflowTooltip from '@/components/commons/OverflowTooltip';

interface UpdatePasswordProps {
  onClose: () => void;
  dataAccount?: Record<string, any> | null;
  isOpenChangePass: boolean;
  isChanged: boolean;
  setIsChanged: Dispatch<SetStateAction<boolean>>;
  isConfirmUpdatePass: boolean;
  setIsConfirmUpdatePass: Dispatch<SetStateAction<boolean>>;
}

interface formValue {
  password: string;
  confirmPassword: string;
}

const UpdatePassword = ({
  onClose,
  dataAccount,
  isOpenChangePass,
  isChanged,
  setIsChanged,
  isConfirmUpdatePass,
  setIsConfirmUpdatePass,
}: UpdatePasswordProps) => {
  const [form] = Form.useForm();
  const intl = useIntl();

  const locale = intl.locale as 'vi' | 'en';

  const indexImage = generateNums(dataAccount?.logtoId!);
  const urlAvatar = `/config/images/avatars/avatar-${indexImage}.svg`;

  const mutationOptions = {
    invalidateQueries: {
      enable: true,
    },
  };

  const { users } = useCreateClient();

  const { mutateAsync: updatePasswordMutate, isPending } = users.updatePasswordProfile(mutationOptions);

  const handleSubmit = async (value: formValue) => {
    try {
      const { password } = value;
      await updatePasswordMutate({ userLogtoId: dataAccount?.logtoId, newPassword: password });

      const title = messageAlertCommon.changePassSuccess;
      toast({
        title: `${capitalizeFirstLetter(
          intl.formatMessage(title, {
            type: intl.formatMessage({
              defaultMessage: 'mật khẩu',
              id: 'components.container.accounts.components.UpdatePassword.password',
            }),
          }),
        )}`,
        type: 'success',
        options: {
          position: 'top-center',
        },
      });
      onClose?.();
    } catch (error) {
      const title = messageAlertCommon.changePassFail;
      toast({
        title: `${capitalizeFirstLetter(
          intl.formatMessage(title, {
            type: intl.formatMessage({
              defaultMessage: 'mật khẩu',
              id: 'components.container.accounts.components.UpdatePassword.password',
            }),
          }),
        )}`,
        type: 'error',
        options: {
          position: 'top-center',
        },
      });
      onClose?.();
    }
  };

  const handleClose = () => {
    if (isChanged) {
      setIsConfirmUpdatePass(true);
    } else {
      onClose?.();
    }
  };

  return (
    <>
      <Form
        name="updatePassForm"
        form={form}
        onFinish={handleSubmit}
        initialValues={{
          password: '',
          confirmPassword: '',
        }}
        onFieldsChange={(_, allFields) => {
          const isValid = allFields.some((field) => field.touched);
          setIsChanged?.(isValid);
        }}
      >
        <HeaderModal
          title={
            <FormattedMessage
              defaultMessage="Đổi mật khẩu"
              id="components.container.accounts.components.AccountForm.changePassword"
            />
          }
          buttonForm={{
            content: (
              <FormattedMessage
                defaultMessage="Cập nhật"
                id="components.container.authorization.components.BaseFormData.**********"
              />
            ),
            type: 'submit',
            isPending: isPending,
          }}
          onClose={handleClose}
        />
        <div className="flex p-4 flex-col items-start self-stretch gap-4 bg-gray-100 rounded-b-xl max-h-[calc(100vh_-_164px)] overflow-auto">
          <Alert variant="warning" isVisible={true}>
            <div className="flex gap-1.5 flex-col">
              <div className="flex gap-2 flex-1">
                <IconCommon name="Warning" color="#D97706" size={20} />
                <div className="text-base font-semibold">
                  <FormattedMessage
                    defaultMessage="Lưu ý"
                    id="components.container.accounts.components.UpdatePassword.notice"
                  />
                </div>
              </div>
              <FormattedMessage
                defaultMessage="Việc thay đổi mật khẩu sẽ ảnh hưởng đến việc sử dụng của chủ tài khoản này. Hệ thống sẽ đăng xuất tài khoản này ra khỏi các thiết bị liên quan. Vui lòng thông báo đến chủ tài khoản để tránh các vấn đề phát sinh."
                id="components.container.accounts.components.AccountForm.changingPasswordWillAffectAccount"
              />
            </div>
          </Alert>
          <div className="flex flex-col p-4 self-stretch w-full bg-white gap-4 rounded-xl">
            <div className="flex py-4 pr-6 pl-4 justify-center items-center self-stretch gap-2.5 rounded-xl border border-gray-200">
              <div className="flex items-center gap-3 flex-1 min-w-0">
                <Image
                  src={urlAvatar}
                  alt="logo-avt"
                  width={44}
                  height={44}
                  unoptimized
                  priority
                  className="cursor-pointer flex-shrink-0"
                />
                <div className="flex flex-col justify-center items-start gap-0.5 flex-1 min-w-0">
                  <div className="flex flex-row items-center w-full">
                    <OverflowTooltip
                      text={`${dataAccount?.username[locale]!} ${dataAccount?.givenName[locale]!}`}
                      className="w-full text-base font-medium text-gray-800"
                    />
                  </div>
                  <div className="flex flex-row items-center w-full">
                    <OverflowTooltip text={dataAccount?.email} className="w-full text-xs font-normal" />
                  </div>
                </div>
              </div>
              <Badge className="flex-shrink-0" variant={dataAccount?.isActive ? 'default' : 'secondary'}>
                {dataAccount?.isActive
                  ? intl.formatMessage(messageStatusCommon.activate)
                  : intl.formatMessage(messageStatusCommon.disable)}
              </Badge>
            </div>
            <ShowStrengthPassword form={form} isOpenChangePass={isOpenChangePass} />
          </div>
        </div>
      </Form>
      <Dialog open={isConfirmUpdatePass}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="max-w-[350px] p-0">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onCancel={() => {
                setIsConfirmUpdatePass(false);
              }}
              onSubmit={() => {
                onClose?.();
                setIsChanged(false);
              }}
              heading={intl.formatMessage({
                defaultMessage: 'Hủy đổi mật khẩu',
                id: 'components.container.accounts.components.AccountAction.cancelChangePassAccount',
              })}
              description={intl.formatMessage({
                defaultMessage:
                  'Bạn có chắc muốn hủy đổi mật khẩu cho tài khoản này không? Các thông tin mà bạn vừa nhập sẽ không được lưu lại.',
                id: 'components.container.accounts.components.AccountAction.areYouSureCancelChangePassAccount',
              })}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
};

export { UpdatePassword };
