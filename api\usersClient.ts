import { BACKEND_API_ENDPOINT } from '@/constants';
import QueryString from 'qs';
import apiPathName from './apiPathName';
import { BaseClient } from './baseClient';
import { GroupUser, Roles, Users } from './dataType';
import { DataResponse, DataWithPagination, FilterOptions, PaginationOptions, SortValue } from './type';

export type updatePasswordProfileType = {
  userLogtoId?: string;
  oldPassword?: string;
  newPassword: string;
};
export class UsersClient extends BaseClient<Users> {
  moduleName = 'UsersClient';
  constructor() {
    super(BACKEND_API_ENDPOINT + apiPathName.user.root);
  }

  find(
    filter?: FilterOptions<Pick<Users, 'role_id' | 'isActive' | 'groups'>>,
    pagination?: PaginationOptions,
    sort?: SortValue<Pick<Users, 'username' | 'createdAt'>>,
  ): Promise<DataResponse<DataWithPagination<Users>>> {
    const queryString = QueryString.stringify({
      filter,
      pagination,
      sort,
    });
    return this.api.get(`?${queryString}`);
  }

  override getById(id: string | number): Promise<DataResponse<Users>> {
    return super.getById(id) as Promise<DataResponse<Users>>;
  }

  getAll(): Promise<Users[]> {
    return this.api.get('/all');
  }

  updateUserState(id: string | undefined): Promise<Users> {
    return this.api.patch(`/${id}/suspend`);
  }

  getRolesByUser(userId: string): Promise<DataResponse<Roles[]>> {
    return this.api.get(`roles/${userId}`);
  }

  deleteUserRole(userId: string | number, roleId: string | number): Promise<Users> {
    return this.api.delete(`/${userId}/roles/${roleId}`);
  }

  removeUserFromGroup(groupId: string, payload: Record<string, any>): Promise<DataResponse<Users>> {
    return this.api.post(`/${groupId}/remove-user`, { userId: payload.id });
  }
  updateGroupUser(id: string, payload: Record<string, any>): Promise<DataResponse<GroupUser>> {
    return this.api.put(`/${id}/update-groupUser`, payload);
  }

  updatePassword(data: { userLogtoId: string; password: string }): Promise<DataResponse<Users>> {
    return this.api.post('/update-password', data);
  }

  async getProfile(): Promise<DataResponse<Users>> {
    return this.api.get(apiPathName.user.me) as Promise<DataResponse<Users>>;
  }

  async updateProfile(data: Partial<Users>): Promise<DataResponse<Users>> {
    return this.api.put(apiPathName.user.updateProfile, data) as Promise<DataResponse<Users>>;
  }

  async updatePasswordProfile(data: updatePasswordProfileType) {
    return this.api.post(`${apiPathName.user.updateProfile}/password`, data);
  }
}
