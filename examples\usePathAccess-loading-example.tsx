import React, { useState, useEffect } from 'react';
import { usePathAccess } from '../hook/usePathAccess';
import { useRouter } from 'next/router';

// Component hiển thị loading state cho từng path
const PathLoadingIndicator: React.FC<{ path: string }> = ({ path }) => {
  const { isPathLoading } = usePathAccess();
  
  if (isPathLoading(path)) {
    return (
      <span style={{ 
        marginLeft: '8px', 
        color: '#1890ff',
        fontSize: '12px'
      }}>
        🔄 Đang kiểm tra...
      </span>
    );
  }
  
  return null;
};

// Component chính để demo loading functionality
export const PathAccessLoadingExample: React.FC = () => {
  const router = useRouter();
  const {
    loading,
    canAccess,
    canAccessWithLoading,
    canAccessMultiple,
    isPathLoading,
    pathLoadingState,
    accessiblePaths,
  } = usePathAccess();

  const [singlePathResult, setSinglePathResult] = useState<boolean | null>(null);
  const [multiplePathResults, setMultiplePathResults] = useState<Map<string, boolean>>(new Map());
  const [testPaths] = useState([
    '/accounts',
    '/accounts/authorization',
    '/settings',
    '/settings/attachment',
    '/monitoring-station',
  ]);

  // Test single path with loading
  const handleTestSinglePath = async (path: string) => {
    setSinglePathResult(null);
    console.log(`🔍 Bắt đầu kiểm tra quyền cho: ${path}`);
    
    try {
      const result = await canAccessWithLoading(path, 1000); // 1 second delay
      setSinglePathResult(result);
      console.log(`✅ Kết quả cho ${path}:`, result);
    } catch (error) {
      console.error('❌ Lỗi khi kiểm tra quyền:', error);
    }
  };

  // Test multiple paths with loading
  const handleTestMultiplePaths = async () => {
    setMultiplePathResults(new Map());
    console.log('🔍 Bắt đầu kiểm tra quyền cho nhiều đường dẫn...');
    
    try {
      const results = await canAccessMultiple(testPaths, 800); // 800ms delay
      setMultiplePathResults(results);
      console.log('✅ Kết quả cho nhiều đường dẫn:', results);
    } catch (error) {
      console.error('❌ Lỗi khi kiểm tra quyền nhiều đường dẫn:', error);
    }
  };

  // Test immediate access (no loading)
  const handleTestImmediateAccess = (path: string) => {
    const result = canAccess(path);
    console.log(`⚡ Kiểm tra ngay lập tức ${path}:`, result);
  };

  if (loading) {
    return <div>🔄 Đang tải quyền truy cập...</div>;
  }

  return (
    <div style={{ padding: '20px', maxWidth: '800px' }}>
      <h2>🔐 Demo Loading States cho Path Access</h2>
      
      {/* Current loading states */}
      <div style={{ marginBottom: '20px', padding: '15px', backgroundColor: '#f5f5f5', borderRadius: '8px' }}>
        <h3>📊 Trạng thái Loading hiện tại:</h3>
        {pathLoadingState.size > 0 ? (
          <ul>
            {Array.from(pathLoadingState.entries()).map(([path, isLoading]) => (
              <li key={path} style={{ margin: '5px 0' }}>
                <strong>{path}</strong>: {isLoading ? '🔄 Đang xử lý...' : '✅ Hoàn thành'}
              </li>
            ))}
          </ul>
        ) : (
          <p style={{ color: '#666' }}>Không có path nào đang được xử lý</p>
        )}
      </div>

      {/* Single path testing */}
      <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #d9d9d9', borderRadius: '8px' }}>
        <h3>🎯 Kiểm tra đơn lẻ (với loading)</h3>
        <div style={{ marginBottom: '10px' }}>
          {testPaths.map((path) => (
            <button
              key={path}
              onClick={() => handleTestSinglePath(path)}
              disabled={isPathLoading(path)}
              style={{
                margin: '5px',
                padding: '8px 12px',
                backgroundColor: isPathLoading(path) ? '#f0f0f0' : '#1890ff',
                color: isPathLoading(path) ? '#999' : 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: isPathLoading(path) ? 'not-allowed' : 'pointer',
              }}
            >
              {path}
              <PathLoadingIndicator path={path} />
            </button>
          ))}
        </div>
        
        {singlePathResult !== null && (
          <div style={{ 
            padding: '10px', 
            backgroundColor: singlePathResult ? '#f6ffed' : '#fff2f0',
            border: `1px solid ${singlePathResult ? '#b7eb8f' : '#ffccc7'}`,
            borderRadius: '4px'
          }}>
            <strong>Kết quả:</strong> {singlePathResult ? '✅ Có quyền truy cập' : '❌ Không có quyền'}
          </div>
        )}
      </div>

      {/* Multiple paths testing */}
      <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #d9d9d9', borderRadius: '8px' }}>
        <h3>📋 Kiểm tra nhiều đường dẫn (batch processing)</h3>
        <button
          onClick={handleTestMultiplePaths}
          disabled={Array.from(pathLoadingState.values()).some(loading => loading)}
          style={{
            padding: '10px 20px',
            backgroundColor: '#52c41a',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer',
            marginBottom: '15px',
          }}
        >
          🚀 Kiểm tra tất cả đường dẫn
        </button>

        {multiplePathResults.size > 0 && (
          <div>
            <h4>📈 Kết quả batch processing:</h4>
            <ul>
              {Array.from(multiplePathResults.entries()).map(([path, hasAccess]) => (
                <li key={path} style={{ margin: '5px 0' }}>
                  <strong>{path}</strong>: {hasAccess ? '✅ Có quyền' : '❌ Không có quyền'}
                </li>
              ))}
            </ul>
          </div>
        )}
      </div>

      {/* Immediate access testing */}
      <div style={{ marginBottom: '20px', padding: '15px', border: '1px solid #d9d9d9', borderRadius: '8px' }}>
        <h3>⚡ Kiểm tra ngay lập tức (không loading)</h3>
        <div>
          {testPaths.map((path) => (
            <button
              key={path}
              onClick={() => handleTestImmediateAccess(path)}
              style={{
                margin: '5px',
                padding: '8px 12px',
                backgroundColor: '#722ed1',
                color: 'white',
                border: 'none',
                borderRadius: '4px',
                cursor: 'pointer',
              }}
            >
              {path}
            </button>
          ))}
        </div>
      </div>

      {/* Accessible paths display */}
      <div style={{ padding: '15px', backgroundColor: '#f0f9ff', borderRadius: '8px' }}>
        <h3>📝 Danh sách đường dẫn có quyền:</h3>
        <ul>
          {accessiblePaths.map((path) => (
            <li key={path} style={{ margin: '5px 0' }}>
              <button
                onClick={() => router.push(path)}
                style={{
                  backgroundColor: 'transparent',
                  border: '1px solid #1890ff',
                  color: '#1890ff',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  cursor: 'pointer',
                  marginRight: '10px',
                }}
              >
                {path}
              </button>
              <PathLoadingIndicator path={path} />
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
};

// Hook tùy chỉnh để sử dụng loading states
export const usePathAccessWithLoading = () => {
  const pathAccess = usePathAccess();
  
  // Helper function để kiểm tra nhiều paths và theo dõi loading
  const checkMultiplePathsWithStatus = async (paths: string[]) => {
    const results = await pathAccess.canAccessMultiple(paths);
    return {
      results,
      isAnyLoading: paths.some(path => pathAccess.isPathLoading(path)),
      loadingPaths: paths.filter(path => pathAccess.isPathLoading(path)),
    };
  };

  return {
    ...pathAccess,
    checkMultiplePathsWithStatus,
  };
};
