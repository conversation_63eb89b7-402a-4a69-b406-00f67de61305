'use client';

import { AlertModal } from '@/components/ui/modal/AlertModal';
import { FormattedMessage } from 'react-intl';

type TypeConfirmChangeLanguage = {
  confirmChangeLanguage?: boolean;
  closeChangePass?: boolean;
};

type LanguageValue = {
  vi: string;
  en: string;
};

type ConfirmChangeLanguageProps = {
  data?: Record<string, any>;
  onClose?: () => void;
  onCloseModalLang?: () => void;
  onSubmit?: (value: LanguageValue) => void;
  type?: TypeConfirmChangeLanguage;
};

export function ConfirmChangeLanguage({
  onClose,
  onSubmit,
  data,
  onCloseModalLang,
}: ConfirmChangeLanguageProps) {
  const valueSuggest = Object.values(data || {}).find((value) => value !== '');

  const title = (
    <FormattedMessage
      defaultMessage="Một số ngôn ngữ còn trống"
      description=""
      id="components.container.accounts.components.ConfirmChangeLanguage.**********"
    />
  );
  const description = (
    <FormattedMessage
      defaultMessage='<span>Một số ngôn ngữ bạn vẫn chưa thiết lập nội dung. Bạn có muốn thiết lập nhanh với nội dung là "<strong>{valueSuggest}</strong>" cho các ngôn ngữ đó không?</span>'
      id="components.container.accounts.components.ConfirmChangeLanguage.**********"
      values={{
        valueSuggest,
        strong: (chunks: React.ReactNode) => <strong>{chunks}</strong>,
        span: (chunks: React.ReactNode) => <span>{chunks}</span>,
      }}
    />
  );

  const handleSubmit = () => {
    const result = fillEmptyValues(data || {});
    onSubmit?.(result);
    onCloseModalLang?.();
    onClose?.();
  };

  const fillEmptyValues = (obj: { [key: string]: string }): LanguageValue => {
    const nonEmptyValue = Object.values(obj).find((value) => value !== '');

    if (nonEmptyValue) {
      for (let key in obj) {
        if (obj[key] === '') {
          obj[key] = nonEmptyValue;
        }
      }
    }

    return { vi: obj.vi || '', en: obj.en || '' };
  };
  return (
    <AlertModal
      title={title}
      description={description}
      cancel={{
        title: (
          <FormattedMessage
            defaultMessage="Hủy bỏ"
            id="components.commons.ContentChangedBase.780985299"
          />
        ),
        onClick: () => onClose?.(),
      }}
      confirm={{
        title: (
          <FormattedMessage
            defaultMessage="Xác nhận"
            id="components.commons.ContentChangedBase.559188735"
          />
        ),
        onClick: handleSubmit,
      }}
    />
  );
}
