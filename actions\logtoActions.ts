'use server';

import { BACKEND_API_ENDPOINT, BASE_URL, WEB_CORE_URL } from '@/constants';
import { urlPathName } from '@/constants/urlPathName';
import axios, { AxiosError } from 'axios';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
import { setCookie } from './cookieActions';

const callApi = axios.create({
  withCredentials: true,
  headers: {
    'Content-Type': 'application/json',
  },
});

export const signOut = async () => {
  return await callApi.get(`${WEB_CORE_URL}/api/logto/sign-out`);
};

const optionsCookie = {
  maxAge: 3600 * 24,
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
};

export const refreshToken = async (): Promise<boolean> => {
  try {
    const cookieStorage = await cookies();
    const response = await callApi.get<{
      accessToken: string;
      organizationToken?: string;
      organizationId?: string;
    }>(`${WEB_CORE_URL}/api/logto/refresh-token`, {
      withCredentials: true,
      headers: {
        'Content-Type': 'application/json',
        cookie: cookieStorage.toString(),
      },
    });

    const { accessToken, organizationToken, organizationId } = response.data;

    if (!accessToken || !organizationToken) {
      return false;
    }

    await Promise.all([
      setCookie('token:base', accessToken, optionsCookie),
      organizationToken && organizationId
        ? setCookie(`token:org:${organizationId}`, organizationToken, optionsCookie)
        : Promise.resolve(true),
    ]).then(() => {
      console.log('set cookie successfully');
    });

    return true;
  } catch (error: any) {
    console.log(error.message);
    return false;
  }
};

export const authorizeAccess = async (request: NextRequest) => {
  return await callApi.get(`${BACKEND_API_ENDPOINT}/auth/auth-access`, {
    withCredentials: true,
    headers: {
      'Content-Type': 'application/json',
      'x-api-key-client': 'config',
      cookie: request.cookies.toString(),
    },
  });
};

export const authorizeLogtoAccess = async (request: NextRequest) => {
  return await callApi.get<{
    isAuthenticated: boolean;
  }>(`${WEB_CORE_URL}/api/logto/check-access-logto`, {
    withCredentials: true,
    headers: {
      'Content-Type': 'application/json',
      'x-api-key-client': 'config',
      cookie: request.cookies.toString(),
    },
  });
};

export const handlerError = async (request: NextRequest, error: AxiosError) => {
  const status = error?.status || error?.response?.status;
  if (status === 401) {
    const result = await refreshToken();
    if (!result) {
      return await signOut().then(() => {
        return NextResponse.redirect(`${WEB_CORE_URL}/auth?redirect_to=${BASE_URL}`);
      });
    }
    const cookie = await cookies();
    const cookieString = cookie.toString();
    const config = error.config;
    config!['headers']['cookie'] = cookieString;
    const apiResponse = await callApi.request(config!);
    return NextResponse.json(apiResponse.data, { status: apiResponse.status });
  }

  if (status === 403) {
    const data = error.response?.data as { code: string };
    if (data.code === 'NOT_FOUND_SESSION') {
      return await signOut().then(() => {
        return NextResponse.redirect(`${WEB_CORE_URL}/auth?redirect_to=${BASE_URL}`);
      });
    }
    return NextResponse.redirect(`${WEB_CORE_URL}${urlPathName.ACCESS_DENIED}`);
  }

  return NextResponse.json(error.response, { status: error.status });
};
