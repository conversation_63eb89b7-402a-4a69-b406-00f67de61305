import { usePathAccess } from '@/hook/usePathAccess';
import { appStories } from '@/store';
import { renderHook } from '@testing-library/react';

jest.mock('@/store');
const mockAppStories = appStories as jest.MockedFunction<typeof appStories>;

describe('usePathAccess - Real World Scenarios', () => {
  describe('👤 User Role: Admin (Full Access)', () => {
    beforeEach(() => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'general:read' },
          { keyName: 'users:read' },
          { keyName: 'group:read' },
          { keyName: 'roles:read' },
          { keyName: 'setting:read' },
          { keyName: 'monitoring:read' },
        ],
      });
    });

    it('should have access to all paths', () => {
      const { result } = renderHook(() => usePathAccess());

      const testPaths = [
        '/',
        '/accounts',
        '/accounts/group',
        '/accounts/authorization',
        '/settings',
        '/settings/attachment',
        '/monitoring-station',
      ];

      testPaths.forEach((path) => {
        expect(result.current.canAccess(path)).toBe(true);
      });
    });

    it('should not need any redirects', () => {
      const { result } = renderHook(() => usePathAccess());

      const testPaths = ['/', '/accounts', '/accounts/authorization', '/settings/digital-signature'];

      testPaths.forEach((path) => {
        expect(result.current.redirectIfUnauthorized(path)).toBe(null);
      });
    });

    it('should get correct module defaults', () => {
      const { result } = renderHook(() => usePathAccess());

      expect(result.current.getModuleDefaultPath('general')).toBe('/');
      expect(result.current.getModuleDefaultPath('accounts')).toBe('/accounts');
      expect(result.current.getModuleDefaultPath('settings')).toBe('/settings');
      expect(result.current.getModuleDefaultPath('monitoring-station')).toBe('/monitoring-station');
    });
  });

  describe('👥 User Role: Account Manager (Accounts Only)', () => {
    beforeEach(() => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }, { keyName: 'group:read' }, { keyName: 'role:read' }],
      });
    });

    it('should have access only to accounts module', () => {
      const { result } = renderHook(() => usePathAccess());

      // Should have access
      expect(result.current.canAccess('/accounts')).toBe(true);
      expect(result.current.canAccess('/accounts/group')).toBe(true);
      expect(result.current.canAccess('/accounts/authorization')).toBe(true);

      // Should not have access
      expect(result.current.canAccess('/')).toBe(false);
      expect(result.current.canAccess('/settings')).toBe(false);
      expect(result.current.canAccess('/monitoring-station')).toBe(false);
    });

    it('should redirect non-accounts paths to accounts', () => {
      const { result } = renderHook(() => usePathAccess());

      expect(result.current.redirectIfUnauthorized('/')).toBe('/accounts');
      expect(result.current.redirectIfUnauthorized('/settings')).toBe('/accounts');
      expect(result.current.redirectIfUnauthorized('/monitoring-station')).toBe('/accounts');
    });

    it('should navigate within accounts module correctly', () => {
      const { result } = renderHook(() => usePathAccess());

      expect(result.current.navigateToModule('accounts')).toBe('/accounts');
      expect(result.current.navigateToModule('settings')).toBe('/accounts'); // Fallback
    });
  });

  describe('⚙️ User Role: Settings Manager (Settings Only)', () => {
    beforeEach(() => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'setting:read' }],
      });
    });

    it('should have access only to settings module', () => {
      const { result } = renderHook(() => usePathAccess());

      // Should have access
      expect(result.current.canAccess('/settings')).toBe(true);
      expect(result.current.canAccess('/settings/attachment')).toBe(true);
      expect(result.current.canAccess('/settings/storage-capacity')).toBe(true);

      // Should not have access
      expect(result.current.canAccess('/accounts')).toBe(false);
      expect(result.current.canAccess('/monitoring-station')).toBe(false);
    });

    it('should redirect all non-settings paths to settings', () => {
      const { result } = renderHook(() => usePathAccess());

      expect(result.current.redirectIfUnauthorized('/accounts')).toBe('/settings');
      expect(result.current.redirectIfUnauthorized('/monitoring-station')).toBe('/settings');
    });
  });

  describe('👀 User Role: Viewer (Limited Access)', () => {
    beforeEach(() => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }, { keyName: 'group:read' }],
      });
    });

    it('should have limited access within accounts', () => {
      const { result } = renderHook(() => usePathAccess());

      // Should have access
      expect(result.current.canAccess('/accounts')).toBe(true);
      expect(result.current.canAccess('/accounts/group')).toBe(true);

      // Should not have access
      expect(result.current.canAccess('/accounts/authorization')).toBe(false);
      expect(result.current.canAccess('/settings')).toBe(false);
    });

    it('should redirect unauthorized accounts paths to accessible ones', () => {
      const { result } = renderHook(() => usePathAccess());

      // Should redirect to accounts root or group
      const redirectPath = result.current.redirectIfUnauthorized('/accounts/authorization');
      expect(['/accounts', '/accounts/group']).toContain(redirectPath);
    });

    it('should find alternative paths in same module', () => {
      const { result } = renderHook(() => usePathAccess());

      const alternative = result.current.findAlternativePathInModule('/accounts/authorization');
      expect(['/accounts', '/accounts/group']).toContain(alternative);
    });
  });

  describe('🚫 User Role: No Permissions', () => {
    beforeEach(() => {
      mockAppStories.mockReturnValue({
        userPermissions: [],
      });
    });

    it('should be marked as unauthorized', () => {
      const { result } = renderHook(() => usePathAccess());

      expect(result.current.unauthorized).toBe(true);
      expect(result.current.accessiblePaths).toHaveLength(0);
    });

    it('should redirect all paths to NOT_PERMISSION', () => {
      const { result } = renderHook(() => usePathAccess());

      const testPaths = ['/', '/accounts', '/settings', '/monitoring-station'];

      testPaths.forEach((path) => {
        expect(result.current.redirectIfUnauthorized(path)).toBe('/not-permission');
      });
    });

    it('should still allow access to public paths', () => {
      const { result } = renderHook(() => usePathAccess());

      expect(result.current.canAccess('/access-denied')).toBe(true);
      expect(result.current.canAccess('/profile')).toBe(true);
      expect(result.current.canAccess('/health')).toBe(true);
    });
  });

  describe('🔄 Complex Redirect Scenarios', () => {
    it('Scenario: User có quyền accounts/group nhưng truy cập accounts/authorization', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'group:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Should redirect to /accounts/group (most similar path)
      const redirectPath = result.current.redirectIfUnauthorized('/accounts/authorization');
      expect(redirectPath).toBe('/accounts/group');
    });

    it('Scenario: User có quyền settings nhưng truy cập accounts', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'setting:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Should redirect to /settings (different module)
      const redirectPath = result.current.redirectIfUnauthorized('/accounts');
      expect(redirectPath).toBe('/settings');
    });

    it('Scenario: User có nhiều quyền, truy cập path không có quyền', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }, { keyName: 'setting:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Should redirect to /accounts (higher priority in module order)
      const redirectPath = result.current.redirectIfUnauthorized('/monitoring-station');
      expect(redirectPath).toBe('/accounts');
    });
  });

  describe('🎯 Module Priority Testing', () => {
    it('should respect module priority order: general > accounts > settings > monitoring', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'setting:read' }, { keyName: 'monitoring:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Should redirect to settings (higher priority than monitoring)
      const redirectPath = result.current.redirectIfUnauthorized('/accounts');
      expect(redirectPath).toBe('/settings');
    });

    it('should prefer general module when available', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'general:read' }, { keyName: 'monitoring:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Should redirect to / (general has highest priority)
      const redirectPath = result.current.redirectIfUnauthorized('/accounts');
      expect(redirectPath).toBe('/');
    });
  });

  describe('📱 Dynamic Path Handling', () => {
    it('should handle dynamic paths with parameters', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'group:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Test various dynamic path formats
      expect(result.current.canAccess('/accounts/group/123')).toBe(true);
      expect(result.current.canAccess('/accounts/group/abc-def')).toBe(true);
      expect(result.current.canAccess('/accounts/group/user-456')).toBe(true);
    });

    it('should redirect dynamic paths correctly', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Should redirect to /accounts (user has accounts access but not group)
      const redirectPath = result.current.redirectIfUnauthorized('/accounts/group/123');
      expect(redirectPath).toBe('/accounts');
    });
  });
});
