import AccountGroupContainer from '@/components/container/group-account/AccountGroupContainer';
import { use } from 'react';

type Params = Promise<{ id: string }>;
export default function PageAccounts({ params }: { params: Params }) {
  const { id } = use(params);
  if (id === 'not-found') return <div className="flex-1 h-full w-full bg-white"></div>;
  return <AccountGroupContainer groupId={id} />;
}
