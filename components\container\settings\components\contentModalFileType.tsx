'use client';

import { FileType } from '@/api/fileConfigClient';
import { useCreateClient } from '@/api/useCreateClient';
import ContentChangedBase from '@/components/commons/ContentChangedBase';
import { HeaderModal } from '@/components/ui/modal/header-modal';
import { BASE_PATH } from '@/constants';
import Image from 'next/image';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import {
  Badge,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Form,
  FormItem,
  Input,
  Switch,
  toast,
} from 'ui-components';

interface ContentModalFileTypeProps {
  dataFileType: FileType;
  onClose: () => void;
  pendingFileType: boolean;
  isChangedData: boolean;
  setIsChangedData: Dispatch<SetStateAction<boolean>>;
  isOpenModalConfirm: boolean;
  setIsOpenModalConfirm: Dispatch<SetStateAction<boolean>>;
}

export const ContentModalFileType = ({
  dataFileType,
  onClose,
  pendingFileType,
  isChangedData,
  setIsChangedData,
  isOpenModalConfirm,
  setIsOpenModalConfirm,
}: ContentModalFileTypeProps) => {
  const intl = useIntl();
  const [form] = Form.useForm();
  const locale = intl.locale as 'vi' | 'en';

  const initialValues = useMemo(() => {
    return {
      permissions: dataFileType?.applications.map((app) => ({
        applicationId: app.application.id,
        enabled: app.enabled,
      })),
    };
  }, [dataFileType?.id]);

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues]);

  const { fileConfig } = useCreateClient();

  const appplicationActive = dataFileType.applications.filter((app) => app.enabled === true)?.length;

  const mutationOptions = {
    invalidateQueries: { enable: true },
  };

  const { mutateAsync: updateFileTypeMultipleAppAsync, isPending: pendingUpdateFileType } =
    fileConfig.updateFileTypeMultipleApp(mutationOptions);

  const handleClose = useCallback(() => {
    if (isChangedData) {
      setIsOpenModalConfirm(true);
    } else {
      onClose?.();
    }
  }, [isChangedData, onClose, setIsOpenModalConfirm]);

  const handleSubmit = async (values: Record<string, any>) => {
    try {
      const payload = {
        fileTypeId: dataFileType?.id,
        permissions: values.permissions,
      };
      await updateFileTypeMultipleAppAsync(payload);
      toast({
        title: `${intl.formatMessage({
          defaultMessage: 'Đã cập nhật thông tin định dạng thành công.',
          id: 'components.container.settings.components.contentModalFileType.updatedValueExtSuccess',
        })}`,
        type: 'success',
        options: {
          position: 'top-center',
        },
      });
      onClose?.();
      setIsChangedData(false);
    } catch (error) {
      toast({
        title: `${intl.formatMessage({
          defaultMessage: 'Không thể cập nhật thông tin định dạng. Vui lòng thử lại sau.',
          id: 'components.container.settings.components.contentModalFileType.updatedValueExtFail',
        })}`,
        type: 'error',
        options: {
          position: 'top-center',
        },
      });
    }
  };
  const sortedApps = [...dataFileType.applications].sort((a, b) => a.application.order! - b.application.order!);
  const getPermissionIndex = (applicationId: string) => {
    return initialValues.permissions.findIndex((p) => p.applicationId === applicationId);
  };
  return (
    <>
      <Form
        name="fileTypeForm"
        form={form}
        onFinish={handleSubmit}
        initialValues={initialValues}
        onFinishFailed={(err) => console.log(err)}
        onFieldsChange={(_, allFields) => {
          const isValid = allFields.some((field) => {
            const fieldName = field.name[0] as 'permissions';
            return initialValues[fieldName] !== field.value;
          });
          // setIsChangedData(isValid);
        }}
      >
        <HeaderModal
          title={
            <FormattedMessage
              defaultMessage="Thông tin định dạng"
              id="components.container.settings.components.contentModalFileType.infoExtention"
            />
          }
          buttonForm={{
            content: (
              <FormattedMessage
                defaultMessage="Cập nhật"
                id="components.container.authorization.components.BaseFormData.1416668261"
              />
            ),
            type: 'submit',
            isPending: pendingFileType || pendingUpdateFileType,
          }}
          onClose={handleClose}
        />
        <div className="bg-gray-200 p-4 rounded-b-xl">
          <div className=" flex flex-col rounded-xl gap-4">
            <div className="bg-white flex flex-col gap-4 p-4 rounded-xl">
              <div className="flex items-start gap-4">
                <div className="flex flex-col gap-2 flex-1">
                  <span className="text-sm text-gray-800 font-medium">
                    <FormattedMessage
                      defaultMessage="Tên định dạng"
                      id="components.container.settings.components.contentModalFileType.nameExtention"
                    />
                  </span>
                  <span className="text-sm text-gray-700 font-normal">{dataFileType.extensions}</span>
                </div>
                <div className="flex flex-col gap-2 flex-1">
                  <span className="text-sm text-gray-800 font-medium">
                    <FormattedMessage
                      defaultMessage="Loại định dạng"
                      id="components.container.settings.components.contentModalFileType.typeExtention"
                    />
                  </span>
                  <span className="text-sm text-gray-700 font-normal">{dataFileType.type[locale]}</span>
                </div>
              </div>
              <div className="flex flex-col items-start gap-2">
                <span className="text-sm text-gray-800 font-medium">
                  <FormattedMessage
                    defaultMessage="Mô tả định dạng"
                    id="components.container.settings.components.contentModalFileType.descriptionExtention"
                  />
                </span>
                <span className="text-sm text-gray-700 font-normal">{dataFileType.description[locale]}</span>
              </div>
            </div>

            <div className="bg-white flex flex-col gap-3 p-4 rounded-xl font-sem">
              <div className="flex flex-col gap-2">
                <div className="flex justify-between">
                  <span className="font-semibold text-gray-700">
                    <FormattedMessage
                      defaultMessage="Sản phẩm áp dụng"
                      id="components.container.settings.components.contentModalFileType.productApply"
                    />
                  </span>
                  <Badge className="shadow-none">
                    {appplicationActive}/{dataFileType.applications.length}
                  </Badge>
                </div>
              </div>
              <div className="flex flex-col border border-gray-200 rounded-xl  divide-y-[1px] divide-gray-200">
                {sortedApps.map((app) => {
                  const permIdx = getPermissionIndex(app.application.id!);
                  return (
                    <div className="flex justify-between gap-3 p-3 border-b-gray-200" key={app.id}>
                      <div className="flex flex-1 gap-2 items-center">
                        <Image
                          src={`${BASE_PATH}/images/branding/branding-${app?.application?.signature + '-active'}.svg`}
                          alt="logo"
                          width={32}
                          height={32}
                          unoptimized
                          priority
                          className="h-8 w-8 rounded-full"
                        />
                        <span className="size-sm font-normal text-gray-800">{app.application.name}</span>
                      </div>
                      <div className="flex items-center">
                        <div className="hidden">
                          <FormItem
                            name={['permissions', permIdx, 'applicationId']}
                            renderItem={({ control }) => <Input type="hidden" {...control} />}
                          />
                        </div>
                        <FormItem
                          name={['permissions', permIdx, 'enabled']}
                          renderItem={({ control }) => {
                            console.log('control value:', control.value);
                            return <Switch checked={control.value} onCheckedChange={control.onChange} />;
                          }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      </Form>
      <Dialog open={isOpenModalConfirm}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="max-w-[420px] p-0">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onCancel={() => setIsOpenModalConfirm(false)}
              onSubmit={() => {
                setIsOpenModalConfirm(false);
                onClose?.();
              }}
              heading={intl.formatMessage({
                defaultMessage: 'Hủy cập nhật giá trị',
                id: 'components.container.settings.components.contentModalType.cancelUpdatingValue',
              })}
              description={intl.formatMessage({
                defaultMessage: 'Bạn có chắc muốn hủy cập nhật giá trị? Giá trị mà bạn vừa nhập sẽ không được lưu lại.',
                id: 'components.container.settings.components.contentModalType.AreYouSureCancelUpdatingValue',
              })}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
};
