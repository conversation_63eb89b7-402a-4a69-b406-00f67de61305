'use-client';

import IconCommon from '@/components/commons/IconLayout';
import { emailRegExp } from '@/constants/regex';
import { cn } from '@/utils/tailwind';
import { useCallback, useEffect, useMemo, useState } from 'react';
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Form,
  FormItem,
  IDatePicker,
  Input,
  Spin,
  toast,
} from 'ui-components';
import { defaultCountries, parseCountry, PhoneInput } from 'react-international-phone';
import { CaretLeft, EnvelopeSimple } from '@phosphor-icons/react';
import dayjs from 'dayjs';
import { FormattedMessage, useIntl } from 'react-intl';
import { messageAlertCommon, messageErrorCommon } from '@/constants/defineMessages';
import { capitalizeFirstLetter } from '@/utils/string';
import { useCreateClient } from '@/api/useCreateClient';
import { Users } from '@/api/dataType';
import { PhoneNumberUtil } from 'google-libphonenumber';
import { useRouter } from 'next/navigation';
import InputLanguage from '@/components/ui/input-language';
import { UpdatePasswordProfile } from './UpdatePasswordProfile';
import ContentChangedBase from '@/components/commons/ContentChangedBase';
import { WEB_CORE_URL } from '@/constants';
import { deleteCookie, getCookie } from '@/actions/cookieActions';
import { usePreventNavigation } from '@/hook/usePreventNavigation';
interface AccountFormProps {
  dataAccount?: Users | undefined | null;
}

export function ProfileForm({ dataAccount }: AccountFormProps) {
  const [form] = Form.useForm();
  const intl = useIntl();

  const [modalState, setModalState] = useState({
    isChangedFormDataProfile: false,
    isOpenConfirm: false,
    isOpenModalChangePass: false,
    isChangeFieldPassword: false,
    isConfirmChangePassSuccess: false,
  });

  usePreventNavigation({
    isDirty: modalState.isChangedFormDataProfile || modalState.isChangeFieldPassword,
  });

  const { auth, users } = useCreateClient();
  const isUpdate = Boolean(dataAccount?.id);
  const router = useRouter();

  const { data: user } = auth.getProfile();

  const initValue = {
    givenName: {
      vi: '',
      en: '',
    },
    username: {
      vi: '',
      en: '',
    },
    description: {
      vi: '',
      en: '',
    },
    email: '',
    password: '',
    phone: '',
    birthdate: (dataAccount?.birthdate ?? user?.data?.birthdate === null) ? undefined : user?.data?.birthdate,
  };

  const initialValues = useMemo(() => {
    if (dataAccount && dataAccount?.id) {
      return {
        givenName: {
          vi: dataAccount?.givenName?.vi,
          en: dataAccount?.givenName?.en,
        },
        username: {
          vi: dataAccount?.username?.vi,
          en: dataAccount?.username?.en,
        },
        phone: dataAccount?.phone === '' || dataAccount?.phone === null ? '+84' : dataAccount?.phone,
        email: dataAccount?.email,
        description: {
          vi: dataAccount?.description?.vi,
          en: dataAccount?.description?.en,
        },
        birthdate: dataAccount?.birthdate ? dataAccount?.birthdate : undefined,
      };
    }
    return initValue;
  }, [dataAccount?.id]);

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues]);

  // UseEffect handle open modal change password
  useEffect(() => {
    const fetchData = async () => {
      const checkCookieValue = await getCookie('openModalChangePass');
      if (checkCookieValue === 'true') {
        await deleteCookie('openModalChangePass');
        setModalState((prev) => ({
          ...prev,
          isOpenModalChangePass: true,
        }));
      }
    };

    fetchData();
  }, []);

  // UseEffect handle sign-out after change password success
  useEffect(() => {
    const fetchData = async () => {
      const checkCookieValue = await getCookie('beforeReload');
      if (checkCookieValue === 'true') {
        await deleteCookie('beforeReload');
        window.location.replace(`${WEB_CORE_URL}/api/logto/sign-out`);
      }
    };

    fetchData();
  }, []);

  const mutationOptions = {
    invalidateQueries: { enable: true },
  };

  const { mutateAsync: updateUserAsync, isPending: isPendingUpdate } = users.updateProfile(mutationOptions);

  const phoneUtil = PhoneNumberUtil.getInstance();

  const isPhoneValid = (phone: string) => {
    const countries = defaultCountries.map((country) => {
      const { dialCode } = parseCountry(country);
      return dialCode;
    });

    try {
      if (phone.length === 5) return true;
      return phoneUtil.isValidNumber(phoneUtil.parseAndKeepRawInput(phone));
    } catch (error) {
      const value = phone.startsWith('+') ? phone.slice(1) : phone;
      // Tách mã vùng và số liền kề
      let regionCode = '';
      for (let i = 1; i <= value.length; i++) {
        const prefix = value.slice(0, i);
        if (countries.includes(prefix)) {
          regionCode = prefix;
          break;
        }
      }
      const nextDigit = value[regionCode.length];

      if (nextDigit === '0') {
        return false;
      }
      return undefined;
    }
  };

  const handleSubmit = async (value: Record<string, any>) => {
    try {
      const body = {
        email: value.email,
        password: value.password,
        givenName: value.givenName,
        username: value.username,
        phone: value.phone === '+84' || value.phone === '' ? null : value.phone,
        description: value.description,
        birthdate: value.birthdate,
      };
      await updateUserAsync(body);
      const title = isUpdate ? messageAlertCommon.updateSuccess : messageAlertCommon.createSuccess;
      toast({
        title: `${capitalizeFirstLetter(
          intl.formatMessage(title, {
            type: intl.formatMessage({
              defaultMessage: 'tài khoản',
              id: 'components.container.accounts.components.AccountForm.account',
            }),
          }),
        )}`,
        type: 'success',
        options: {
          position: 'top-center',
        },
      });
      // Sau khi test mở comment
      router.back();
    } catch (error: any) {
      const { email, phone }: { email: boolean; phone: boolean } = error;
      if (phone || email) {
        if (isUpdate) {
          const fieldPhone = phone
            ? {
                name: 'phone',
                errors: [
                  intl.formatMessage({
                    defaultMessage: 'Số điện thoại đã tồn tại trong hệ thống.',
                    id: 'components.container.accounts.components.AccountForm.phoneAlreadyExists',
                  }),
                ],
              }
            : null;
          const error: any = [fieldPhone].filter(Boolean);
          return form.setFields(error);
        } else {
          const fieldPhone = phone
            ? {
                name: 'phone',
                errors: [
                  intl.formatMessage({
                    defaultMessage: 'Số điện thoại đã tồn tại trong hệ thống.',
                    id: 'components.container.accounts.components.AccountForm.phoneAlreadyExists',
                  }),
                ],
              }
            : null;
          const fieldEmail = email
            ? {
                name: 'email',
                errors: [
                  intl.formatMessage({
                    defaultMessage: 'Email đã tồn tại trong hệ thống.',
                    id: 'components.container.accounts.components.AccountForm.emailAlreadyExists',
                  }),
                ],
              }
            : null;
          const error: any = [fieldPhone, fieldEmail].filter(Boolean);
          return form.setFields(error);
        }
      } else {
        const title = isUpdate ? messageAlertCommon.updateFail : messageAlertCommon.createFail;
        toast({
          title: `${capitalizeFirstLetter(
            intl.formatMessage(title, {
              type: intl.formatMessage({
                defaultMessage: 'tài khoản',
                id: 'components.container.accounts.components.AccountForm.account',
              }),
            }),
          )}`,
          type: 'error',
          options: {
            position: 'top-center',
          },
        });
        // onClose?.();
      }
    }
  };

  const onOpenChangeModalPass = useCallback(
    (open: boolean) => {
      if (modalState.isChangeFieldPassword) {
        setModalState((prev) => ({
          ...prev,
          isOpenConfirm: true,
        }));
      } else {
        setModalState((prev) => ({
          ...prev,
          isOpenModalChangePass: open,
        }));
      }
    },
    [modalState.isChangeFieldPassword],
  );

  return (
    <Spin loading={isPendingUpdate}>
      <Form
        name="profileForm"
        form={form}
        onFinish={handleSubmit}
        initialValues={initialValues}
        onFinishFailed={(err) => console.log(err)}
        onFieldsChange={(_, allFields) => {
          const isValid = allFields.some((field, index) => {
            const fieldName = field.name[0] as 'username' | 'givenName' | 'description';

            return initialValues[fieldName]?.vi !== field.value?.vi;
          });
          setModalState((prev) => ({
            ...prev,
            isChangedFormDataProfile: isValid,
          }));
        }}
        className="h-full"
      >
        <div
          className={cn(
            'h-[64px] w-full bg-white flex-shrink-0 px-5 py-3 flex flex-row justify-between items-center gap-5 border-b border-gray-200',
          )}
        >
          <div className="flex flex-row gap-3 items-center">
            <Button
              icon
              variant="neutral"
              className="bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-100"
              onClick={() => router.back()}
              type="button"
            >
              <CaretLeft className="text-current" size={20} />
            </Button>
            <span className="text-lg text-gray-800 font-medium">
              <FormattedMessage
                defaultMessage="Thông tin tài khoản"
                id="components.container.accounts.components.AccountForm.af223b70"
              />
            </span>
          </div>
          <Button className="flex flex-row gap-2 text-white">
            <span className="font-medium text-current">
              <FormattedMessage
                defaultMessage="Cập nhật"
                id="components.container.authorization.components.BaseFormData.**********"
              />
            </span>
          </Button>
        </div>
        {/* Body Modal */}
        <div className="flex w-full h-full">
          <div
            className={cn(
              'p-5 flex self-stretch bg-gray-100 rounded-b-xl  flex-col max-h-[calc(100vh_-_65px)] max-w-[1300px] m-[0_auto] gap-5 h-full overflow-auto',
            )}
          >
            <div className={cn('flex flex-1 flex-col gap-4 p-4 rounded-xl bg-white h-full min-w-0 max-w-[650px]')}>
              <span className="text-base font-semibold text-gray-700">
                <FormattedMessage
                  defaultMessage="Thông tin tài khoản"
                  id="components.container.accounts.components.AccountForm.af223b70"
                />
              </span>
              <div className="flex flex-row gap-4 min-w-0">
                <div className="flex flex-col items-start gap-2 flex-grow flex-shink-0 basis-0">
                  <div className="items-center space-x-2">
                    <span className="text-sm text-gray-800 font-medium">
                      <FormattedMessage
                        defaultMessage="Họ và tên đệm"
                        id="components.container.accounts.components.AccountForm.ab9a0cc1"
                      />
                    </span>
                    <span className="text-sm text-red-500 font-medium">*</span>
                  </div>
                  <Form.Field
                    name="givenName"
                    rules={[
                      {
                        validator(rule, value, callback) {
                          if (!(value.vi.length > 0)) {
                            return Promise.reject(
                              new Error(
                                intl.formatMessage(messageErrorCommon.fieldRequired, {
                                  field: intl.formatMessage({
                                    defaultMessage: 'Họ và tên đệm',
                                    id: 'components.container.accounts.components.AccountForm.ab9a0cc1',
                                  }),
                                }),
                              ),
                            );
                          }

                          if (value.vi.length > 64) {
                            return Promise.reject(
                              new Error(
                                intl.formatMessage(messageErrorCommon.fieldMax, {
                                  max: 64,
                                }),
                              ),
                            );
                          }

                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    {({ value, onChange }, meta) => (
                      <InputLanguage
                        placeholder={intl.formatMessage({
                          defaultMessage: 'Nhập họ và tên đệm',
                          id: 'components.container.accounts.components.AccountForm.fb1a03fd',
                        })}
                        name="givenName"
                        meta={meta}
                        value={value}
                        onChange={onChange}
                        maxLenght={64}
                      />
                    )}
                  </Form.Field>
                </div>
                <div className="flex flex-col items-start gap-2 flex-grow flex-shink-0 basis-0">
                  <div className="items-center space-x-2">
                    <span className="text-sm text-gray-800 font-medium">
                      <FormattedMessage
                        defaultMessage="Tên tài khoản"
                        id="components.container.accounts.components.AccountForm.5d7d978b"
                      />
                    </span>
                    <span className="text-sm text-red-500 font-medium">*</span>
                  </div>
                  <Form.Field
                    name="username"
                    rules={[
                      {
                        validator(rule, value, callback) {
                          if (!(value.vi.length > 0)) {
                            return Promise.reject(
                              new Error(
                                intl.formatMessage(messageErrorCommon.fieldRequired, {
                                  field: intl.formatMessage({
                                    defaultMessage: 'Tên tài khoản',
                                    id: 'components.container.accounts.components.AccountForm.5d7d978b',
                                  }),
                                }),
                              ),
                            );
                          }

                          if (value.vi.length > 64) {
                            return Promise.reject(
                              new Error(
                                intl.formatMessage(messageErrorCommon.fieldMax, {
                                  max: 64,
                                }),
                              ),
                            );
                          }

                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    {({ value, onChange }, meta) => (
                      <InputLanguage
                        placeholder={intl.formatMessage({
                          defaultMessage: 'Nhập tên tài khoản',
                          id: 'components.container.accounts.components.AccountForm.29ee7312',
                        })}
                        name="username"
                        meta={meta}
                        value={value}
                        onChange={onChange}
                        maxLenght={64}
                      />
                    )}
                  </Form.Field>
                </div>
              </div>
              <div className="flex flex-col items-start gap-2 flex-grow flex-shink-0 basis-0">
                <div className="items-center space-x-2">
                  <span className="text-sm text-gray-800 font-medium">
                    <FormattedMessage
                      defaultMessage="Ngày sinh"
                      id="components.container.profile.ProfileForm.birthdate"
                    />
                  </span>
                </div>
                <Form.Field name="birthdate">
                  {({ value, onChange }, meta) => (
                    <IDatePicker
                      picker="date"
                      placeholder={intl.formatMessage({
                        defaultMessage: 'Chọn ngày sinh',
                        id: 'components.container.profile.ProfileForm.chooseBirthdate',
                      })}
                      dropdownClassName="!min-w-[276px] bg-white w-auto"
                      className="h-[36px]"
                      value={value ? dayjs(value) : undefined}
                      disabledDate={(date: dayjs.Dayjs) => {
                        const datecurrent = date.format('DD/MM/YYYY');
                        const today = dayjs().startOf('day').format('DD/MM/YYYY');
                        return (date && date >= dayjs().endOf('day')) || today === datecurrent;
                      }}
                      showToday={false}
                      onChange={onChange}
                    />
                  )}
                </Form.Field>
              </div>
              <div className="flex flex-col items-start gap-2 self-stretch">
                <div className="space-x-2">
                  <span className="text-sm text-gray-800 font-medium">
                    <FormattedMessage
                      defaultMessage="Ghi chú"
                      id="components.container.accounts.components.AccountForm.5b1e5596"
                    />
                  </span>
                </div>
                <Form.Field
                  name="description"
                  rules={[
                    {
                      validator(rule, value, callback) {
                        if (value?.vi?.length > 256) {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage(messageErrorCommon.fieldMax, {
                                max: 256,
                              }),
                            ),
                          );
                        }

                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  {({ value, onChange }, meta) => (
                    <InputLanguage
                      placeholder={intl.formatMessage({
                        defaultMessage: 'Thêm ghi chú về tài khoản.',
                        id: 'components.container.profile.ProfileForm.addNoteAboutAccount',
                      })}
                      name="description"
                      meta={meta}
                      value={value}
                      onChange={onChange}
                      type="textarea"
                      maxLenght={256}
                    />
                  )}
                </Form.Field>
              </div>
            </div>
            <div className={cn('flex flex-col gap-4 flex-1 h-full min-w-0 max-w-[650px]')}>
              <div className="flex flex-1 flex-col gap-4 p-4 rounded-xl bg-white min-w-0">
                <div className="flex flex-col gap-1">
                  <span className="text-base font-semibold text-gray-700">
                    <FormattedMessage
                      defaultMessage="Thông tin đăng nhập"
                      id="components.container.accounts.components.AccountForm.384afe01"
                    />
                  </span>
                  <span className="text-sm font-normal text-gray-500">
                    <FormattedMessage
                      defaultMessage="Email và số điện thoại có thể được sử dụng để đăng nhập vào hệ thống và nhận các thông báo, cảnh báo qua email và tin nhắn."
                      id="components.container.accounts.components.AccountForm.4072213d"
                    />
                  </span>
                </div>
                <div className="flex flex-col items-start gap-2">
                  <div className="items-center space-x-2">
                    <span className="text-sm text-gray-800 font-medium">
                      <FormattedMessage
                        defaultMessage="Email"
                        id="components.container.accounts.UserAccountPage.c207467b"
                      />
                    </span>
                    <span className="text-sm text-red-500 font-medium">*</span>
                  </div>
                  <FormItem
                    name="email"
                    rules={[
                      {
                        validator(rule, value, callback) {
                          if (!(value.length > 0)) {
                            return Promise.reject(
                              new Error(
                                intl.formatMessage(messageErrorCommon.fieldRequired, {
                                  field: intl.formatMessage({
                                    defaultMessage: 'Email',
                                    id: 'components.container.accounts.UserAccountPage.c207467b',
                                  }),
                                }),
                              ),
                            );
                          }

                          if (!new RegExp(emailRegExp).test(value)) {
                            return Promise.reject(
                              new Error(
                                intl.formatMessage(messageErrorCommon.fieldNotFormat, {
                                  field: intl.formatMessage({
                                    defaultMessage: 'Email',
                                    id: 'components.container.accounts.UserAccountPage.c207467b',
                                  }),
                                }),
                              ),
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    renderItem={({ control, meta, form, isError }) => (
                      <div title={control.value} className="w-full">
                        <Input
                          placeholder={intl.formatMessage({
                            defaultMessage: 'Nhập email',
                            id: 'components.container.accounts.components.AccountForm.eff648a4',
                          })}
                          className="placeholder-gray-500 text-sm [&_input:disabled]:text-gray-700"
                          onSubmitValue={control.onChange}
                          variant={isError ? 'error' : 'default'}
                          autoComplete="email"
                          prefix={<EnvelopeSimple size={16} />}
                          disabled={isUpdate}
                          {...control}
                        />
                      </div>
                    )}
                  />
                </div>
                <div className="flex flex-col items-start gap-2 w-full">
                  <div className="items-center space-x-2">
                    <span className="text-sm text-gray-800 font-medium">
                      <FormattedMessage
                        defaultMessage="Số điện thoại"
                        id="components.container.accounts.components.AccountForm.1372d3c0"
                      />
                    </span>
                  </div>
                  <FormItem
                    name="phone"
                    rules={[
                      {
                        validator(rule, value) {
                          const checkPhone = isPhoneValid(value);
                          if (checkPhone === false && value !== '') {
                            return Promise.reject(
                              new Error(
                                intl.formatMessage({
                                  defaultMessage: 'Số điện thoại không hợp lệ.',
                                  id: 'components.container.accounts.components.AccountForm.phoneNumberIsInvalid',
                                }),
                              ),
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                    renderItem={({ control, meta, form, isError }) => {
                      const checkPhone = isPhoneValid(control.value);
                      return (
                        <PhoneInput
                          defaultCountry="vn"
                          onChange={(value) => control.onChange(value)}
                          placeholder={intl.formatMessage({
                            defaultMessage: 'Nhập số điện thoại',
                            id: 'components.container.accounts.components.AccountForm.enterPhoneNumber',
                          })}
                          {...control}
                          className={cn(
                            'hover:ring-2 hover:ring-primary-300',
                            'rounded-lg h-11 w-full',
                            'transition-all ease-in duration-300',
                            '[&.react-international-phone-input-container_.react-international-phone-input]:w-full ',
                            '[&.react-international-phone-input-container_.react-international-phone-input]:h-full ',
                            '[&.react-international-phone-input-container_.react-international-phone-input]:block ',
                            '[&.react-international-phone-input-container_.react-international-phone-input]:rounded-r-lg ',
                            '[&.react-international-phone-input-container_.react-international-phone-country-selector-button]:h-full',
                            '[&.react-international-phone-input-container_.react-international-phone-country-selector-button]:rounded-l-lg',
                            '[&.react-international-phone-input-container_.react-international-phone-country-selector-button]:px-2',
                            '[&_.react-international-phone-country-selector-button]:w-[70px]',
                            '[&_.react-international-phone-country-selector-button_.react-international-phone-flag-emoji]:w-[35px]',
                            '[&_.react-international-phone-country-selector-button_.react-international-phone-flag-emoji]:h-[31px]',
                            {
                              '[&_.react-international-phone-country-selector_.react-international-phone-country-selector-dropdown]:w-[475px]':
                                isUpdate === false,
                              '[&_.react-international-phone-country-selector_.react-international-phone-country-selector-dropdown]:w-[430px]':
                                isUpdate === true,
                            },
                            '[&_.react-international-phone-country-selector_.react-international-phone-country-selector-dropdown]:rounded-lg',
                            {
                              'ring-1 ring-red-400 hover:ring-red-400 hover:ring-1':
                                (checkPhone === false && control.value !== '') || meta.errors.length > 0,
                            },
                          )}
                        />
                      );
                    }}
                  />
                </div>
                <Button
                  onClick={() => {
                    setModalState((prev) => ({
                      ...prev,
                      isOpenModalChangePass: true,
                    }));
                  }}
                  type="button"
                  outline
                  variant="neutral"
                  className="w-full hover:ring-2 hover:ring-primary-300 transition-all ease-in duration-300"
                >
                  <div className="flex gap-2 w-full">
                    <div className="flex gap-2 self-stretch w-full">
                      <IconCommon name="LockKeyOpen" size={20} color="#3F3F46" />
                      <span className="text-gray-800 text-sm font-normal">
                        <FormattedMessage
                          defaultMessage="Đổi mật khẩu tài khoản"
                          id="components.container.accounts.components.AccountForm.6b8ea5ae"
                        />
                      </span>
                    </div>
                    <IconCommon name="CaretRight" size={20} color="#3F3F46" />
                  </div>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </Form>

      <Dialog open={modalState.isOpenModalChangePass} onOpenChange={onOpenChangeModalPass}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className={cn('p-0 border-none gap-0 max-w-[500px] ')}>
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <UpdatePasswordProfile
              dataAccount={dataAccount}
              isOpenChangePass={modalState.isOpenModalChangePass}
              onClose={() => {
                if (modalState.isChangeFieldPassword) {
                  setModalState((prev) => ({
                    ...prev,
                    isOpenConfirm: true,
                  }));
                } else {
                  setModalState((prev) => ({
                    ...prev,
                    isOpenModalChangePass: false,
                  }));
                }
              }}
              setIsChangeFieldPass={(value) =>
                setModalState((prev) => ({
                  ...prev,
                  isChangeFieldPassword: value as boolean,
                }))
              }
              onCloseConfirmChangePass={(value) => {
                setModalState((prev) => ({
                  ...prev,
                  isConfirmChangePassSuccess: value as boolean,
                  isChangeFieldPassword: false,
                  isOpenModalChangePass: false,
                  isOpenConfirm: false,
                }));
              }}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>

      <Dialog open={modalState.isOpenConfirm}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="max-w-[350px] p-0">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onCancel={() => {
                setModalState((prev) => ({
                  ...prev,
                  isOpenConfirm: false,
                }));
              }}
              onSubmit={() => {
                setModalState((prev) => ({
                  ...prev,
                  isOpenConfirm: false,
                  isOpenModalChangePass: false,
                  isChangeFieldPassword: false,
                }));
              }}
              heading={intl.formatMessage({
                defaultMessage: 'Hủy đổi mật khẩu',
                id: 'components.container.accounts.components.AccountAction.cancelChangePassAccount',
              })}
              description={intl.formatMessage({
                defaultMessage:
                  'Bạn có chắc muốn hủy đổi mật khẩu cho tài khoản này không? Các thông tin mà bạn vừa nhập sẽ không được lưu lại.',
                id: 'components.container.accounts.components.AccountAction.areYouSureCancelChangePassAccount',
              })}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>

      <Dialog open={modalState.isConfirmChangePassSuccess}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="max-w-[350px] p-0">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onSubmit={async () => {
                await deleteCookie('beforeReload');
                return router.push(`${WEB_CORE_URL}/api/logto/sign-out`);
              }}
              heading={intl.formatMessage({
                defaultMessage: 'Thông tin tài khoản của bạn đã được cập nhật',
                id: 'components.container.profile.ProfileForm.**********',
              })}
              description={intl.formatMessage({
                defaultMessage:
                  'Vì lý do bảo mật, hệ thống sẽ đăng xuất tài khoản của bạn khỏi các thiết bị. Vui lòng đăng nhập lại để tiếp tục sử dụng ứng dụng.',
                id: 'components.container.profile.ProfileForm.1537149278',
              })}
              showButton
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </Spin>
  );
}
