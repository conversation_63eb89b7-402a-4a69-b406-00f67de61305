import { BACKEND_API_ENDPOINT } from '@/constants';
import apiPathName from './apiPathName';
import { BaseClient } from './baseClient';
import { Applications, Permission, Users } from './dataType';
import { DataResponse } from './type';

export class AuthClient extends BaseClient<Record<string, any>> {
  moduleName = 'AuthClient';
  constructor() {
    super(BACKEND_API_ENDPOINT + apiPathName.auth.root);
  }

  getProfile(): Promise<DataResponse<Users>> {
    return this.api.get(apiPathName.auth.profile);
  }

  getApplications(id?: string): Promise<Applications[]> {
    return this.api.get(`${apiPathName.auth.authApplications}/${id}`);
  }

  getAuthAccess(): Promise<{ access: boolean }> {
    return this.api.get(apiPathName.auth.authAccess);
  }

  getPermissions(): Promise<DataResponse<Permission[]>> {
    return this.api.get(apiPathName.auth.userPermission);
  }
}
