'use client';

import Icon<PERSON><PERSON>mon from '@/components/commons/IconLayout';
import { HeaderModal } from '@/components/ui/modal/header-modal';
import { generateNums } from '@/utils';
import Image from 'next/image';
import { Dispatch, SetStateAction, useState } from 'react';
import { Badge, Form, FormItem, Input, toast } from 'ui-components';
import { FormattedMessage, useIntl } from 'react-intl';
import { messageAlertCommon, messageErrorCommon, messageStatusCommon } from '@/constants/defineMessages';
import { capitalizeFirstLetter } from '@/utils/string';
import { useCreateClient } from '@/api/useCreateClient';
import OverflowTooltip from '@/components/commons/OverflowTooltip';
import { ShowStrengthPassword } from '../accounts/CheckPassword';
import { updatePasswordProfileType } from '@/api/usersClient';
import { setCookie } from '@/actions/cookieActions';

interface UpdatePasswordProfileProps {
  onClose: () => void;
  dataAccount?: Record<string, any> | null;
  isOpenChangePass: boolean;
  onCloseConfirmChangePass: Dispatch<SetStateAction<boolean>>;
  setIsChangeFieldPass?: Dispatch<SetStateAction<boolean>>;
}

interface formValue {
  oldPassword: string;
  password: string;
  confirmPassword: string;
}

const UpdatePasswordProfile = ({
  onClose,
  dataAccount,
  isOpenChangePass,
  onCloseConfirmChangePass,
  setIsChangeFieldPass,
}: UpdatePasswordProfileProps) => {
  const [form] = Form.useForm();
  const intl = useIntl();

  const [showPasswordCurrent, setShowPasswordCurrent] = useState({
    isShowPass: false,
    isShowCurrentPass: false,
  });

  const locale = intl.locale as 'vi' | 'en';
  const indexImage = generateNums(dataAccount?.logtoId!);
  const urlAvatar = `/config/images/avatars/avatar-${indexImage}.svg`;

  const initialValues = {
    oldPassword: '',
    password: '',
    confirmPassword: '',
  };

  const mutationOptions = {
    invalidateQueries: {
      enable: true,
    },
  };

  const { users } = useCreateClient();

  const { mutateAsync: updatePasswordProfileMutate, isPending } = users.updatePasswordProfile(mutationOptions);

  const handleSubmit = async (value: formValue) => {
    try {
      const { oldPassword, password } = value;
      const body: updatePasswordProfileType = {
        oldPassword,
        newPassword: password,
      };
      await updatePasswordProfileMutate(body);

      await setCookie('beforeReload', 'true');

      onClose?.();
      onCloseConfirmChangePass?.(true);
    } catch (error: any) {
      console.log('error:', error);
      const { code }: { code: string } = error.response.data;
      if (code === 'session.invalid_credentials') {
        const fieldOldPass = code
          ? {
              name: 'oldPassword',
              errors: [
                intl.formatMessage({
                  defaultMessage: 'Mật khẩu hiện tại mà bạn nhập không đúng. Vui lòng nhập lại mật khẩu hiện tại.',
                  id: 'components.container.profile.UpdatePasswordProfile.oldPasswordIncorrect',
                }),
              ],
            }
          : null;
        const error: any = [fieldOldPass].filter(Boolean);
        return form.setFields(error);
      }
      const title = messageAlertCommon.changePassFail;
      toast({
        title: `${capitalizeFirstLetter(
          intl.formatMessage(title, {
            type: intl.formatMessage({
              defaultMessage: 'mật khẩu',
              id: 'components.container.accounts.components.UpdatePassword.password',
            }),
          }),
        )}`,
        type: 'error',
        options: {
          position: 'top-center',
        },
      });
      onClose?.();
    }
  };

  return (
    <>
      <Form
        name="updatePassForm"
        form={form}
        onFinish={handleSubmit}
        initialValues={initialValues}
        onFieldsChange={(_, allFields) => {
          const isValid = allFields.some((field) => field.touched);
          setIsChangeFieldPass?.(isValid);
        }}
      >
        <HeaderModal
          title={
            <FormattedMessage
              defaultMessage="Đổi mật khẩu"
              id="components.container.accounts.components.AccountForm.changePassword"
            />
          }
          buttonForm={{
            content: (
              <FormattedMessage
                defaultMessage="Cập nhật"
                id="components.container.authorization.components.BaseFormData.**********"
              />
            ),
            type: 'submit',
            isPending: isPending,
          }}
          onClose={() => onClose?.()}
        />
        <div className="flex p-4 flex-col items-start self-stretch gap-4 bg-gray-100 rounded-b-xl max-h-[calc(100vh_-_164px)] overflow-auto">
          <div className="flex flex-col p-4 self-stretch w-full bg-white gap-4 rounded-xl">
            <div className="flex py-4 pr-6 pl-4 justify-center items-center self-stretch gap-2.5 rounded-xl border border-gray-200">
              <div className="flex items-center gap-3 flex-1 min-w-0">
                <Image
                  src={urlAvatar}
                  alt="logo-avt"
                  width={44}
                  height={44}
                  className="cursor-pointer flex-shrink-0"
                  unoptimized
                  priority
                />
                <div className="flex flex-col justify-center items-start gap-0.5 flex-1 min-w-0">
                  <div className="flex flex-row items-center w-full">
                    <OverflowTooltip
                      text={`${dataAccount?.username[locale]!} ${dataAccount?.givenName[locale]!}`}
                      className="w-full text-base font-medium text-gray-800"
                    />
                  </div>
                  <div className="flex flex-row items-center w-full">
                    <OverflowTooltip text={dataAccount?.email} className="w-full text-xs font-normal" />
                  </div>
                </div>
              </div>
              <Badge className="flex-shrink-0" variant={dataAccount?.isActive ? 'default' : 'secondary'}>
                {dataAccount?.isActive
                  ? intl.formatMessage(messageStatusCommon.activate)
                  : intl.formatMessage(messageStatusCommon.disable)}
              </Badge>
            </div>
            <div className="flex flex-col items-start gap-2">
              <div className="items-center space-x-2">
                <span className="text-sm text-gray-800 font-medium">
                  <FormattedMessage
                    defaultMessage="Mật khẩu hiện tại"
                    id="components.container.accounts.CheckPassword.passwordCurrent"
                  />
                </span>
                <span className="text-sm text-red-500 font-medium">*</span>
              </div>
              <FormItem
                name={'oldPassword'}
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage(messageErrorCommon.fieldRequired, {
                      field: intl.formatMessage({
                        defaultMessage: 'Mật khẩu hiện tại',
                        id: 'components.container.accounts.CheckPassword.passwordCurrent',
                      }),
                    }),
                  },
                ]}
                // validateTrigger="onBlur" updatePasswordProfile
                renderItem={({ control, meta, form, isError }) => {
                  return (
                    <Input
                      placeholder={intl.formatMessage({
                        defaultMessage: 'Nhập mật khẩu hiện tại',
                        id: 'components.container.accounts.CheckPassword.enterCurrentPassword',
                      })}
                      type={showPasswordCurrent.isShowCurrentPass ? 'text' : 'password'}
                      className="placeholder-gray-500 text-sm"
                      prefix={<IconCommon name="Lock" size={16} />}
                      variant={isError ? 'error' : 'default'}
                      autoComplete="current-password"
                      onSubmitValue={control.onChange}
                      suffix={
                        control.value && (
                          <IconCommon
                            name={`${showPasswordCurrent.isShowCurrentPass ? 'Eye' : 'EyeClosed'}`}
                            size={16}
                            className="cursor-pointer"
                            onClick={() =>
                              setShowPasswordCurrent((prevState) => ({
                                ...prevState,
                                isShowCurrentPass: !prevState.isShowCurrentPass,
                              }))
                            }
                          />
                        )
                      }
                      value={control.value ?? ''}
                      {...control}
                    />
                  );
                }}
              />
            </div>
            <ShowStrengthPassword form={form} isOpenChangePass={isOpenChangePass} />
          </div>
        </div>
      </Form>
    </>
  );
};

export { UpdatePasswordProfile };
