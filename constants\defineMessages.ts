import { defineMessages } from 'react-intl';

export const messageErrorCommon = defineMessages({
  fieldRequired: {
    defaultMessage: '{field} không được để trống.',
    id: 'constants.defineMessages.1790118527',
  },
  fieldMax: {
    defaultMessage: 'Không được nhập quá {max} ký tự.',
    id: 'constants.defineMessages.852565916',
  },
  fieldFileMax: {
    defaultMessage: 'Giới hạn mỗi tệp không được vượt quá {max} MB.',
    id: 'constants.defineMessages.fileMustNotExceed',
  },
  fieldMin: {
    defaultMessage: '{field} phải có ít nhất {min} ký tự.',
    id: 'constants.defineMessages.84628235',
  },
  fieldInvalid: {
    defaultMessage: '{field} không hợp lệ.',
    id: 'constants.defineMessages.424075352',
  },
  fieldNotFormat: {
    defaultMessage: '{field} không đúng định dạng.',
    id: 'constants.defineMessages.439996808',
  },
  fieldPassNotFormat: {
    defaultMessage: '{field} không trùng nhau.',
    id: 'constants.defineMessages.80dcc7bb',
  },
});

export const messageAlertCommon = defineMessages({
  createSuccess: {
    defaultMessage: 'Đã tạo {type} mới thành công.',
    id: 'constants.defineMessages.967969992',
  },
  createFail: {
    defaultMessage: 'Không thể tạo {type} mới. Vui lòng thử lại sau.',
    id: 'constants.defineMessages.1278882097',
  },
  updateSuccess: {
    defaultMessage: 'Đã cập nhật thông tin {type} thành công.',
    id: 'constants.defineMessages.1280440072',
  },
  updateFail: {
    defaultMessage: 'Không thể cập nhật thông tin {type}. Vui lòng thử lại sau',
    id: 'constants.defineMessages.1073665205',
  },
  updateGroupSuccess: {
    defaultMessage: 'Đã cập nhật {type} thành công.',
    id: 'constants.defineMessages.updateGroupSuccess',
  },
  updateGroupFail: {
    defaultMessage: 'Không thể cập nhật {type}. Vui lòng thử lại sau',
    id: 'constants.defineMessages.updateGroupFail',
  },
  deleteSuccess: {
    defaultMessage: 'Đã xóa {type} thành công.',
    id: 'constants.defineMessages.*********',
  },
  deleteFail: {
    defaultMessage: 'Không thể xóa {type}. Vui lòng thử lại sau',
    id: 'constants.defineMessages.*********',
  },
  removeUserGroupFail: {
    defaultMessage: 'Không thể loại bỏ tài khoản khỏi nhóm này. Vui lòng thử lại sau.',
    id: 'components.container.group-account.components.RemoveUserGroupAccount.cannotRemoveUser',
  },
  removeUserGroupSuccess: {
    defaultMessage: 'Đã loại bỏ tài khoản khỏi nhóm thành công.',
    id: 'components.container.group-account.components.RemoveUserGroupAccount.hasBeenRemoveUser',
  },
  removeRoleFail: {
    defaultMessage: 'Không thể loại bỏ nhóm quyền. Vui lòng thử lại sau.',
    id: 'components.container.accounts.components.RemoveRoleAccount.cannotRemoveRole',
  },
  removeRoleSuccess: {
    defaultMessage: 'Đã loại bỏ nhóm quyền thành công.',
    id: 'components.container.accounts.components.RemoveRoleAccount.hasBeenRemoveRole',
  },
  disableSuccess: {
    defaultMessage: 'Đã vô hiệu hóa {type} thành công.',
    id: 'constants.defineMessages.disableSuccess',
  },
  disableFail: {
    defaultMessage: 'Không thể vô hiệu hóa {type}. Vui lòng thử lại sau.',
    id: 'constants.defineMessages.disableFail',
  },
  activateSuccess: {
    defaultMessage: 'Đã kích hoạt {type} thành công.',
    id: 'constants.defineMessages.activateSuccess',
  },
  activateFail: {
    defaultMessage: 'Không thể kích hoạt {type}. Vui lòng thử lại sau.',
    id: 'constants.defineMessages.activateFail',
  },
  changePassSuccess: {
    defaultMessage: 'Đã đổi {type} tài khoản thành công.',
    id: 'constants.defineMessages.changePassSuccess',
  },
  changePassFail: {
    defaultMessage: 'Không thể đổi {type} tài khoản. Vui lòng thử lại sau.',
    id: 'constants.defineMessages.changePassFail',
  },
});

export const messageStatusCommon = defineMessages({
  activate: {
    defaultMessage: 'Kích hoạt',
    id: 'constants.defineMessages.75d334b5',
  },
  disable: {
    defaultMessage: 'Vô hiệu hóa',
    id: 'constants.defineMessages.f6fbf4ac',
  },
})

export const messagePlaceholder = defineMessages({
  search: {
    defaultMessage: 'Tìm kiếm tài khoản',
    id: 'constants.defineMessages.searchAccount',
  },
})
