'use client';

import { moduleScope, Scopes } from '@/constants/scopes';
import { usePermissionModule } from '@/hook/usePermissionModule';
import { appStories } from '@/store';
import { usePathname } from 'next/navigation';
import { useIntl } from 'react-intl';
import { useCreateClient } from '@/api/useCreateClient';
import { GroupUser } from '@/api/dataType';
import { useRef, useState } from 'react';
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Popover,
  PopoverContent,
  PopoverTrigger,
} from 'ui-components';
import { BASE_PATH } from '@/constants';
import Image from 'next/image';
import Link from 'next/link';
import { cn } from '@/utils/tailwind';
import { DotsThreeVertical, Plus, Trash, UsersThree } from '@phosphor-icons/react';
import { FormattedMessage } from 'react-intl';
import OverflowTooltip from '@/components/commons/OverflowTooltip';
import { GroupAccountForm } from '../group-account/components/GroupAccountForm';
import { DeleteGroupAccount } from '../group-account/components/DeleteGroupAccount';

export default function UserGroupNavBar() {
  const intl = useIntl();
  const pathname = usePathname();
  const actionGroupUser = usePermissionModule(moduleScope.USER_GROUPS, Scopes.GroupUserConfigScopes);
  const recordAccRef = useRef<GroupUser>(null);
  const [isOpenGroupAccountForm, setIsOpenGroupAccountForm] = useState(false);
  const [isChangedData, setIsChangeData] = useState(false);
  const [isOpenConfirm, setIsOpenConfirm] = useState(false);
  const [isOpenDeleteGroupAccount, setIsOpenDeleteGroupAccount] = useState(false);
  const locale = intl.locale as 'vi' | 'en';

  const { groupUsers } = useCreateClient();

  const { data: listGroupUsers, isLoading } = groupUsers.getGroupUsersInOrg();

  const dataGroupAccount = listGroupUsers ?? [];

  const onOpenChange = (open: boolean) => {
    if (isChangedData) {
      setIsOpenConfirm(true);
    } else {
      setIsOpenGroupAccountForm(open);
    }
  };

  const handleDeleteGroupAccount = (group: GroupUser) => {
    if (!actionGroupUser.DELETE) return;
    setIsOpenDeleteGroupAccount(true);
    recordAccRef.current = group;
  };

  if (isLoading) return null;

  return (
    <>
      {dataGroupAccount.length > 0 ? (
        <div className="flex flex-col w-full">
          <div className="flex flex-col gap-2 h-full max-h-[calc(100vh_-_230px)]">
            <span className="text-base font-medium text-gray-700">
              <FormattedMessage
                defaultMessage="Nhóm tài khoản"
                id="components.container.accounts.UserAccountPage.accountGroup"
              />
            </span>
            <div className="flex flex-col h-auto max-h-[650px] overflow-auto">
              {dataGroupAccount.map((group) => {
                const isActivateGroup = pathname === `/accounts/group/${group.id}`;
                return (
                  <div
                    key={group.id}
                    className={cn(
                      'flex justify-between gap-2 items-center text-gray-700 w-full',
                      'hover:bg-gray-100 rounded-lg',
                      {
                        'bg-primary-500 text-white hover:bg-primary-500': isActivateGroup,
                      },
                    )}
                  >
                    <div className="flex flex-row w-full">
                      <Link
                        href={`/accounts/group/${group.id}`}
                        className={cn('flex flex-1 gap-2 items-center w-full p-2.5')}
                        prefetch={false}
                      >
                        <UsersThree size={20} />
                        <OverflowTooltip text={group.name[locale]} />
                      </Link>
                      {(actionGroupUser.UPDATE || actionGroupUser.DELETE) && (
                        <Popover>
                          <PopoverTrigger className="flex justify-center items-center text-center px-2">
                            <div
                              className={cn('text-gray-500 px-0 py-1 hover:bg-gray-200 hover:rounded-[4px]', {
                                'bg-primary-500 text-white hover:bg-primary-500': isActivateGroup,
                              })}
                            >
                              <DotsThreeVertical
                                size={20}
                                className={cn('block hover:text-primary-500 text-current', {
                                  'hover:text-current': isActivateGroup,
                                })}
                              />
                            </div>
                          </PopoverTrigger>
                          <PopoverContent
                            align="center"
                            side="right"
                            sideOffset={-60}
                            className="bg-white w-auto min-w-[260px] flex flex-col rounded-xl p-2"
                          >
                            {actionGroupUser.UPDATE && (
                              <div
                                className="flex flex-row items-center text-gray-700 gap-2 cursor-pointer p-2 hover:bg-gray-100 hover:rounded-lg"
                                onClick={() => {
                                  if (!actionGroupUser.UPDATE) return;
                                  setIsOpenGroupAccountForm(true);
                                  recordAccRef.current = group;
                                }}
                              >
                                <UsersThree size={20} className="text-current" />
                                <span className="text-sm font-normal text-current">
                                  <FormattedMessage
                                    defaultMessage="Cập nhật nhóm tài khoản"
                                    id="components.container.account-managenment.SiderbarAccountManagement.updateGroupAccount"
                                  />
                                </span>
                              </div>
                            )}
                            {actionGroupUser.DELETE && (
                              <div
                                className="flex flex-row items-center gap-2 text-red-500 cursor-pointer p-2 hover:bg-gray-100 hover:rounded-lg"
                                onClick={() => handleDeleteGroupAccount(group)}
                              >
                                <Trash size={20} className="text-current" />
                                <span className="text-sm font-normal text-current">
                                  <FormattedMessage
                                    defaultMessage="Xóa nhóm tài khoản"
                                    id="components.container.account-managenment.SiderbarAccountManagement.deleteGroupAccount"
                                  />
                                </span>
                              </div>
                            )}
                          </PopoverContent>
                        </Popover>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
          {actionGroupUser.CREATE && (
            <Button
              variant="neutral"
              className="flex justify-start p-2.5 bg-gray-50"
              onClick={() => {
                setIsOpenGroupAccountForm(true);
                recordAccRef.current = null;
              }}
            >
              <Plus size={20} className="text-primary-500" />
              <FormattedMessage
                defaultMessage="Tạo nhóm"
                id="components.container.account-managenment.SiderbarAccountManagement.*********"
              />
            </Button>
          )}
        </div>
      ) : (
        <div className="flex flex-col border-dashed border-2 border-gray-300 rounded-xl p-5 gap-3 text-center items-center">
          <Image
            width={120}
            height={120}
            src={`${BASE_PATH}/images/commons/no-general-data.svg`}
            alt="Nhóm tài khoản"
            unoptimized
            priority
          />
          <div className="text-base items-center text-gray-700 font-medium">
            <FormattedMessage
              defaultMessage="Nhóm tài khoản"
              id="components.container.accounts.UserAccountPage.accountGroup"
            />
          </div>
          <p className="text-gray-700 text-xs font-normal">
            {actionGroupUser.CREATE ? (
              <FormattedMessage
                defaultMessage="Vui lòng tạo nhóm tài khoản mới để dễ dàng quản lý và phân quyền tài khoản."
                id="components.container.accounts.UserAccountPage.pleaseCreateGroupAccount"
              />
            ) : (
              <FormattedMessage
                defaultMessage="Hiện chưa có nhóm tài khoản nào để quản lý. Bạn không có quyền thêm nhóm tài khoản."
                id="components.container.account-managenment.UserGroupNavBar.**********"
              />
            )}
          </p>
          {actionGroupUser.CREATE && (
            <Button
              variant="neutral"
              className="bg-gray-100 hover:bg-gray-200"
              onClick={() => {
                setIsOpenGroupAccountForm(true);
                recordAccRef.current = null;
              }}
            >
              <Plus size={20} className="text-primary-500" />
              <FormattedMessage
                defaultMessage="Tạo nhóm"
                id="components.container.account-managenment.SiderbarAccountManagement.*********"
              />
            </Button>
          )}
        </div>
      )}

      <Dialog open={isOpenGroupAccountForm} onOpenChange={onOpenChange}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[900px] max-h-[calc(100vh_-_100px)]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <GroupAccountForm
              onClose={() => {
                setIsOpenGroupAccountForm(false);
                setIsChangeData(false);
              }}
              dataGroupAccount={recordAccRef.current!}
              isChangedData={isChangedData}
              setIsChangedData={setIsChangeData}
              isOpenConfirm={isOpenConfirm}
              setIsOpenConfirm={setIsOpenConfirm}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>

      <Dialog open={isOpenDeleteGroupAccount}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[380px]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <DeleteGroupAccount
              dataRow={recordAccRef.current!}
              onClose={() => {
                setIsOpenDeleteGroupAccount(false);
              }}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
}
