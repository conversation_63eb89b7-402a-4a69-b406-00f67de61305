import { renderHook, act } from '@testing-library/react';
import { usePathAccess } from '@/hook/usePathAccess';
import { appStories } from '@/store';

jest.mock('@/store');
const mockAppStories = appStories as jest.MockedFunction<typeof appStories>;

describe('usePathAccess - Performance & Edge Cases', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  describe('⚡ Performance Tests', () => {
    it('should cache access results efficiently', async () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // First call - should take full delay
      const promise1 = result.current.canAccessWithLoading('/accounts', 1000);
      
      act(() => {
        jest.advanceTimersByTime(1000);
      });
      
      const result1 = await promise1;

      // Second call - should return cached result immediately
      const start = performance.now();
      const result2 = await result.current.canAccessWithLoading('/accounts', 1000);
      const duration = performance.now() - start;

      expect(result1).toBe(true);
      expect(result2).toBe(true);
      expect(duration).toBeLessThan(10); // Should be nearly instant
    });

    it('should handle concurrent requests for same path', async () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Start multiple concurrent requests
      const promise1 = result.current.canAccessWithLoading('/accounts', 500);
      const promise2 = result.current.canAccessWithLoading('/accounts', 500);
      const promise3 = result.current.canAccessWithLoading('/accounts', 500);

      act(() => {
        jest.advanceTimersByTime(500);
      });

      const [result1, result2, result3] = await Promise.all([promise1, promise2, promise3]);

      expect(result1).toBe(true);
      expect(result2).toBe(true);
      expect(result3).toBe(true);
    });

    it('should process batch requests efficiently', async () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'setting:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      const paths = [
        '/accounts',
        '/accounts/group',
        '/settings',
        '/settings/attachment',
        '/monitoring-station',
      ];

      const start = performance.now();
      const batchPromise = result.current.canAccessMultiple(paths, 200);
      
      act(() => {
        jest.advanceTimersByTime(200);
      });
      
      const results = await batchPromise;
      const duration = performance.now() - start;

      expect(results.size).toBe(5);
      expect(results.get('/accounts')).toBe(true);
      expect(results.get('/settings')).toBe(true);
      expect(results.get('/monitoring-station')).toBe(false);
      
      // Batch should be faster than individual requests
      expect(duration).toBeLessThan(1000); // Much faster than 5 * 200ms
    });

    it('should cleanup timeouts properly', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });

      const { result, unmount } = renderHook(() => usePathAccess());

      // Start some loading operations
      result.current.canAccessWithLoading('/accounts', 1000);
      result.current.canAccessWithLoading('/settings', 1000);

      // Unmount should cleanup all timeouts
      unmount();

      // Advance timers - should not cause any issues
      act(() => {
        jest.advanceTimersByTime(2000);
      });

      // No errors should occur
      expect(true).toBe(true);
    });
  });

  describe('🚨 Edge Cases', () => {
    it('should handle null/undefined userPermissions', () => {
      mockAppStories.mockReturnValue({
        userPermissions: null,
      });

      const { result } = renderHook(() => usePathAccess());

      expect(result.current.unauthorized).toBe(true);
      expect(result.current.canAccess('/accounts')).toBe(false);
      expect(result.current.canAccess('/access-denied')).toBe(true); // Public path
    });

    it('should handle empty userPermissions array', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [],
      });

      const { result } = renderHook(() => usePathAccess());

      expect(result.current.unauthorized).toBe(true);
      expect(result.current.accessiblePaths).toHaveLength(0);
      expect(result.current.canAccess('/not-permission')).toBe(true);
    });

    it('should handle malformed permission objects', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: null }, // Malformed
          { keyName: undefined }, // Malformed
          { keyName: '' }, // Empty
          { keyName: 'setting:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      // Should still work with valid permissions
      expect(result.current.canAccess('/accounts')).toBe(true);
      expect(result.current.canAccess('/settings')).toBe(true);
    });

    it('should handle invalid path formats', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Test various invalid path formats
      expect(result.current.canAccess('')).toBe(false);
      expect(result.current.canAccess('invalid-path')).toBe(false);
      expect(result.current.canAccess('//double-slash')).toBe(false);
      expect(result.current.canAccess('/accounts//double')).toBe(false);
    });

    it('should handle very long paths', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      const longPath = '/accounts/' + 'a'.repeat(1000);
      
      // Should not crash
      expect(() => result.current.canAccess(longPath)).not.toThrow();
      expect(result.current.canAccess(longPath)).toBe(false);
    });

    it('should handle special characters in paths', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'group:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Test paths with special characters
      const specialPaths = [
        '/accounts/group/user-123',
        '/accounts/group/user_456',
        '/accounts/group/user.789',
        '/accounts/group/user@domain',
      ];

      specialPaths.forEach(path => {
        expect(() => result.current.canAccess(path)).not.toThrow();
      });
    });

    it('should handle rapid permission changes', () => {
      const { result, rerender } = renderHook(() => usePathAccess());

      // Start with user permissions
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });
      rerender();

      expect(result.current.canAccess('/accounts')).toBe(true);

      // Change to settings permissions
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'setting:read' }],
      });
      rerender();

      expect(result.current.canAccess('/accounts')).toBe(false);
      expect(result.current.canAccess('/settings')).toBe(true);

      // Remove all permissions
      mockAppStories.mockReturnValue({
        userPermissions: [],
      });
      rerender();

      expect(result.current.unauthorized).toBe(true);
    });
  });

  describe('🔄 Loading State Management', () => {
    it('should track loading states correctly', async () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Start loading
      const promise = result.current.canAccessWithLoading('/accounts', 500);
      
      // Should be loading
      expect(result.current.isPathLoading('/accounts')).toBe(true);
      expect(result.current.pathLoadingState.get('/accounts')).toBe(true);

      // Complete loading
      act(() => {
        jest.advanceTimersByTime(500);
      });
      
      await promise;

      // Should not be loading anymore
      expect(result.current.isPathLoading('/accounts')).toBe(false);
      expect(result.current.pathLoadingState.has('/accounts')).toBe(false);
    });

    it('should handle loading state cleanup on permission change', async () => {
      const { result, rerender } = renderHook(() => usePathAccess());

      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });
      rerender();

      // Start loading
      result.current.canAccessWithLoading('/accounts', 1000);
      expect(result.current.isPathLoading('/accounts')).toBe(true);

      // Change permissions - should cleanup loading states
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'setting:read' }],
      });
      rerender();

      expect(result.current.isPathLoading('/accounts')).toBe(false);
      expect(result.current.pathLoadingState.size).toBe(0);
    });
  });

  describe('🎯 Path Similarity Algorithm', () => {
    it('should calculate path similarity correctly', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'group:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      // Test finding alternative path with highest similarity
      const alternative = result.current.findAlternativePathInModule('/accounts/authorization');
      
      // /accounts/group should be preferred over /accounts due to higher similarity
      // Both have 'accounts' in common, but 'group' is more similar to 'authorization'
      expect(alternative).toBeTruthy();
      expect(['/accounts', '/accounts/group']).toContain(alternative);
    });

    it('should handle paths with different segment counts', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'setting:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      // Test similarity between paths of different lengths
      const bestPath = result.current.findBestRedirectPath('/accounts/authorization/deep/path');
      
      // Should still find a good alternative
      expect(bestPath).toBeTruthy();
      expect(['/accounts', '/settings']).toContain(bestPath);
    });
  });
});
