import { refreshToken } from '@/actions/logtoActions';
import { WEB_CORE_URL } from '@/constants';
import { urlPathName } from '@/constants/urlPathName';
import axios, { AxiosError, AxiosInstance, AxiosResponse } from 'axios';
import { DataResponse, DataWithPagination, FilterOptions, PaginationOptions, SortValue } from './type';

declare module 'axios' {
  export interface AxiosRequestConfig {
    _retry?: boolean;
  }
}

export class BaseClient<T> {
  protected api: AxiosInstance;
  moduleName = 'BaseClient';

  constructor(baseURL: string) {
    this.api = axios.create({
      baseURL,
      withCredentials: true,
      headers: {
        Accept: 'application/json',
        'Content-Type': 'application/json',
        'x-api-key-client': 'config:web',
      },
    });

    this.api.interceptors.response.use(
      (response: AxiosResponse) => response.data,
      async (error: AxiosError) => {
        const status = error.response?.status;
        const config = error.config;
        const dataError = error.response?.data as { code: string; message?: string };

        // 1. Handle 403
        if (status === 403) {
          if (dataError?.code === 'NOT_FOUND_SESSION') {
            if (typeof window !== 'undefined') {
              localStorage.setItem('session_expired', 'true');

              const sessionExpiredEvent = new CustomEvent('session_expired', {
                detail: { message: 'Tài khoản của bạn đã bị thay đổi hoặc hết hạn phiên làm việc.' },
              });
              window.dispatchEvent(sessionExpiredEvent);
              return Promise.resolve();
            }
          } else {
            return window.location.assign(`${WEB_CORE_URL}${urlPathName.ACCESS_DENIED}`);
          }
        }

        // 2. Handle 401 - Token hết hạn
        if (status === 401 && !config?._retry) {
          config!._retry = true; // prevent infinite loop

          try {
            const result = await refreshToken();
            console.log('[Interceptor] refreshToken result:', result);

            if (!result) {
              return window.location.assign(`${WEB_CORE_URL}/api/logto/sign-out`);
            }

            // Retry lại request sau khi đã refresh thành công
            return this.api.request(config!);
          } catch (refreshError) {
            console.error('[Interceptor] Refresh token failed:', refreshError);
            return window.location.assign(`${WEB_CORE_URL}/api/logto/sign-out`);
          }
        }

        // 3. Các lỗi khác (hoặc đã retry mà vẫn lỗi)
        return Promise.reject(error);
      },
    );
  }

  find(
    filter: FilterOptions<T>,
    pagination: PaginationOptions,
    sort: SortValue<T>,
  ): Promise<T[] | DataResponse<DataWithPagination<T> | T>> {
    return this.api.post(`/`, { filter, pagination, sort });
  }

  getById(id: string | number): Promise<T | DataResponse<T>> {
    return this.api.get(`/${id}`);
  }

  create(data: Partial<T>): Promise<T | DataResponse<T>> {
    return this.api.post(`/`, data);
  }

  update(id: string | number, data: Partial<T>): Promise<T | DataResponse<T>> {
    return this.api.put(`/${id}`, data);
  }

  delete(id: string | number): Promise<T | DataResponse<T>> {
    return this.api.delete(`/${id}`);
  }
}
