'use client';

import { cn } from '@/utils/tailwind';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { Tooltip, TooltipContent, TooltipTrigger } from 'ui-components';
import IconCommon from '../commons/IconLayout';
import { AsideItemProps } from './dataItemAside';
import { CaretRight } from '@phosphor-icons/react';
import { appStories } from '@/store';
import SkeletonComp from '../ui/skeleton';

const ItemAside = ({
  itemAside,
  isToggle,
}: {
  itemAside: AsideItemProps;
  isToggle: boolean;
}) => {
  const { user } = appStories((state) => state);
  const pathName = usePathname();
  const pathItem: string =
    typeof itemAside.path === 'string'
      ? itemAside.path
      : itemAside.path?.pathname!;

  const isActive = pathName.split('/')[1] === pathItem.split('/')[1];

  if (!user) {
    return <SkeletonComp className="h-11 w-11 rounded-lg bg-gray-100" />;
  }
  return (
    <Link
      href={itemAside.path}
      className={cn(
        'min-w-11 flex flex-row p-2.5 gap-2.5 items-center text-gray-700 cursor-pointer hover:bg-gray-100 hover:rounded-lg relative',
        isActive &&
          'bg-primary-100 text-primary-500 rounded-lg hover:bg-primary-100',
      )}
      prefetch={false}
      title={''}
    >
      <IconCommon
        name={itemAside.icon}
        className="text-current flex-shrink-0"
        size={isToggle ? 24 : 20}
      />
      <div
        className={cn(
          'block',
          'overflow-hidden text-sm leading-[21px] font-normal text-gray-800 min-w-[146px] visible opacity-100 transition-opacity duration-700',
          isToggle && 'invisible opacity-0 transition-opacity duration-700',
          isActive && 'text-current',
        )}
      >
        {itemAside.name}
      </div>
      <CaretRight
        className="text-current flex-shrink-0"
        size={20}
        weight="regular"
      />
      <Tooltip>
        <TooltipTrigger asChild>
          <div
            className={cn('absolute top-0 right-0 h-11 w-11 rounded-lg', {
              'w-full h-[41px]': !isToggle,
            })}
          />
        </TooltipTrigger>
        <TooltipContent
          className="px-3 py-2 text-sm font-normal text-gray-100 bg-gray-700 rounded-lg shadow"
          side="right"
          align="center"
        >
          {itemAside.name}
        </TooltipContent>
      </Tooltip>
    </Link>
  );
};
export default ItemAside;
