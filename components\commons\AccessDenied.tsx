import { BASE_PATH } from '@/constants';
import Image from 'next/image';
import { FormattedMessage } from 'react-intl';

export default function AccessDenied() {
  return (
    <div className="w-[376px] mx-auto flex flex-col justify-center items-center gap-2">
      <Image
        src={`${BASE_PATH}/icons/403-forbidden.svg`}
        alt="403"
        width={200}
        height={200}
        unoptimized
        priority
        className="object-contain"
      />
      <div className="flex flex-col gap-2 items-center">
        <span className="text-base font-semibold text-gray-700 text-center">
          <FormattedMessage
            defaultMessage="Không có quyền truy cập tính năng"
            id="components.commons.AccessDenied.2035504993"
          />
        </span>
        <span className="text-sm text-center text-gray-500">
          <FormattedMessage
            defaultMessage="Rất tiếc, hiện tại bạn không có quyền truy cập vào bất kỳ tính năng nào trong ứng dụng này. Vui lòng liên hệ với quản trị viên để kiểm tra lại quyền truy cập."
            id="components.commons.AccessDenied.1247917498"
          />
        </span>
      </div>
    </div>
  );
}
