import { useMemo } from 'react';
import { ApplicationsClient } from './applicationsClient';
import { AuthClient } from './authClient';
import { createDynamicClient } from './createDynamicClient';
import { Applications, GroupUser, Roles, Users } from './dataType';
import { FileConfig, FileConfigClient } from './fileConfigClient';
import { GroupUsersClient } from './groupUsersClient';
import { RolesClient } from './rolesClient';
import { UsersClient } from './usersClient';

export const useCreateClient = () => {
  const rolesClient = useMemo(() => new RolesClient(), []);
  const applicationsClient = useMemo(() => new ApplicationsClient(), []);
  const usersClient = useMemo(() => new UsersClient(), []);
  const groupUsersClient = useMemo(() => new GroupUsersClient(), []);
  const authClient = useMemo(() => new AuthClient(), []);
  const fileConfigClient = useMemo(() => new FileConfigClient(), []);

  const roles = useMemo(() => createDynamicClient<Roles, RolesClient>(rolesClient), [rolesClient]);

  const applications = useMemo(
    () => createDynamicClient<Applications, ApplicationsClient>(applicationsClient),
    [applicationsClient],
  );

  const users = useMemo(() => createDynamicClient<Users, UsersClient>(usersClient), [usersClient]);

  const groupUsers = useMemo(
    () => createDynamicClient<GroupUser, GroupUsersClient>(groupUsersClient),
    [groupUsersClient],
  );

  const auth = useMemo(() => createDynamicClient<any, AuthClient>(authClient), [authClient]);

  const fileConfig = useMemo(
    () => createDynamicClient<FileConfig, FileConfigClient>(fileConfigClient),
    [fileConfigClient],
  );

  return { roles, applications, users, groupUsers, auth, fileConfig };
};
