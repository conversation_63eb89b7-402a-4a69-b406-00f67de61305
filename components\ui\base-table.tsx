import { BASE_PATH, QueryOperations } from '@/constants';
import Image from 'next/image';
import { FormattedMessage } from 'react-intl';
import { DefaultRecordType, Spin, Table, TableProps } from 'ui-components';

function BaseTable<T extends DefaultRecordType>({
  className,
  columns,
  dataSource = [],
  pagination,
  rowKey,
  loading,
  striped = 'column',
  tableLayout = 'auto',
  classNameRoot,
  ...propsTable
}: TableProps<T> & {
  loading?: boolean;
  dataSource: readonly T[];
  classNameRoot?: string;
}) {
  const query = new URLSearchParams(window.location.search);
  const isQuery = query.size > 0;
  return (
    <Spin loading={loading!} className={classNameRoot}>
      <Table<T>
        columns={columns}
        data={dataSource}
        tableLayout={tableLayout}
        striped={striped}
        className={className}
        rowKey={rowKey}
        pagination={pagination}
        emptyText={
          <div className="flex p-5 h-[calc(100dvh_-_230px)] flex-col justify-center items-center gap-5 flex-1 self-stretch">
            <div className="flex flex-col items-center justify-center h-full">
              {
                isQuery && (
                  <div className="flex flex-col items-center">
                    <Image
                      src={`${BASE_PATH}/icons/empty-result.svg`}
                      width={200}
                      height={200}
                      alt="empty-result"
                      unoptimized
                      priority
                    />
                    <div className="flex flex-col gap-2 items-center max-w-[370px]">
                      <div className="text-gray-800 text-center text-lg font-semibold">
                        <FormattedMessage
                          defaultMessage="Không tìm thấy kết quả"
                          id="components.ui.base-table.1237119840"
                        />
                      </div>
                      <span className="text-sm text-gray-700 text-center font-normal">
                        {query.has(QueryOperations.SEARCH) && query.has(QueryOperations.FILTERS) ? (
                          <FormattedMessage
                            defaultMessage="Rất tiếc, không có kết quả nào phù hợp với từ khóa bạn đã tìm kiếm. Vui lòng kiểm tra lại từ khóa hoặc thử tìm kiếm với từ khóa khác."
                            id="components.ui.base-table.notEmptyResult"
                          />
                        ) : query.has(QueryOperations.FILTERS) ? (
                          <FormattedMessage
                            defaultMessage="Rất tiếc, không có kết quả nào phù hợp với bộ lọc bạn đã tìm kiếm. Vui lòng chọn lại bộ lọc."
                            id="components.ui.base-table.notEmptyResultWithFilter"
                          />
                        ) : (
                          <FormattedMessage
                            defaultMessage="Rất tiếc, không có kết quả nào phù hợp với từ khóa bạn đã tìm kiếm. Vui lòng kiểm tra lại từ khóa hoặc thử tìm kiếm với từ khóa khác."
                            id="components.ui.base-table.notEmptyResult"
                          />
                        )}
                      </span>
                    </div>
                  </div>
                )
                // : (
                //   <div className="flex flex-col items-center">
                //     <Image
                //       src={`${BASE_PATH}/images/commons/no-general-data.svg`}
                //       width={200}
                //       height={200}
                //       alt="no-general-data"
                //     />
                //     <div className="flex flex-col gap-2 self-stretch">
                //       <div className="text-gray-800 text-center text-lg font-semibold">
                //         <FormattedMessage
                //           defaultMessage="Chưa có tài khoản trong nhóm tài khoản"
                //           id="components.container.group-account.AccountGroupContainer.notAccountInGroupAccount"
                //         />
                //       </div>
                //       <div className="text-gray-700 text-center text-sm font-normal">
                //         <FormattedMessage
                //           defaultMessage="Thêm các tài khoản để dễ dàng quản lý và phân quyền truy cập."
                //           id="components.container.group-account.AccountGroupContainer.addAccForManagenmentAndAccess"
                //         />
                //       </div>
                //     </div>
                //   </div>
              }
            </div>
          </div>
        }
        {...propsTable}
      />
    </Spin>
  );
}

export { BaseTable };
