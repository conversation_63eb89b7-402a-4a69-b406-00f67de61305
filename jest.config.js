module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
  },
  testMatch: [
    '**/__tests__/**/*.(js)',
    '**/*.(test|spec).(js)'
  ],
  collectCoverageFrom: [
    'hook/**/*.{js}',
    'components/**/*.{js}',
    'utils/**/*.{js}',
    '!**/node_modules/**',
  ],
  coverageReporters: ['text', 'lcov', 'html'],
}
