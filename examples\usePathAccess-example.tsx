import React from 'react';
import { usePathAccess } from '../hook/usePathAccess';
import { useRouter } from 'next/router';

// Ví dụ sử dụng usePathAccess với module-based access
export const PathAccessExample: React.FC = () => {
  const router = useRouter();
  const {
    loading,
    unauthorized,
    firstAccessiblePath,
    accessiblePaths,
    canAccess,
    redirectIfUnauthorized,
    getModuleDefaultPath,
    getDefaultPathForCurrentModule,
    moduleDefaultPaths,
  } = usePathAccess();

  // Ví dụ 1: Kiể<PERSON> tra quyền truy cập đường dẫn hiện tại
  const handleCheckCurrentPath = () => {
    const currentPath = router.pathname;
    const hasAccess = canAccess(currentPath);
    console.log(`Quyền truy cập ${currentPath}:`, hasAccess);
  };

  // Ví dụ 2: Lấy đường dẫn mặc định cho module accounts
  const handleGetAccountsDefault = () => {
    const defaultPath = getModuleDefaultPath('accounts');
    console.log('Đường dẫn mặc định cho module accounts:', defaultPath);
    
    if (defaultPath) {
      router.push(defaultPath);
    }
  };

  // Ví dụ 3: Lấy đường dẫn mặc định cho module settings
  const handleGetSettingsDefault = () => {
    const defaultPath = getModuleDefaultPath('settings');
    console.log('Đường dẫn mặc định cho module settings:', defaultPath);
    
    if (defaultPath) {
      router.push(defaultPath);
    }
  };

  // Ví dụ 4: Lấy đường dẫn mặc định cho module hiện tại
  const handleGetCurrentModuleDefault = () => {
    const currentPath = router.pathname;
    const defaultPath = getDefaultPathForCurrentModule(currentPath);
    console.log(`Đường dẫn mặc định cho module hiện tại (${currentPath}):`, defaultPath);
    
    if (defaultPath) {
      router.push(defaultPath);
    }
  };

  // Ví dụ 5: Chuyển hướng nếu không có quyền
  const handleRedirectIfUnauthorized = () => {
    const currentPath = router.pathname;
    const redirectPath = redirectIfUnauthorized(currentPath);
    
    if (redirectPath) {
      console.log(`Chuyển hướng từ ${currentPath} đến ${redirectPath}`);
      router.push(redirectPath);
    } else {
      console.log(`Có quyền truy cập ${currentPath}`);
    }
  };

  // Ví dụ 6: Hiển thị tất cả đường dẫn mặc định của các module
  const displayModuleDefaults = () => {
    console.log('Đường dẫn mặc định cho các module:');
    moduleDefaultPaths.forEach((path, module) => {
      console.log(`- ${module}: ${path}`);
    });
  };

  if (loading) {
    return <div>Đang tải quyền truy cập...</div>;
  }

  if (unauthorized) {
    return <div>Bạn không có quyền truy cập bất kỳ trang nào.</div>;
  }

  return (
    <div style={{ padding: '20px' }}>
      <h2>Ví dụ sử dụng usePathAccess</h2>
      
      <div style={{ marginBottom: '20px' }}>
        <h3>Thông tin cơ bản:</h3>
        <p>Đường dẫn đầu tiên có quyền: {firstAccessiblePath}</p>
        <p>Tổng số đường dẫn có quyền: {accessiblePaths.length}</p>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Danh sách đường dẫn có quyền truy cập:</h3>
        <ul>
          {accessiblePaths.map((path) => (
            <li key={path}>
              <button onClick={() => router.push(path)}>{path}</button>
            </li>
          ))}
        </ul>
      </div>

      <div style={{ marginBottom: '20px' }}>
        <h3>Các chức năng mới:</h3>
        <button onClick={handleCheckCurrentPath} style={{ margin: '5px' }}>
          Kiểm tra quyền đường dẫn hiện tại
        </button>
        <button onClick={handleGetAccountsDefault} style={{ margin: '5px' }}>
          Đi đến trang mặc định của Accounts
        </button>
        <button onClick={handleGetSettingsDefault} style={{ margin: '5px' }}>
          Đi đến trang mặc định của Settings
        </button>
        <button onClick={handleGetCurrentModuleDefault} style={{ margin: '5px' }}>
          Đi đến trang mặc định của module hiện tại
        </button>
        <button onClick={handleRedirectIfUnauthorized} style={{ margin: '5px' }}>
          Chuyển hướng nếu không có quyền
        </button>
        <button onClick={displayModuleDefaults} style={{ margin: '5px' }}>
          Hiển thị đường dẫn mặc định các module
        </button>
      </div>

      <div>
        <h3>Đường dẫn mặc định cho các module:</h3>
        {Array.from(moduleDefaultPaths.entries()).map(([module, path]) => (
          <div key={module} style={{ margin: '5px 0' }}>
            <strong>{module}:</strong> {path}
            <button 
              onClick={() => router.push(path)} 
              style={{ marginLeft: '10px' }}
            >
              Đi đến
            </button>
          </div>
        ))}
      </div>
    </div>
  );
};

// Ví dụ sử dụng trong component khác
export const NavigationExample: React.FC = () => {
  const router = useRouter();
  const { getModuleDefaultPath, canAccess } = usePathAccess();

  const navigateToModule = (moduleName: string) => {
    const defaultPath = getModuleDefaultPath(moduleName);
    if (defaultPath && canAccess(defaultPath)) {
      router.push(defaultPath);
    } else {
      console.log(`Không có quyền truy cập module ${moduleName}`);
    }
  };

  return (
    <nav>
      <button onClick={() => navigateToModule('accounts')}>
        Quản lý tài khoản
      </button>
      <button onClick={() => navigateToModule('settings')}>
        Cài đặt
      </button>
      <button onClick={() => navigateToModule('monitoring-station')}>
        Trạm giám sát
      </button>
    </nav>
  );
};
