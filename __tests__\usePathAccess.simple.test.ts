import { renderHook, act } from '@testing-library/react';

// Mock store trước khi import hook
const mockAppStories = jest.fn();
jest.mock('../store', () => ({
  appStories: mockAppStories,
}));

// Mock constants
jest.mock('../constants/urlPathName', () => ({
  urlPathName: {
    ROOT: '',
    HOME: '/',
    NOT_PERMISSION: '/not-permission',
    ACCESS_DENIED: '/access-denied',
    ACCOUNT: {
      ROOT: '/accounts',
      GROUP_ACCOUNT: '/accounts/group',
      GROUP_ACCOUNT_ID: '/accounts/group/:id',
      AUTHORIZATION: '/accounts/authorization',
    },
    SETTINGS: {
      ROOT: '/settings',
      ATTACHMENT: '/settings/attachment',
      STORAGECAPACITY: '/settings/storage-capacity',
      DIGITALSIGNATURE: '/settings/digital-signature',
      PACKAGEMANAGEMENT: '/settings/package-management',
    },
    MON<PERSON>ORING_STATION: {
      ROOT: '/monitoring-station',
    },
    HEALTH: '/health',
    PROFILE: '/profile',
  },
  pathWithScopes: {
    '': 'general:read',
    '/': 'general:read',
    '/not-permission': '',
    '/access-denied': '',
    '/accounts': 'user:read',
    '/accounts/group': 'group:read',
    '/accounts/group/:id': 'group:read',
    '/accounts/authorization': 'role:read',
    '/settings': 'setting:read',
    '/settings/attachment': 'setting:read',
    '/settings/storage-capacity': 'setting:read',
    '/settings/digital-signature': 'setting:read',
    '/settings/package-management': 'setting:read',
    '/monitoring-station': 'monitoring:read',
    '/health': '',
    '/profile': '',
  },
}));

import { usePathAccess } from '../hook/usePathAccess';

describe('usePathAccess - Simple Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('🔐 Basic Access Control', () => {
    it('should allow access to public paths', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [], // No permissions
      });

      const { result } = renderHook(() => usePathAccess());

      // Public paths should always be accessible
      expect(result.current.canAccess('/access-denied')).toBe(true);
      expect(result.current.canAccess('/profile')).toBe(true);
      expect(result.current.canAccess('/health')).toBe(true);
    });

    it('should deny access when user has no permissions', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [],
      });

      const { result } = renderHook(() => usePathAccess());

      expect(result.current.canAccess('/accounts')).toBe(false);
      expect(result.current.canAccess('/settings')).toBe(false);
      expect(result.current.canAccess('/monitoring-station')).toBe(false);
      expect(result.current.unauthorized).toBe(true);
    });

    it('should allow access when user has correct permissions', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'setting:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      expect(result.current.canAccess('/accounts')).toBe(true);
      expect(result.current.canAccess('/settings')).toBe(true);
      expect(result.current.canAccess('/monitoring-station')).toBe(false);
    });
  });

  describe('🏗️ Module-based Logic', () => {
    it('should return correct module default paths', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'setting:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      expect(result.current.getModuleDefaultPath('accounts')).toBe('/accounts');
      expect(result.current.getModuleDefaultPath('settings')).toBe('/settings');
      expect(result.current.getModuleDefaultPath('monitoring-station')).toBe(null);
    });

    it('should get accessible paths in module', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'group:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      const accountsPaths = result.current.getAccessiblePathsInModule('accounts');
      expect(accountsPaths).toContain('/accounts');
      expect(accountsPaths).toContain('/accounts/group');
      expect(accountsPaths).not.toContain('/accounts/authorization');
    });
  });

  describe('🎯 Smart Redirect Logic', () => {
    it('should redirect to same module when possible', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'group:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      // User tries to access authorization but only has user/group permissions
      const redirectPath = result.current.redirectIfUnauthorized('/accounts/authorization');
      
      // Should redirect to accessible path in same module
      expect(['/accounts', '/accounts/group']).toContain(redirectPath);
    });

    it('should redirect to different module when no access in current module', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'setting:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // User tries to access accounts but only has settings permission
      const redirectPath = result.current.redirectIfUnauthorized('/accounts');
      
      expect(redirectPath).toBe('/settings');
    });

    it('should redirect to NOT_PERMISSION when no access anywhere', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [],
      });

      const { result } = renderHook(() => usePathAccess());

      const redirectPath = result.current.redirectIfUnauthorized('/accounts');
      
      expect(redirectPath).toBe('/not-permission');
    });

    it('should return null when user has access to current path', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      const redirectPath = result.current.redirectIfUnauthorized('/accounts');
      
      expect(redirectPath).toBe(null);
    });
  });

  describe('🔄 Navigation Logic', () => {
    it('should navigate to best path for module', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'group:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      const bestPath = result.current.getBestPathForModule('accounts');
      
      expect(bestPath).toBe('/accounts'); // Default path has priority
    });

    it('should return first available path when default is not accessible', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'group:read' }], // Only group permission
      });

      const { result } = renderHook(() => usePathAccess());

      const bestPath = result.current.getBestPathForModule('accounts');
      
      expect(bestPath).toBe('/accounts/group'); // First accessible path
    });

    it('should handle module navigation with fallback', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'setting:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Try to navigate to accounts but only have settings permission
      const navigationPath = result.current.navigateToModule('accounts');
      
      expect(navigationPath).toBe('/settings'); // Fallback to accessible module
    });
  });

  describe('🚨 Edge Cases', () => {
    it('should handle dynamic paths correctly', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'group:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Test dynamic path matching
      expect(result.current.canAccess('/accounts/group/123')).toBe(true);
      expect(result.current.canAccess('/accounts/group/abc')).toBe(true);
    });

    it('should handle null permissions gracefully', () => {
      mockAppStories.mockReturnValue({
        userPermissions: null,
      });

      const { result } = renderHook(() => usePathAccess());

      expect(result.current.unauthorized).toBe(true);
      expect(result.current.canAccess('/accounts')).toBe(false);
      expect(result.current.canAccess('/access-denied')).toBe(true); // Public path
    });

    it('should handle invalid module names', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      expect(result.current.getModuleDefaultPath('invalid-module')).toBe(null);
      expect(result.current.getBestPathForModule('invalid-module')).toBe(null);
    });
  });
});
