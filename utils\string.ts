// export const capitalizeFirstLetter = (str: string) =>
//   str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();
export const capitalizeFirstLetter = (str: string) => {
  if (!str) return str;

  // Split string by dot and handle multiple sentences
  return str
    .split('.')
    .map((sentence) => {
      // Trim each sentence and capitalize first letter
      const trimmed = sentence.trim();
      return trimmed ? trimmed.charAt(0).toUpperCase() + trimmed.slice(1).toLowerCase() : '';
    })
    .join('. '); // Join with dot and space
};

export const replaceAll = (str: string, find: string, replace: string) => str.replace(new RegExp(find, 'g'), replace);

export const removeAccents = (str: string) => str.normalize('NFD').replace(/[\u0300-\u036f]/g, '');

export const removeVietnameseTones = (str: string) => {
  str = str.replace(/à|á|ạ|ả|ã|â|ầ|ấ|ậ|ẩ|ẫ|ă|ằ|ắ|ặ|ẳ|ẵ/g, 'a'); //a
  str = str.replace(/è|é|ẹ|ẻ|ẽ|ê|ề|ế|ệ|ể|ễ/g, 'e'); //e
  str = str.replace(/ì|í|ị|ỉ|ĩ/g, 'i'); //i
  str = str.replace(/ò|ó|ọ|ỏ|õ|ô|ồ|ố|ộ|ổ|ỗ|ơ|ờ|ớ|ợ|ở|ỡ/g, 'o'); //o
  str = str.replace(/ù|ú|ụ|ủ|ũ|ư|ừ|ứ|ự|ử|ữ/g, 'u'); //u
  str = str.replace(/ỳ|ý|ỵ|ỷ|ỹ/g, 'y'); //y
  str = str.replace(/đ/g, 'd'); //d
  str = str.replace(/À|Á|Ạ|Ả|Ã|Â|Ầ|Ấ|Ậ|Ẩ|Ẫ|Ă|Ằ|Ắ|Ặ|Ẳ|Ẵ/g, 'A'); //A
  str = str.replace(/È|É|Ẹ|Ẻ|Ẽ|Ê|Ề|Ế|Ệ|Ể|Ễ/g, 'E'); //E
  str = str.replace(/Ì|Í|Ị|Ỉ|Ĩ/g, 'I'); //I
  str = str.replace(/Ò|Ó|Ọ|Ỏ|Õ|Ô|Ồ|Ố|Ộ|Ổ|Ỗ|Ơ|Ờ|Ớ|Ợ|Ở|Ỡ/g, 'O'); //O
  str = str.replace(/Ù|Ú|Ụ|Ủ|Ũ|Ư|Ừ|Ứ|Ự|Ử|Ữ/g, 'U'); //U
  str = str.replace(/Ỳ|Ý|Ỵ|Ỷ|Ỹ/g, 'Y'); //Y
  str = str.replace(/Đ/g, 'D'); //D
  return str;
};

export const matchDynamicPath = (pattern: string, actual: string): boolean => {
  const patternSegments = pattern.split('/').filter(Boolean);
  const actualSegments = actual.split('/').filter(Boolean);

  if (patternSegments.length !== actualSegments.length) return false;

  for (let i = 0; i < patternSegments.length; i++) {
    if (patternSegments[i].startsWith(':')) continue; // dynamic segment
    if (patternSegments[i] !== actualSegments[i]) return false;
  }

  return true;
};
