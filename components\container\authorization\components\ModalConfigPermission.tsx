import { Applications, Permission } from '@/api/dataType';
import { X } from '@phosphor-icons/react';
import { useEffect, useMemo, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import {
  Badge,
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Form,
  TreeField,
} from 'ui-components';
import { useCreateTreePermission } from './useCreateTreePermission';
import ContentChangedBase from '@/components/commons/ContentChangedBase';

export default function ModalConfigPermission({
  defaultValues = [],
  open,
  onOpenChange,
  appData,
  onSubmit: onSubmitData,
  disabled,
}: {
  defaultValues: { key: string; id: string }[];
  appData: Applications;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (permissions: Permission[]) => void;
  disabled?: boolean;
}) {
  const intl = useIntl();
  const [form] = Form.useForm();
  const listPermission = Form.useWatch('permissions', form) ?? [];
  const treePermission = useCreateTreePermission(appData.permissions ?? []);
  const [isChangedData, setIsChangedData] = useState(false);
  const [showConfirm, setShowConfirm] = useState(false);

  const initialValues = useMemo(() => {
    return {
      permissions: defaultValues?.map((item) => item.key) ?? [],
    };
  }, [defaultValues]);

  const treeData = useMemo(() => {
    return treePermission?.map((item) => ({
      ...item,
      disabled: disabled,
      children: item.children?.map((child) => ({
        ...child,
        disabled: disabled,
      })),
    }));
  }, [treePermission, disabled]);

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues]);

  const totalCheck = useMemo(() => {
    return (
      treePermission?.filter(
        (item) =>
          listPermission?.includes(item.value) || item.children?.some((child) => listPermission.includes(child.value)),
      )?.length ?? 0
    );
  }, [listPermission, treePermission]);

  const handleSubmit = async (values: { permissions: string[] }) => {
    if (disabled) return;
    const permissions = values.permissions;
    const data = appData?.permissions?.filter((per) => permissions.includes(per.keyName)) ?? [];
    onSubmitData(data);
    onOpenChange(false);
    setIsChangedData(false);
    return;
  };

  const handleClose = () => {
    if (isChangedData) {
      setShowConfirm(true);
      return;
    }
    onOpenChange(false);
    form.resetFields();
  };

  return (
    <>
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="flex flex-col p-0 border-none gap-0 max-w-[500px] max-h-[calc(100vh_-_60px)] sm:rounded-xl overflow-hidden">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <Form
              name="permissions-data-form"
              form={form}
              initialValues={initialValues}
              onFinish={handleSubmit}
              className="flex flex-col flex-1 min-h-0"
              onFieldsChange={(_, allFields) => {
                const isValid = allFields.some((field) => field.touched);
                setIsChangedData(isValid);
              }}
            >
              <div className="flex-shrink-0 flex flex-row gap-3 items-center p-[12px_12px_12px_24px] border-b border-gray-200">
                <span className="flex-1 min-w-0 truncate font-semibold text-lg text-gray-800">{appData.name}</span>
                <div className="flex flex-row gap-3 flex-shrink-0">
                  {!disabled && (
                    <Button type="submit">
                      <FormattedMessage
                        defaultMessage="Lưu cấu hình"
                        id="components.container.authorization.components.ConfigApplication.1043319462"
                      />
                    </Button>
                  )}
                  <Button variant="gray" icon type="button" onClick={handleClose}>
                    <X size={20} className="text-current" weight="regular" />
                  </Button>
                </div>
              </div>
              <div className="flex-1 min-h-0 flex flex-col w-full">
                <div className="min-h-0 flex-1 w-full p-4 bg-gray-100 flex">
                  <div className="rounded-xl flex-1 min-h-0 w-full bg-white">
                    <div className="h-full w-full overflow-auto p-4">
                      <div className="flex flex-col gap-3">
                        <div className="flex flex-row w-full justify-between">
                          <span className="font-semibold text-base text-gray-700">
                            <FormattedMessage
                              defaultMessage="Danh sách tính năng truy cập"
                              id="components.container.authorization.components.ConfigApplication.709701327"
                            />
                          </span>
                          <Badge variant={totalCheck ? 'default' : 'secondary'} className="shadow-none text-sm">
                            {totalCheck}/{treeData.length}
                          </Badge>
                        </div>
                        <Form.Field name="permissions">
                          {(control) => {
                            return (
                              <TreeField
                                data={treeData ?? []}
                                value={control.value ?? []}
                                onChange={control.onChange}
                              />
                            );
                          }}
                        </Form.Field>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Form>
          </DialogContent>
        </DialogPortal>
      </Dialog>
      <Dialog open={showConfirm} onOpenChange={setShowConfirm}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="flex flex-col p-0 border-none gap-0 max-w-[420px] max-h-[calc(100vh_-_60px)] sm:rounded-xl overflow-hidden">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onCancel={() => {
                setShowConfirm(false);
              }}
              onSubmit={() => {
                onOpenChange(false);
                setIsChangedData(false);
                setShowConfirm(false);
                form.resetFields();
              }}
              heading={intl.formatMessage({
                id: 'components.container.authorization.components.ModalConfigPermission.2035227936',
                defaultMessage: 'Hủy lưu cấu hình',
              })}
              description={intl.formatMessage({
                id: 'components.container.authorization.components.ModalConfigPermission.641274112',
                defaultMessage:
                  'Bạn có chắc muốn hủy lưu cấu hình? Các thông tin mà bạn vừa thiết lập sẽ không được lưu lại.',
              })}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
}
