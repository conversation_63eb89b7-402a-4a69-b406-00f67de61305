'use client';

import IconCommon from '@/components/commons/IconLayout';
import { messageErrorCommon } from '@/constants/defineMessages';
import { PasswordStrength, Strength } from '@/utils/check-password-strength';
import { cn } from '@/utils/tailwind';
import { usePathname } from 'next/navigation';
import { Fragment, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Alert, FormInstance, FormItem, Input } from 'ui-components';

const ShowStrengthPassword = ({ form, isOpenChangePass }: { form: FormInstance<any>; isOpenChangePass?: boolean }) => {
  const intl = useIntl();
  const [visible, setVisible] = useState(false);
  const [showPass, setShowPass] = useState({
    isShowPass: false,
    isShowConfirmPass: false,
  });
  const [checkPassStrength, setCheckPasStrength] = useState<Strength>();
  const pathname = usePathname();
  const isProfilePage = pathname.includes('profile');

  const handleChangePass = (value: string) => {
    if (value.length >= 8 && value.length <= 256) {
      setVisible(true);
      setCheckPasStrength(PasswordStrength(value));
    } else {
      setVisible(false);
      setCheckPasStrength(undefined);
    }
  };

  return (
    <Fragment>
      <div className="flex items-start flex-col gap-2 self-stretch">
        <div className="items-center space-x-2">
          <span className="text-sm text-gray-800 font-medium">
            {isOpenChangePass ? (
              <FormattedMessage
                defaultMessage="Mật khẩu mới"
                id="components.container.accounts.CheckPassword.9893c634"
              />
            ) : (
              <FormattedMessage defaultMessage="Mật khẩu" id="components.container.accounts.CheckPassword.6b8826d8" />
            )}
          </span>
          <span className="text-sm text-red-500 font-medium">*</span>
        </div>
        <FormItem
          name="password"
          rules={[
            {
              required: true,
              message: intl.formatMessage(
                messageErrorCommon.fieldRequired,
                isOpenChangePass
                  ? {
                      field: intl.formatMessage({
                        defaultMessage: 'Mật khẩu mới',
                        id: 'components.container.accounts.CheckPassword.9893c634',
                      }),
                    }
                  : {
                      field: intl.formatMessage({
                        defaultMessage: 'Mật khẩu',
                        id: 'components.container.accounts.CheckPassword.6b8826d8',
                      }),
                    },
              ),
            },
            {
              min: 8,
              message: intl.formatMessage(messageErrorCommon.fieldMin, {
                field: intl.formatMessage({
                  defaultMessage: 'Mật khẩu',
                  id: 'components.container.accounts.CheckPassword.6b8826d8',
                }),
                min: 8,
              }),
            },
            {
              max: 256,
              message: intl.formatMessage(messageErrorCommon.fieldMax, {
                max: 256,
              }),
            },
          ]}
          renderItem={({ control, meta, form, isError }) => (
            <Input
              placeholder={
                isOpenChangePass
                  ? intl.formatMessage({
                      defaultMessage: 'Nhập mật khẩu mới',
                      id: 'components.container.accounts.CheckPassword.9f77a595',
                    })
                  : intl.formatMessage({
                      defaultMessage: 'Nhập mật khẩu',
                      id: 'components.container.accounts.CheckPassword.49be255b',
                    })
              }
              type={showPass.isShowPass ? 'text' : 'password'}
              className="placeholder-gray-500 text-sm"
              onSubmitValue={control.onChange}
              variant={isError ? 'error' : 'default'}
              autoComplete="current-password"
              prefix={<IconCommon name="Lock" size={16} />}
              suffix={
                control.value && (
                  <IconCommon
                    name={`${showPass.isShowPass ? 'Eye' : 'EyeClosed'}`}
                    size={16}
                    onClick={() =>
                      setShowPass((prevState) => ({
                        ...prevState,
                        isShowPass: !prevState.isShowPass,
                      }))
                    }
                  />
                )
              }
              value={control.value ?? ''}
              {...control}
            />
          )}
        />
      </div>

      {isOpenChangePass && (
        <div className="flex flex-col items-start gap-2">
          <div className="items-center space-x-2">
            <span className="text-sm text-gray-800 font-medium">
              <FormattedMessage
                defaultMessage="Xác nhận mật khẩu mới"
                id="components.container.accounts.CheckPassword.confirmNewPassword"
              />
            </span>
            <span className="text-sm text-red-500 font-medium">*</span>
          </div>
          <FormItem
            name={'passwordConfirm'}
            rules={[
              {
                required: true,
                message: intl.formatMessage(messageErrorCommon.fieldRequired, {
                  field: intl.formatMessage({
                    defaultMessage: 'Xác nhận mật khẩu mới',
                    id: 'components.container.accounts.CheckPassword.confirmNewPassword',
                  }),
                }),
              },
              {
                validator(rule, value, callback) {
                  if (form.getFieldValue('password') && form.getFieldValue('password')?.trim() !== value.trim()) {
                    return Promise.reject(
                      new Error(
                        intl.formatMessage(messageErrorCommon.fieldPassNotFormat, {
                          field: intl.formatMessage({
                            defaultMessage: 'Xác nhận mật khẩu và mật khẩu mới',
                            id: 'components.container.accounts.CheckPassword.confirmPasswordAndNewPassNotMatch',
                          }),
                        }),
                      ),
                    );
                  }

                  return Promise.resolve();
                },
              },
            ]}
            // validateTrigger="onBlur"
            renderItem={({ control, meta, form, isError }) => {
              return (
                <Input
                  placeholder={intl.formatMessage({
                    defaultMessage: 'Nhập lại mật khẩu mới',
                    id: 'components.container.accounts.CheckPassword.re-EnterNewPassword',
                  })}
                  type={showPass.isShowConfirmPass ? 'text' : 'password'}
                  className="placeholder-gray-500 text-sm"
                  prefix={<IconCommon name="Lock" size={16} />}
                  variant={isError ? 'error' : 'default'}
                  autoComplete="current-password"
                  onSubmitValue={control.onChange}
                  suffix={
                    control.value && (
                      <IconCommon
                        name={`${showPass.isShowConfirmPass ? 'Eye' : 'EyeClosed'}`}
                        size={16}
                        onClick={() =>
                          setShowPass((prevState) => ({
                            ...prevState,
                            isShowConfirmPass: !prevState.isShowConfirmPass,
                          }))
                        }
                      />
                    )
                  }
                  value={control.value ?? ''}
                  {...control}
                />
              );
            }}
          />
        </div>
      )}
      <div
        className={cn('flex-wrap h-[185px]', {
          'h-auto': isProfilePage,
        })}
      >
        {visible && (
          <Alert
            className={`flex justify-center bg-${checkPassStrength?.color}-100 border-${checkPassStrength?.color}-400 !rounded-b-none`}
            isVisible={true}
          >
            <span className={`text-${checkPassStrength?.color}-700`}>
              {intl.formatMessage({
                defaultMessage: 'Mức độ bảo mật',
                id: 'components.container.accounts.CheckPassword.levelSecurity',
              })}
              :{' '}
              <FormattedMessage
                defaultMessage="{text}"
                id="components.container.authorization.components.DeletedRole.*********"
                values={{
                  text:
                    `${checkPassStrength?.value}` === 'Yếu' ? (
                      <FormattedMessage
                        defaultMessage="Yếu"
                        id="components.container.accounts.components.AccountAction.levelSecurityWeak"
                      />
                    ) : `${checkPassStrength?.value}` === 'Khá' ? (
                      <FormattedMessage
                        defaultMessage="Khá"
                        id="components.container.accounts.components.AccountAction.levelSecurityFair"
                      />
                    ) : (
                      <FormattedMessage
                        defaultMessage="Tốt"
                        id="components.container.accounts.components.AccountAction.levelSecurityGood"
                      />
                    ),
                }}
              />
            </span>
          </Alert>
        )}
        <div
          className={cn(
            `flex flex-col gap-1 p-3 rounded-lg w-full border
             ${visible ? `border-${checkPassStrength?.color}-400 !border-t-0 !rounded-t-none` : 'border-primary-500'}`,
          )}
        >
          <span className="text-sm font-medium text-gray-800">
            <FormattedMessage
              defaultMessage="Gợi ý đặt mật khẩu"
              id="components.container.accounts.CheckPassword.suggestionsForSetingPass"
            />
          </span>
          <span className="text-xs font-normal text-gray-700">
            <FormattedMessage
              defaultMessage="Để tăng mức độ an toàn, mật khẩu của bạn nên bao gồm:"
              id="components.container.accounts.CheckPassword.toEnhanceSecurity"
            />
          </span>
          <div className="flex flex-col">
            <div className="flex flex-row">
              <div className="flex flex-row gap-2 w-1/2 items-center">
                {checkPassStrength?.length! >= 8 ? (
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path
                      d="M18.1637 6.28839L8.16374 16.2884C8.07664 16.3758 7.97315 16.4451 7.85919 16.4925C7.74524 16.5398 7.62306 16.5641 7.49967 16.5641C7.37628 16.5641 7.25411 16.5398 7.14016 16.4925C7.0262 16.4451 6.92271 16.3758 6.83561 16.2884L2.46061 11.9134C2.3734 11.8262 2.30423 11.7227 2.25703 11.6087C2.20984 11.4948 2.18555 11.3727 2.18555 11.2493C2.18555 11.126 2.20984 11.0039 2.25703 10.8899C2.30423 10.776 2.3734 10.6725 2.46061 10.5853C2.54782 10.4981 2.65134 10.4289 2.76528 10.3817C2.87922 10.3345 3.00135 10.3102 3.12467 10.3102C3.248 10.3102 3.37012 10.3345 3.48406 10.3817C3.598 10.4289 3.70153 10.4981 3.78874 10.5853L7.50045 14.297L16.8372 4.96183C17.0133 4.78571 17.2522 4.68677 17.5012 4.68677C17.7503 4.68677 17.9892 4.78571 18.1653 4.96183C18.3414 5.13795 18.4404 5.37682 18.4404 5.62589C18.4404 5.87497 18.3414 6.11384 18.1653 6.28996L18.1637 6.28839Z"
                      fill="#3B82F6"
                    />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <circle cx="10" cy="10" r="4" fill="#A1A1AA" />
                  </svg>
                )}

                <span className="text-xs font-normal text-gray-700 w-full inline items-center">
                  <FormattedMessage
                    defaultMessage="Mật khẩu ít nhất 8 ký tự."
                    id="components.container.accounts.CheckPassword.atLeastEightCharacters"
                  />
                </span>
              </div>

              <div className="flex flex-row gap-2 w-1/2 items-center">
                {checkPassStrength?.contains.includes('uppercase') ? (
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <path
                      d="M18.1637 6.28839L8.16374 16.2884C8.07664 16.3758 7.97315 16.4451 7.85919 16.4925C7.74524 16.5398 7.62306 16.5641 7.49967 16.5641C7.37628 16.5641 7.25411 16.5398 7.14016 16.4925C7.0262 16.4451 6.92271 16.3758 6.83561 16.2884L2.46061 11.9134C2.3734 11.8262 2.30423 11.7227 2.25703 11.6087C2.20984 11.4948 2.18555 11.3727 2.18555 11.2493C2.18555 11.126 2.20984 11.0039 2.25703 10.8899C2.30423 10.776 2.3734 10.6725 2.46061 10.5853C2.54782 10.4981 2.65134 10.4289 2.76528 10.3817C2.87922 10.3345 3.00135 10.3102 3.12467 10.3102C3.248 10.3102 3.37012 10.3345 3.48406 10.3817C3.598 10.4289 3.70153 10.4981 3.78874 10.5853L7.50045 14.297L16.8372 4.96183C17.0133 4.78571 17.2522 4.68677 17.5012 4.68677C17.7503 4.68677 17.9892 4.78571 18.1653 4.96183C18.3414 5.13795 18.4404 5.37682 18.4404 5.62589C18.4404 5.87497 18.3414 6.11384 18.1653 6.28996L18.1637 6.28839Z"
                      fill="#3B82F6"
                    />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                    <circle cx="10" cy="10" r="4" fill="#A1A1AA" />
                  </svg>
                )}

                <span className="text-xs font-normal text-gray-700 w-full inline items-center">
                  <FormattedMessage
                    defaultMessage="Ít nhất 1 chữ cái in hoa."
                    id="components.container.accounts.CheckPassword.atLeastOneUppercaseLetter"
                  />
                </span>
              </div>
            </div>

            <div className="flex flex-row gap-2 w-1/2 items-center">
              {checkPassStrength?.contains.includes('number') || checkPassStrength?.contains.includes('symbol') ? (
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <path
                    d="M18.1637 6.28839L8.16374 16.2884C8.07664 16.3758 7.97315 16.4451 7.85919 16.4925C7.74524 16.5398 7.62306 16.5641 7.49967 16.5641C7.37628 16.5641 7.25411 16.5398 7.14016 16.4925C7.0262 16.4451 6.92271 16.3758 6.83561 16.2884L2.46061 11.9134C2.3734 11.8262 2.30423 11.7227 2.25703 11.6087C2.20984 11.4948 2.18555 11.3727 2.18555 11.2493C2.18555 11.126 2.20984 11.0039 2.25703 10.8899C2.30423 10.776 2.3734 10.6725 2.46061 10.5853C2.54782 10.4981 2.65134 10.4289 2.76528 10.3817C2.87922 10.3345 3.00135 10.3102 3.12467 10.3102C3.248 10.3102 3.37012 10.3345 3.48406 10.3817C3.598 10.4289 3.70153 10.4981 3.78874 10.5853L7.50045 14.297L16.8372 4.96183C17.0133 4.78571 17.2522 4.68677 17.5012 4.68677C17.7503 4.68677 17.9892 4.78571 18.1653 4.96183C18.3414 5.13795 18.4404 5.37682 18.4404 5.62589C18.4404 5.87497 18.3414 6.11384 18.1653 6.28996L18.1637 6.28839Z"
                    fill="#3B82F6"
                  />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
                  <circle cx="10" cy="10" r="4" fill="#A1A1AA" />
                </svg>
              )}

              <span className="text-xs font-normal text-gray-700 w-full inline items-center">
                <FormattedMessage
                  defaultMessage="Ít nhất 1 chữ số hoặc ký tự đặc biệt."
                  id="components.container.accounts.CheckPassword.atLeastOneNumberOrSpecialCharacter"
                />
              </span>
            </div>
          </div>
        </div>
      </div>
    </Fragment>
  );
};

export { ShowStrengthPassword };
