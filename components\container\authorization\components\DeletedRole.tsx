import { SortEnum } from '@/api/type';
import { useCreateClient } from '@/api/useCreateClient';
import ActionBaseContent from '@/components/commons/ActionBaseContent';
import { Label } from '@/components/commons/LabelText';
import { messageAlertCommon } from '@/constants/defineMessages';
import { capitalizeFirstLetter } from '@/utils/string';
import { useQueryClient } from '@tanstack/react-query';
import { useMemo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { Combobox, Form, FormItem, OptionItemType, toast } from 'ui-components';

type IProp = {
  onClose?: () => void;
  data?: Record<string, any>;
};

export default function DeletedRole({ onClose, data }: IProp) {
  const queryClient = useQueryClient();
  const [form] = Form.useForm();
  const intl = useIntl();
  const isFoundUser = data?.users?.length > 0;
  const { roles } = useCreateClient();
  const { data: listRoles, isLoading } = roles.find();
  const optionRoles: OptionItemType[] = useMemo(() => {
    if (isLoading) return [];
    return (
      listRoles?.data?.list
        ?.map((item: any) => ({
          title: item.name[intl.locale],
          value: item.id,
        }))
        .filter((item: any) => item.value !== data?.id) ?? []
    );
  }, [isLoading]);

  const { mutateAsync: deleteRoleAsync, isPending } = roles.deleteOrReplaceRole({
    invalidateQueries: [
      { enable: true, queryKey: ['UsersClient', 'find'], refetchType: 'all' },
      { enable: true, queryKey: ['RolesClient', 'find'], refetchType: 'all' },
    ],
  });

  const onSubmitForm = async (value: { roleReplacedId: string }) => {
    try {
      await deleteRoleAsync([data?.id, value.roleReplacedId ?? '']);
      const title = messageAlertCommon.deleteSuccess;
      toast({
        title: `${capitalizeFirstLetter(
          intl.formatMessage(title, {
            type: intl.formatMessage({
              defaultMessage: 'nhóm quyền',
              id: 'components.container.authorization.components.BaseFormData.1859212970',
            }),
          }),
        )}`,
        type: 'success',
        options: {
          position: 'top-center',
        },
      });
      queryClient.invalidateQueries({
        queryKey: ['UsersClient'],
        refetchType: 'all',
      });
    } catch (error) {
      console.error({ error });
      const title = messageAlertCommon.deleteFail;
      toast({
        title: `${capitalizeFirstLetter(
          intl.formatMessage(title, {
            type: intl.formatMessage({
              defaultMessage: 'nhóm quyền',
              id: 'components.container.authorization.components.BaseFormData.1859212970',
            }),
          }),
        )}`,
        type: 'error',
        options: {
          position: 'top-center',
        },
      });
    }
    onClose?.();
  };

  return (
    <ActionBaseContent onSubmit={() => form.submit()} onCancel={onClose} disabled={isPending}>
      <div className="flex flex-col gap-5">
        <div className="flex flex-col gap-1 items-center justify-center">
          <span className="text-center font-semibold text-lg leading-[27px] text-gray-800">
            <FormattedMessage
              defaultMessage="Xác nhận xóa nhóm quyền"
              id="components.container.authorization.components.DeletedRole.1144690065"
            />
          </span>
          <span className="text-center font-normal text-sm leading-[21px] text-gray-700">
            <FormattedMessage
              defaultMessage="{text}"
              id="components.container.authorization.components.DeletedRole.663325355"
              values={{
                text: isFoundUser ? (
                  <FormattedMessage
                    defaultMessage="Bạn chắc chắn muốn xóa nhóm quyền này? Hành động này sẽ xóa vĩnh
                viễn nhóm quyền và ảnh hưởng đến tất cả các tài khoản thuộc nhóm
                quyền này. Tất cả quyền hạn của các tài khoản sẽ bị thu hồi. Vui làm
                xác nhận nếu bạn muốn tiếp tục."
                    id="components.container.authorization.components.DeletedRole.236479752"
                  />
                ) : (
                  <FormattedMessage
                    defaultMessage="Bạn chắc chắn muốn xóa nhóm quyền này? Hành động này sẽ xóa vĩnh viễn nhóm quyền khỏi hệ thống. Vui lòng xác nhận nếu bạn muốn tiếp tục."
                    id="components.container.authorization.components.DeletedRole.1031901772"
                  />
                ),
              }}
            />
          </span>
        </div>
        <Form name="role-delete-form" form={form} initialValues={{ roleReplacedId: '' }} onFinish={onSubmitForm}>
          {isFoundUser && (
            <div className="w-full h-full p-3 rounded-xl flex flex-col gap-3 border border-gray-200 bg-gray-50">
              <Label
                label={
                  <FormattedMessage
                    defaultMessage="Chuyển các tài khoản đến nhóm quyền khác"
                    id="components.container.authorization.components.DeletedRole.419042780"
                  />
                }
                required
                className="font-medium text-sm"
              />
              <FormItem
                name="roleReplacedId"
                rules={[
                  {
                    required: true,
                    message: intl.formatMessage({
                      defaultMessage: 'Vui lòng chọn nhóm quyền khác cho các tài khoản trực thuộc nhóm quyền này.',
                      id: 'components.container.authorization.components.DeletedRole.1297653463',
                    }),
                  },
                ]}
                renderItem={({ control, meta, isError }) => (
                  <Combobox
                    modalPopover
                    placeholder={intl.formatMessage({
                      defaultMessage: 'Chọn nhóm quyền khác',
                      id: 'components.container.authorization.components.DeletedRole.2087098543',
                    })}
                    options={optionRoles ?? []}
                    variant={isError ? 'error' : 'default'}
                    value={control.value}
                    onChange={control.onChange}
                    disabled={isLoading}
                    sideOffset={-250}
                    customValue={(value, options) => {
                      const title = options?.find((item) => item.value === value)?.title;
                      return (
                        <span
                          className="text-gray-700 text-sm font-normal leading-[1.31rem] text-left truncate"
                          title={title}
                        >
                          {title}
                        </span>
                      );
                    }}
                  />
                )}
              />
            </div>
          )}
        </Form>
      </div>
    </ActionBaseContent>
  );
}
