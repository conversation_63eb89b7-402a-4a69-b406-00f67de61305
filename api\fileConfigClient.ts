import { BACKEND_API_ENDPOINT } from '@/constants';
import apiPathName from './apiPathName';
import { BaseClient } from './baseClient';
import { ApplicationFileType, LangType } from './dataType';
import { DataResponse } from './type';

export type FileConfig = {
  id: string;
  value: string;
  unit: string;
  status: boolean;
  createdAt: string;
  updatedAt: string;
};

export type FileType = {
  id: string
  extensions: string;
  description: LangType;
  status: boolean;
  type: LangType;
  applicationIds: string[];
  appUploadPermissions: Record<string, boolean>;
  createdAt: string;
  updatedAt: string;
  applications: ApplicationFileType[];
};

export type AppPermissionItem = {
  applicationId: string;
  enabled: boolean;
}

export class FileConfigClient extends BaseClient<FileConfig> {
  moduleName = 'FileConfigClient';
  constructor() {
    super(BACKEND_API_ENDPOINT + apiPathName.storage.root);
  }

  getFileConfig(): Promise<DataResponse<FileConfig[]>> {
    return this.api.get('/get-file-config');
  }

  getFileType(): Promise<DataResponse<FileType[]>> {
    return this.api.get('/get-file-type');
  }

  updateFileConfig(id: string, data: Record<string, any>): Promise<DataResponse<FileConfig>> {
    return this.api.put(`/${id}/update-file-config`, data);
  }

  uploadPermissionApp(payload: {
    fileTypeId: string,
    applicationId: string,
    enabled: boolean,
  }): Promise<void> {
    return this.api.patch('/upload-permission-app', payload);
  }

  updateFileTypeStatus(payload: {
    fileTypeId: string,
    status: boolean,
  }): Promise<void> {
    return this.api.patch('/update-file-type-status', payload);
  }

  updateFileTypeMultipleApp(payload: {
    fileTypeId: string,
    permissions: AppPermissionItem[];
  }): Promise<void> {
    return this.api.patch('/update-file-type-multiple-app', payload);
  }
}
