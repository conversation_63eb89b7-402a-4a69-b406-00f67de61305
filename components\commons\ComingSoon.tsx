'use client';

import { BASE_PATH } from '@/constants';
import Image from 'next/image';
import { FormattedMessage } from 'react-intl';

export const ComingSoon = () => {
  return (
    <div className="relative flex flex-col justify-center items-center gap-2">
      <Image
        src={`${BASE_PATH}/icons/coming-soon.svg`}
        alt="coming-soon-image"
        width={200}
        height={200}
        unoptimized
        priority
        className="h-[200px] w-[200px] object-contain"
      />
      <span className="text-base text-gray-500 font-normal">
        <FormattedMessage
          defaultMessage="Tính năng đang được phát triển, dự kiến sẽ ra mắt trong thời gian sắp tới."
          id="components.commons.ComingSoon.1405629260"
        />
      </span>
    </div>
  );
};
