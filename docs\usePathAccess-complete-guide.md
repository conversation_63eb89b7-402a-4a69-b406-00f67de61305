# usePathAccess Hook - Complete Guide

## Tổng quan tính năng mới

Hook `usePathAccess` đã được cập nhật với 2 tính năng chính:

### 1. 🏗️ Module-based Path Access
- Nhóm paths theo modules (accounts, settings, monitoring-station)
- Tự động tìm default accessible path cho mỗi module
- Smart redirect trong cùng module

### 2. ⏳ In-Progress Loading States
- Theo dõi loading state cho từng path
- Async processing với simulation delay
- Batch processing nhiều paths
- C<PERSON> kết quả để tối ưu performance

## API Reference

### Core Functions

```typescript
const {
  // === Existing APIs ===
  loading,                    // boolean - main loading state
  canAccess,                 // (path: string) => boolean - sync check
  redirectIfUnauthorized,    // (path: string) => string | null
  accessiblePaths,          // string[] - all accessible paths
  
  // === Module-based APIs ===
  moduleDefaultPaths,        // Map<string, string> - default paths per module
  getModuleDefaultPath,      // (module: string) => string | null
  getDefaultPathForCurrentModule, // (path: string) => string | null
  
  // === Loading APIs ===
  pathLoadingState,         // Map<string, boolean> - loading state per path
  isPathLoading,           // (path: string) => boolean - check if path is loading
  canAccessWithLoading,    // async (path: string, delay?: number) => Promise<boolean>
  canAccessMultiple,       // async (paths: string[], delay?: number) => Promise<Map<string, boolean>>
} = usePathAccess();
```

## Quick Start Examples

### 1. Basic Module Navigation

```typescript
const Navigation = () => {
  const { getModuleDefaultPath } = usePathAccess();
  const router = useRouter();
  
  const goToModule = (module: string) => {
    const defaultPath = getModuleDefaultPath(module);
    if (defaultPath) router.push(defaultPath);
  };
  
  return (
    <nav>
      <button onClick={() => goToModule('accounts')}>Accounts</button>
      <button onClick={() => goToModule('settings')}>Settings</button>
    </nav>
  );
};
```

### 2. Loading States

```typescript
const PathChecker = () => {
  const { canAccessWithLoading, isPathLoading } = usePathAccess();
  
  const checkPath = async (path: string) => {
    const hasAccess = await canAccessWithLoading(path, 500); // 500ms delay
    console.log(`${path}: ${hasAccess ? 'Accessible' : 'Denied'}`);
  };
  
  return (
    <button 
      onClick={() => checkPath('/accounts')}
      disabled={isPathLoading('/accounts')}
    >
      Check Access {isPathLoading('/accounts') && '🔄'}
    </button>
  );
};
```

### 3. Batch Processing

```typescript
const BatchChecker = () => {
  const { canAccessMultiple } = usePathAccess();
  
  const checkAll = async () => {
    const paths = ['/accounts', '/settings', '/monitoring-station'];
    const results = await canAccessMultiple(paths, 300);
    
    results.forEach((hasAccess, path) => {
      console.log(`${path}: ${hasAccess ? '✅' : '❌'}`);
    });
  };
  
  return <button onClick={checkAll}>Check All Paths</button>;
};
```

## Advanced Usage

### Smart Route Guard

```typescript
const RouteGuard = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const { 
    loading, 
    canAccess, 
    redirectIfUnauthorized,
    getDefaultPathForCurrentModule 
  } = usePathAccess();

  useEffect(() => {
    if (!loading) {
      const currentPath = router.pathname;
      
      if (!canAccess(currentPath)) {
        // Try to redirect to default path in same module first
        const moduleDefault = getDefaultPathForCurrentModule(currentPath);
        const redirectPath = moduleDefault || redirectIfUnauthorized(currentPath);
        
        if (redirectPath) {
          router.replace(redirectPath);
        }
      }
    }
  }, [loading, router.pathname]);

  if (loading) return <div>Loading permissions...</div>;
  return <>{children}</>;
};
```

### Loading-aware Navigation Menu

```typescript
const NavigationMenu = () => {
  const { 
    canAccessWithLoading, 
    isPathLoading, 
    getModuleDefaultPath 
  } = usePathAccess();
  const router = useRouter();
  
  const navigateWithCheck = async (module: string) => {
    const defaultPath = getModuleDefaultPath(module);
    if (!defaultPath) return;
    
    const hasAccess = await canAccessWithLoading(defaultPath, 200);
    if (hasAccess) {
      router.push(defaultPath);
    } else {
      alert('Access denied');
    }
  };
  
  const modules = ['accounts', 'settings', 'monitoring-station'];
  
  return (
    <nav>
      {modules.map(module => {
        const defaultPath = getModuleDefaultPath(module);
        const isLoading = defaultPath ? isPathLoading(defaultPath) : false;
        
        return (
          <button
            key={module}
            onClick={() => navigateWithCheck(module)}
            disabled={isLoading}
            className={isLoading ? 'loading' : ''}
          >
            {module} {isLoading && '🔄'}
          </button>
        );
      })}
    </nav>
  );
};
```

## Components Library

### PathLoadingSpinner

```typescript
import { PathLoadingSpinner } from '../components/PathLoadingSpinner';

<PathLoadingSpinner 
  path="/accounts" 
  size="medium" 
  type="spinner" 
/>
```

### PathAccessButton

```typescript
import { PathAccessButton } from '../components/PathLoadingSpinner';

<PathAccessButton
  path="/accounts"
  onAccessGranted={() => router.push('/accounts')}
  onAccessDenied={() => alert('Access denied')}
>
  Go to Accounts
</PathAccessButton>
```

### LoadingOverview

```typescript
import { LoadingOverview } from '../components/PathLoadingSpinner';

// Shows floating overview of all loading paths
<LoadingOverview />
```

## Best Practices

### 1. Performance Optimization

```typescript
// ✅ Good: Use sync check for immediate feedback
const hasAccess = canAccess('/accounts');

// ✅ Good: Use async check for better UX with loading
const hasAccess = await canAccessWithLoading('/accounts', 300);

// ❌ Avoid: Too many individual async checks
// Instead use batch processing:
const results = await canAccessMultiple(['/accounts', '/settings']);
```

### 2. Error Handling

```typescript
const safeCheckAccess = async (path: string) => {
  try {
    return await canAccessWithLoading(path);
  } catch (error) {
    console.error('Access check failed:', error);
    return canAccess(path); // Fallback to sync check
  }
};
```

### 3. Loading State Management

```typescript
// ✅ Good: Check loading state before making requests
if (!isPathLoading(path)) {
  const result = await canAccessWithLoading(path);
}

// ✅ Good: Show loading indicators
{isPathLoading(path) && <LoadingSpinner />}

// ✅ Good: Disable actions during loading
<button disabled={isPathLoading(path)}>
  Navigate {isPathLoading(path) && '🔄'}
</button>
```

## Migration Guide

### From Basic to Module-based

```typescript
// Before
const { canAccess, redirectIfUnauthorized } = usePathAccess();

// After - add module support
const { 
  canAccess, 
  redirectIfUnauthorized,
  getModuleDefaultPath,           // New
  getDefaultPathForCurrentModule  // New
} = usePathAccess();
```

### From Sync to Async with Loading

```typescript
// Before - synchronous
const hasAccess = canAccess('/accounts');

// After - asynchronous with loading
const hasAccess = await canAccessWithLoading('/accounts');
const isLoading = isPathLoading('/accounts');
```

## Configuration

### Module Paths Configuration

Update `MODULE_PATHS` in the hook to add new modules:

```typescript
const MODULE_PATHS = {
  accounts: [
    '/accounts',           // First = default priority
    '/accounts/group',
    '/accounts/authorization',
  ],
  settings: [
    '/settings',           // First = default priority
    '/settings/attachment',
    // ... other settings paths
  ],
  // Add new modules here
  reports: [
    '/reports',
    '/reports/analytics',
  ],
};
```

### Loading Delays

```typescript
// Quick check (200ms)
await canAccessWithLoading(path, 200);

// Standard check (500ms)
await canAccessWithLoading(path, 500);

// Slow check (1000ms) - for complex permissions
await canAccessWithLoading(path, 1000);
```

## Troubleshooting

### Common Issues

1. **Loading never completes**: Check for JavaScript errors in console
2. **Memory leaks**: All timeouts are automatically cleaned up
3. **Concurrent requests**: Same path requests are automatically deduplicated
4. **Cache issues**: Cache is cleared when user permissions change

### Debug Mode

```typescript
// Enable debug logging
const { pathLoadingState } = usePathAccess();

useEffect(() => {
  console.log('Current loading states:', pathLoadingState);
}, [pathLoadingState]);
```

This complete guide covers all aspects of the updated `usePathAccess` hook with both module-based access and in-progress loading functionality!
