import { cn } from '@/utils/tailwind';
import { Wrench } from '@phosphor-icons/react';
import Image from 'next/image';
import { FormattedMessage } from 'react-intl';
import { Button, Form, FormInstance, Separator, Switch } from 'ui-components';
import { useState } from 'react';
import { Applications } from '@/api/dataType';
import ModalConfigPermission from './ModalConfigPermission';

const ConfigApplication = ({
  image,
  index,
  appData,
  disabled,
}: {
  image: string;
  index: number;
  appData: Applications;
  disabled?: boolean;
}) => {
  const [openPermission, setOpenPermission] = useState(false);
  return (
    <>
      <div key={`applications-${index}`} className="flex flex-row items-center justify-between gap-3 p-3">
        <div className="flex flex-row items-center gap-2">
          <Image src={image} alt="logo" width={30} height={30} unoptimized priority className="h-[30px] w-[30px]" />
          <Form.Field name={[index, 'name']}>
            {({ value }) => <span className="text-sm font-normal text-gray-800">{value}</span>}
          </Form.Field>
        </div>
        <div className="group overflow-hidden h-[38px] flex flex-row border border-gray-200 rounded-lg [&>*:nth-child(n):not(:first-child)]:border-l [&>*:nth-child(n):not(:first-child)]:border-l-gray-200">
          <div className="flex flex-row items-center">
            <div className="flex flex-row items-center gap-2 p-2">
              <Form.Field name={[index, 'allowWeb']}>
                {({ value, onChange }) => <Switch size="sm" checked={value} onCheckedChange={onChange} disabled={disabled} />}
              </Form.Field>
              <span className="text-sm font-normal text-gray-700">Web</span>
            </div>
            <Separator orientation="vertical" className="bg-gray-200 h-5" />
            <div
              className={cn('flex flex-row items-center gap-2 p-2', { 'opacity-50': appData.signature === 'config' })}
            >
              <Form.Field name={[index, 'allowMobile']}>
                {({ value, onChange }) => (
                  <Switch
                    size="sm"
                    checked={value}
                    onCheckedChange={onChange}
                    disabled={appData.signature === 'config' || disabled}
                  />
                )}
              </Form.Field>
              <span className="text-sm font-normal text-gray-700">Mobile</span>
            </div>
          </div>
          <Button
            type="button"
            variant="gray"
            onClick={() => {
              setOpenPermission(true);
            }}
            className="h-full rounded-none flex flex-row items-center gap-2 py-2 px-4 active:bg-gray-100 bg-white hover:bg-gray-100 disabled:bg-white disabled:hover:bg-white"
          >
            <Wrench size={20} className="text-current" weight="regular" />
            <span className="text-current">
              <FormattedMessage
                defaultMessage="Cấu hình"
                id="components.container.authorization.components.BaseFormData.216090699"
              />
            </span>
          </Button>
        </div>
        <Form.Field name={[index, 'permissions']}>
          {(control) => {
            return (
              <ModalConfigPermission
                defaultValues={control?.value}
                open={openPermission}
                onOpenChange={setOpenPermission}
                appData={appData}
                onSubmit={(data) => {
                  const values = data?.map((item: any) => ({
                    key: item.keyName,
                    id: item.logtoId,
                  }));
                  control.onChange(values);
                }}
                disabled={disabled}
              />
            );
          }}
        </Form.Field>
      </div>
    </>
  );
};

export { ConfigApplication };
