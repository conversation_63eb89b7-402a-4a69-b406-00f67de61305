'use client';

import IconCommon from '@/components/commons/IconLayout';
import { cn } from '@/utils/tailwind';
import { Button } from 'ui-components';

interface HeaderModalProps {
  title: React.ReactElement;
  buttonForm?: {
    onAction?: () => void;
    content?: React.ReactNode | string;
    type?: 'button' | 'submit';
    isPending?: boolean;
  };
  onClose?: () => void;
  showButton?: boolean;
}
export function HeaderModal({ title, buttonForm, onClose, showButton = true }: HeaderModalProps) {
  return (
    <div className="flex w-full h-[64px] pl-6 py-3 pr-3 rounded-t-xl items-center gap-3 flex-shrink-0 border-b border-b-gray-200 bg-white">
      <div className="flex flex-col justify-center items-center gap-1 flex-grow flex-shrink-0 basis-0">
        <div className="text-lg font-semibold text-gray-800 self-stretch">{title}</div>
      </div>
      <Button
        onClick={buttonForm?.onAction}
        className={cn({ hidden: !showButton })}
        type={buttonForm?.type}
        disabled={buttonForm?.isPending}
      >
        {buttonForm?.content}
      </Button>
      <Button
        icon
        variant="neutral"
        className="bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-100"
        onClick={onClose}
        type="button"
      >
        <IconCommon name="X" className="text-current" />
      </Button>
    </div>
  );
}
