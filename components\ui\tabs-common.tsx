import { cn } from '@/utils/tailwind';

type dataTag = {
  label: string | React.ReactNode;
  value: React.Key;
  disabled?: boolean;
};

const TagWithStateBorder = ({
  dataTag,
  actionTag,
  onSwitchTag,
  className,
  classNames,
}: {
  dataTag: dataTag[];
  actionTag: React.Key;
  onSwitchTag: (tag: React.Key) => void;
  className?: string;
  classNames?: {
    item: string;
  };
}) => {
  return (
    <div className={cn('h-[52px] border-b justify-start items-start inline-flex', className)}>
      {dataTag.map((tag) => {
        const isActive = tag.value === actionTag;
        return (
          <div
            key={tag.value}
            aria-disabled={tag.disabled}
            onClick={() => {
              if (tag.disabled) return;
              onSwitchTag(tag.value);
            }}
            className={cn(
              'h-[52px] p-4 border-b-2 border-transparent justify-center items-center inline-flex cursor-pointer transition-all duration-300',
              { 'border-primary-500': isActive },
              { 'opacity-50 cursor-not-allowed': tag.disabled },
              classNames?.item,
            )}
          >
            <div
              className={cn('text-center text-gray-700 text-sm font-semibold', {
                'text-primary-500': isActive,
              })}
            >
              <div className="flex gap-1.5">{tag.label}</div>
            </div>
          </div>
        );
      })}
    </div>
  );
};
const TagWithStateTransition = ({
  dataTag,
  actionTag,
  onSwitchTag,
  className,
}: {
  dataTag: dataTag[];
  actionTag: React.Key;
  onSwitchTag: (tag: React.Key) => void;
  className?: string;
}) => {
  return (
    <div
      className={cn(
        'flex flex-row text-center rounded-lg bg-neutral-100 p-1 h-full w-max shadow-[0px_2px_6px_0px_rgba(0,0,0,0.10)_inset]',
        className,
      )}
    >
      {dataTag.map((tag) => {
        const isActive = tag.value === actionTag;
        return (
          <div
            key={tag.value}
            onClick={() => onSwitchTag(tag.value)}
            className={cn(
              'px-3 py-2 w-auto h-9 flex justify-center items-center',
              'text-sm text-gray-700 font-medium whitespace-nowrap block',
              'cursor-pointer',
              'transition-all',
              isActive && 'h-full w-full shadow-[0px_1px_4px_0px_rgba(0,0,0,0.20)] bg-white rounded-lg font-semibold',
            )}
          >
            {tag.label}
          </div>
        );
      })}
    </div>
  );
};

export { TagWithStateBorder, TagWithStateTransition };
