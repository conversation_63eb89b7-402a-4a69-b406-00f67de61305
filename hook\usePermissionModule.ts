import { appStories } from '@/store';
import { useMemo } from 'react';

type EnumLike = { [key: string]: string };

type ExtractActions<E extends EnumLike> = {
  [K in keyof E]: boolean;
};

export const usePermissionModule = <E extends EnumLike>(module: string, actionsEnum: E): ExtractActions<E> => {
  const { userPermissions } = appStories((state) => state);

  return useMemo(() => {
    const result = {} as ExtractActions<E>;
    if (!userPermissions || !module) return result;

    const permissionKeys = userPermissions.map((p) => p.keyName);

    (Object.keys(actionsEnum) as (keyof E)[]).forEach((key) => {
      const fullPermission = actionsEnum[key]; // e.g. 'config:users:create'
      const [, mod] = fullPermission.split(':'); // extract module part
      if (mod === module) {
        result[key] = permissionKeys.includes(fullPermission);
      } else {
        result[key] = false;
      }
    });

    return result;
  }, [module, userPermissions, actionsEnum]);
};
