'use client';

import { GroupUser } from '@/api/dataType';
import { useCreateClient } from '@/api/useCreateClient';
import ActionBaseContent from '@/components/commons/ActionBaseContent';
import { messageAlertCommon } from '@/constants/defineMessages';
import { capitalizeFirstLetter } from '@/utils/string';
import { useQueryClient } from '@tanstack/react-query';
import { usePathname, useRouter } from 'next/navigation';
import { FormattedMessage, useIntl } from 'react-intl';
import { toast } from 'ui-components';

type DeleteGroupAccountProps = {
  onClose?: () => void;
  dataRow?: GroupUser;
};

export function DeleteGroupAccount({ onClose, dataRow }: DeleteGroupAccountProps) {
  const intl = useIntl();
  const pathname = usePathname();
  const router = useRouter();

  const mutationOptions = {
    invalidateQueries: {
      enable: true,
    },
  };

  const { groupUsers } = useCreateClient();
  const { mutateAsync: deleted, isPending: isPendingDelete } = 
  groupUsers.deleteGroupById(mutationOptions)

  const handleSubmit = async () => {
    try {
      const data = await deleted(dataRow?.id!);
      const checkPathname = pathname.startsWith('/accounts/group')
      if (checkPathname && data.data.length > 0) {
        router.push(`/accounts/group/${data.data[0].id}`)
      } else {
        router.push('/accounts');
      }
      const title = messageAlertCommon.deleteSuccess;
        toast({
          title: `${capitalizeFirstLetter(
            intl.formatMessage(title, {
              type: intl.formatMessage({
                defaultMessage: 'nhóm tài khoản',
                id: 'components.container.group-account.components.GroupAccountForm.accountGroupLower',
              }),
            }),
          )}`,
          type: 'success',
          options: {
            position: 'top-center',
          },
        });
        onClose?.()
    } catch (error) {
      const title = messageAlertCommon.deleteFail;
        toast({
          title: `${capitalizeFirstLetter(
            intl.formatMessage(title, {
              type: intl.formatMessage({
                defaultMessage: 'nhóm tài khoản',
                id: 'components.container.group-account.components.GroupAccountForm.accountGroupLower',
              }),
            }),
          )}`,
          type: 'error',
          options: {
            position: 'top-center',
          },
        });
      onClose?.()
    }
  }

  return (
    <ActionBaseContent
      onSubmit={handleSubmit}
      onCancel={onClose}
      disabled={isPendingDelete}
    >
      <div className="flex flex-col gap-1 items-center justify-center">
        <span className="text-center font-semibold text-lg leading-[27px] text-gray-800">
          <FormattedMessage
            defaultMessage="Xác nhận xóa nhóm tài khoản"
            id="components.container.group-account.DeleteGroupAccount.confirmGroupAccountDelete"
          />
        </span>
        <span className="text-center font-normal text-sm leading-[21px] text-gray-700">
          <FormattedMessage
            defaultMessage="Bạn chắc chắn muốn xóa nhóm tài khoản này? Hành động này sẽ xóa vĩnh viễn nhóm tài khoản và không ảnh hưởng đến tài khoản trong nhóm. Vui lòng xác nhận nếu bạn muốn tiếp tục."
            id="components.container.group-account.DeleteGroupAccount.areYouSureDeleteGroupAccount"
          />
        </span>
      </div>
    </ActionBaseContent>
  );
}
