'use client';

import { useMemo, useRef, useState } from 'react';
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from 'ui-components';
import { FormattedMessage, useIntl } from 'react-intl';
import { BASE_PATH, QueryOperations } from '@/constants';
import Image from 'next/image';
import { CaretDown, IdentificationCard, Trash, UsersThree } from '@phosphor-icons/react';
import { DeleteGroupAccount } from './components/DeleteGroupAccount';
import { GroupAccountForm } from './components/GroupAccountForm';
import { useCreateClient } from '@/api/useCreateClient';
import { FilterStatusEnum, SortEnum } from '@/api/type';
import { messageStatusCommon } from '@/constants/defineMessages';
import { cn } from '@/utils/tailwind';
import HeaderFilter from '@/components/commons/HeaderFilter';
import { GroupUser, Users } from '@/api/dataType';
import { FilterDataType, FormValuesQuery, SortDataType } from '@/components/commons/HeaderFilter/types';
import UserAccountPage from '../accounts/UserAccountPage';
import { useFilterOrSearch } from '@/hook/useFilterOrSearch';
import { FilterAndSortProps } from '../accounts/AccountContainer';
import { useSearchParams } from 'next/navigation';
import { usePermissionModule } from '@/hook/usePermissionModule';
import { moduleScope, Scopes } from '@/constants/scopes';

export default function AccountGroupContainer({ groupId }: { groupId?: string }) {
  const intl = useIntl();
  const query = useSearchParams();
  const recordAccRef = useRef<GroupUser>(null);
  const [isOpenAccountForm, setIsOpenAccountForm] = useState(false);
  const [isChangedData, setIsChangeData] = useState(false);
  const [isOpenConfirm, setIsOpenConfirm] = useState(false);
  const [isOpenDeleteGroupAccount, setIsOpenDeleteGroupAccount] = useState(false);
  const actionUserGroup = usePermissionModule(moduleScope.USER_GROUPS, Scopes.GroupUserConfigScopes);

  const { groupUsers } = useCreateClient();
  const { data: dataGroupUsers, isLoading } = groupUsers.getById(groupId!);

  const dataUserInGroup = (dataGroupUsers?.data as unknown as GroupUser) || [];

  const locale = intl.locale as 'vi' | 'en';

  const dynamicTitle = locale === 'vi' ? dataUserInGroup?.name?.vi : dataUserInGroup?.name?.en;

  const handleDeleteGroupAccount = async (group: GroupUser) => {
    if (!actionUserGroup.DELETE) return;
    setIsOpenDeleteGroupAccount(true);
    recordAccRef.current = group;
  };

  const { roles } = useCreateClient();

  const { data: dataRoles } = roles.find();

  const optionsRoles = useMemo(() => {
    return (
      dataRoles?.data?.list.map((item) => ({
        title: `${locale === 'vi' ? item.name.vi : item.name.en}`,
        value: item.id,
      })) ?? []
    );
  }, [dataRoles]);

  const filterData: FilterDataType<Users>[] = useMemo(() => {
    return [
      {
        key: 'roles',
        title: (
          <div className="flex flex-row items-center gap-2 text-current">
            <IdentificationCard className="flex-shrink-0 text-current" size={20} weight="regular" />
            <span className="text-current">
              <FormattedMessage
                defaultMessage="Lọc theo nhóm quyền"
                id="components.commons.FilterAndSort.filterByRole"
              />
            </span>
          </div>
        ),
        options: optionsRoles,
      },
      {
        key: 'isActive',
        title: (
          <div className="flex flex-row items-center gap-2 text-current">
            <IdentificationCard className="flex-shrink-0 text-current" size={20} weight="regular" />
            <span className="text-current">
              <FormattedMessage
                defaultMessage="Lọc theo trạng thái"
                id="components.commons.FilterAndSort.filterByStatus"
              />
            </span>
          </div>
        ),
        options: [
          {
            value: FilterStatusEnum.ACTIVATE,
            title: intl.formatMessage(messageStatusCommon.activate),
          },
          {
            value: FilterStatusEnum.INACTIVATE,
            title: intl.formatMessage(messageStatusCommon.disable),
          },
        ],
      },
    ];
  }, [optionsRoles]);

  const sortData: SortDataType<Users>[] = [
    {
      key: 'createdAt',
      title: (
        <FormattedMessage
          defaultMessage="Sắp xếp theo thời gian tạo"
          id="components.commons.FilterAndSort.sortByCreateTime"
        />
      ),
      defaultValue: SortEnum.DESC,
      options: [
        {
          value: SortEnum.DESC,
          title: intl.formatMessage({
            defaultMessage: 'Mới nhất đến cũ nhất',
            id: 'components.commons.FilterAndSort.newestToOldest',
          }),
        },
        {
          value: SortEnum.ASC,
          title: intl.formatMessage({
            defaultMessage: 'Cũ nhất đến mới nhất',
            id: 'components.commons.FilterAndSort.oldestToNewest',
          }),
        },
      ],
    },
    {
      key: 'username',
      title: (
        <FormattedMessage
          defaultMessage="Sắp xếp theo tên tài khoản"
          id="components.commons.FilterAndSort.sortByNameAccount"
        />
      ),
      defaultValue: SortEnum.ASC,
      options: [
        {
          value: SortEnum.ASC,
          title: intl.formatMessage({
            defaultMessage: 'Từ A -> Z',
            id: 'components.commons.FilterAndSort.sortByFromAZ',
          }),
        },
        {
          value: SortEnum.DESC,
          title: intl.formatMessage({
            defaultMessage: 'Từ Z -> A',
            id: 'components.commons.FilterAndSort.sortByFromZA',
          }),
        },
      ],
    },
  ];

  const {
    filters,
    page,
    search,
    sort,
    setPage,
    getDefaultFiltersArray,
    setFilters,
    setSort,
    getDefaultSort,
    setSearch,
    getDefaultSortObj,
  } = useFilterOrSearch({
    filterData,
    sortData,
  });

  const dataFilterAndSortProps: FilterAndSortProps = {
    filters: filters,
    page: page,
    search: search,
    sort: sort,
    setPage: setPage,
    getDefaultFiltersArray: () => getDefaultFiltersArray,
    setFilters: setFilters,
    setSort: setSort,
    getDefaultSort: getDefaultSort,
    setSearch: setSearch,
    getDefaultSortObj: getDefaultSortObj,
  };

  const onOpenChange = (open: boolean) => {
    if (isChangedData) {
      setIsOpenConfirm(true);
    } else {
      setIsOpenAccountForm(open);
    }
  };

  const onSubmitForm = async (values: FormValuesQuery<Users>) => {
    setPage('1');

    if (values.filters.length > 0) {
      const isDefault = values.filters.every((item) => {
        const defaultFilter = getDefaultFiltersArray?.find((d) => d.key === item.key);
        if (!defaultFilter) return false;

        const defaultValue = defaultFilter.value || [];
        return JSON.stringify(item.value) === JSON.stringify(defaultValue);
      });
      if (!isDefault) {
        await setFilters(values.filters as any);
      } else {
        await setFilters(null);
      }
    } else {
      await setFilters(null);
    }

    // Update sort - check if it's different from default
    if (values.sort.key && values.sort.value) {
      const [key, value] = getDefaultSort?.split('.') || [];
      const isDefault = key.length > 0 && value.length > 0 && values.sort.key === key && values.sort.value === value;

      if (!isDefault) {
        await setSort({
          key: values.sort.key,
          value: values.sort.value,
        } as any);
      } else {
        await setSort(null);
      }
    } else {
      await setSort(null);
    }
  };

  return (
    <>
      <div className="flex-1 flex flex-col bg-white">
        <div
          className={cn(
            'h-[64px] bg-white flex-shrink-0 px-5 py-3 flex flex-row justify-between items-center gap-5 border-b border-gray-200',
          )}
        >
          <Tooltip>
            <TooltipTrigger asChild>
              <span className={cn('truncate text-lg leading-[21px] font-medium text-gray-800')}>{dynamicTitle}</span>
            </TooltipTrigger>
            <TooltipContent
              className="px-3 py-2 text-sm font-normal text-gray-100 bg-gray-700 rounded-lg shadow max-w-[300px]"
              side="bottom"
              align="start"
            >
              <span className="text-white break-all">{dynamicTitle}</span>
            </TooltipContent>
          </Tooltip>

          <div className="flex gap-5">
            {dataUserInGroup?.users?.length > 0 && groupId && (
              <HeaderFilter<Users>
                renderElement={[
                  {
                    type: 'TOOL_BAR',
                    config: {
                      isSearch: true,
                      operations: {
                        filters: filterData as FilterDataType<Users>[],
                        sort: sortData as SortDataType<Users>[],
                      },
                      defaultValues: {
                        search,
                        filters: filters!,
                        sort: sort!,
                      },
                      onSubmit: onSubmitForm,
                      onSubmitSearch: (value: string) => {
                        setTimeout(() => {
                          setSearch(value);
                          setPage('1');
                        }, 500);
                      },
                      isActiveFilter: query.has(QueryOperations.FILTERS),
                      isActiveSort: query.has(QueryOperations.SORT),
                    },
                  },
                ]}
              />
            )}
            {(actionUserGroup.UPDATE || actionUserGroup.DELETE) && (
              <Popover>
                <PopoverTrigger
                  onClick={(event) => event.stopPropagation()}
                  className="flex justify-center items-center text-center"
                  asChild
                >
                  <Button className="flex flex-row gap-2 text-white">
                    <UsersThree size={20} className="text-current" />
                    <span className="font-medium text-current">
                      <FormattedMessage
                        defaultMessage="Cập nhật"
                        id="components.container.authorization.components.BaseFormData.1416668261"
                      />
                    </span>
                    <div className="border-r border-white h-5"></div>
                    <CaretDown size={18} weight="regular" className="text-white font-semibold" />
                  </Button>
                </PopoverTrigger>
                <PopoverContent
                  align="center"
                  side="left"
                  sideOffset={-180}
                  className="bg-white w-auto min-w-[260px] flex flex-col rounded-xl p-2"
                >
                  {actionUserGroup.UPDATE && (
                    <div
                      className="flex flex-row items-center text-gray-700 gap-2 cursor-pointer p-2 hover:bg-gray-100 hover:rounded-lg"
                      onClick={() => {
                        if (!actionUserGroup.UPDATE) return;
                        setIsOpenAccountForm(true);
                      }}
                    >
                      <UsersThree size={20} className="text-current" />
                      <span className="text-sm font-normal text-current">
                        <FormattedMessage
                          defaultMessage="Cập nhật nhóm tài khoản"
                          id="components.container.account-managenment.SiderbarAccountManagement.updateGroupAccount"
                        />
                      </span>
                    </div>
                  )}
                  {actionUserGroup.DELETE && (
                    <div
                      className="flex flex-row items-center gap-2 text-red-500 cursor-pointer p-2 hover:bg-gray-100 hover:rounded-lg"
                      onClick={() => handleDeleteGroupAccount(dataUserInGroup)}
                    >
                      <Trash size={20} className="text-current" />
                      <span className="text-sm font-normal text-current">
                        <FormattedMessage
                          defaultMessage="Xóa nhóm tài khoản"
                          id="components.container.account-managenment.SiderbarAccountManagement.deleteGroupAccount"
                        />
                      </span>
                    </div>
                  )}
                </PopoverContent>
              </Popover>
            )}
          </div>
        </div>
        <div className="flex h-[calc(100vh_-_64px)]">
          {isLoading ? null : dataUserInGroup?.users?.length === 0 && groupId ? (
            <div className="flex p-5 h-[calc(100dvh_-_230px)] flex-col justify-center items-center gap-5 flex-1 self-stretch">
              <div className="flex w-full flex-col items-center gap-5">
                <div className="flex flex-col items-center">
                  <Image
                    src={`${BASE_PATH}/images/commons/no-general-data.svg`}
                    width={200}
                    height={200}
                    unoptimized
                    priority
                    alt="no-general-data"
                  />
                  <div className="flex flex-col gap-2 self-stretch max-w-[370px]">
                    <div className="text-gray-800 text-center text-lg font-semibold">
                      <FormattedMessage
                        defaultMessage="Chưa có tài khoản trong nhóm tài khoản"
                        id="components.container.group-account.AccountGroupContainer.notAccountInGroupAccount"
                      />
                    </div>
                    <div className="text-gray-700 text-center text-sm font-normal">
                      {actionUserGroup.UPDATE ? (
                        <FormattedMessage
                          defaultMessage="Thêm các tài khoản để dễ dàng quản lý và phân quyền truy cập."
                          id="components.container.group-account.AccountGroupContainer.addAccForManagenmentAndAccess"
                        />
                      ) : (
                        <FormattedMessage
                          defaultMessage="Nhóm này hiện chưa có tài khoản nào. Bạn không có quyền thêm các tài khoản."
                          id="components.container.group-account.AccountGroupContainer.**********"
                        />
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <UserAccountPage groupId={groupId!} dataFilterAndSortProps={dataFilterAndSortProps} />
          )}
        </div>
      </div>
      <Dialog open={isOpenAccountForm} onOpenChange={onOpenChange}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[900px] max-h-[calc(100vh_-_100px)]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <GroupAccountForm
              onClose={() => {
                setIsOpenAccountForm(false);
                setIsChangeData(false);
              }}
              dataGroupAccount={dataUserInGroup}
              isChangedData={isChangedData}
              setIsChangedData={setIsChangeData}
              isOpenConfirm={isOpenConfirm}
              setIsOpenConfirm={setIsOpenConfirm}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
      <Dialog open={isOpenDeleteGroupAccount}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[380px]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <DeleteGroupAccount
              dataRow={recordAccRef.current!}
              onClose={() => {
                setIsOpenDeleteGroupAccount(false);
              }}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
}
