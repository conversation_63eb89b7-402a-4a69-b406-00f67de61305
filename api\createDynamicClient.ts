import { getNumArgs } from '@/utils';
import {
  InvalidateQueryFilters,
  keepPreviousData,
  UndefinedInitialDataOptions,
  useMutation,
  UseMutationResult,
  useQuery,
  useQueryClient,
  UseQueryResult,
} from '@tanstack/react-query';
import { BaseClient } from './baseClient';
import { props } from './helper';
import { ExtractClientMethods } from './type';

type MutationInvalidateQuery = { enable: boolean } & InvalidateQueryFilters;

export type MutationOptions = {
  invalidateQueries?: MutationInvalidateQuery | MutationInvalidateQuery[];
};

export function createDynamicClient<T, TClient extends BaseClient<T>>(client: TClient) {
  // Extract client methods
  type ClientMethods = ExtractClientMethods<TClient>;

  type QueryMethodNames = 'find' | 'getById' | `get${string}` | `list${string}` | `fetch${string}`;

  type IsQueryMethod<T extends string> = T extends QueryMethodNames ? true : false;

  type HasLengthGreaterThanOne<T extends (...args: any[]) => any> =
    Parameters<T> extends [any, ...[any, ...any[]]] ? true : false;

  type Params<TMethod extends (...args: any[]) => Promise<any>> =
    HasLengthGreaterThanOne<TMethod> extends true ? [...Parameters<TMethod>] : Parameters<TMethod>[0];

  type MethodReturnType<TMethod extends (...args: any[]) => Promise<any>, TMethodName extends string> =
    IsQueryMethod<TMethodName> extends true
      ? UseQueryResult<Awaited<ReturnType<TMethod>>>
      : UseMutationResult<Awaited<ReturnType<TMethod>>, Error, Params<TMethod>>;

  type FunctionOption = (options?: Partial<UndefinedInitialDataOptions>) => void;

  type WithOptions<T extends (...args: any[]) => Promise<any>> = [...Parameters<T>, ...Parameters<FunctionOption>];
  // Utility to create query or mutation hooks
  function createMethodHooks<TMethod extends (...args: any[]) => Promise<any>>(
    methodName: string,
    methodImpl: TMethod,
  ) {
    const isQueryMethod =
      ['find'].includes(methodName) ||
      methodName.startsWith('get') ||
      methodName.startsWith('find') ||
      methodName.startsWith('fetch');

    if (isQueryMethod) {
      return (...allArgs: WithOptions<TMethod>) => {
        // eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
        const currentFunc: Function = client.constructor.prototype[methodName];
        const paramsLength = getNumArgs(currentFunc);
        const args = allArgs.slice(0, paramsLength);
        const options = allArgs[paramsLength];

        return useQuery({
          queryKey: [client.moduleName, methodName, ...args],
          queryFn: () => methodImpl(...args),
          enabled: args.every((arg) => {
            if (typeof arg === 'string') return arg.length > 0;
            return arg !== null && arg !== undefined;
          }),
          placeholderData: keepPreviousData,
          ...options,
        });
      };
    } else {
      return (options?: MutationOptions) => {
        const queryClient = useQueryClient();
        return useMutation({
          mutationFn: (args: Parameters<TMethod>) => (Array.isArray(args) ? methodImpl(...args) : methodImpl(args)),
          throwOnError: false,
          onSuccess() {
            const invalidateQueries = options?.invalidateQueries
              ? Array.isArray(options?.invalidateQueries)
                ? [...options.invalidateQueries]
                : [options.invalidateQueries]
              : [];

            invalidateQueries.forEach(({ enable, queryKey, refetchType, ...rest }) => {
              if (enable) {
                queryClient.invalidateQueries({
                  queryKey: queryKey ?? [client.moduleName],
                  refetchType: refetchType ?? 'all',
                  ...rest,
                });
              }
            });
          },
        });
      };
    }
  }

  // Generate hooks for all methods
  const methodKeys = props(client).filter(
    (key) => typeof client[key as keyof TClient] === 'function',
  ) as (keyof ClientMethods)[];

  const hooks = methodKeys
    .filter((key) => {
      const excludes = [
        'api',
        'constructor',
        'hasOwnProperty',
        'isPrototypeOf',
        'propertyIsEnumerable',
        'toString',
        'valueOf',
        'toLocaleString',
      ];
      return !excludes.includes(key as string) && !key.toString().startsWith('_');
    })
    .reduce(
      (acc, methodName) => {
        const method = client[methodName] as (...args: any[]) => Promise<any>;

        acc[methodName] = createMethodHooks(methodName as string, method.bind(client));
        return acc;
      },
      {} as Record<keyof ClientMethods, any>,
    );

  return hooks as {
    [K in keyof ClientMethods]: IsQueryMethod<K & string> extends true
      ? (...args: WithOptions<ClientMethods[K]>) => MethodReturnType<ClientMethods[K], K & string>
      : (options?: MutationOptions) => MethodReturnType<ClientMethods[K], K & string>;
  };
}
