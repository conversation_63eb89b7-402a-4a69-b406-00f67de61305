{"components": {"Layout": {"LeftAside": {"194521447": "Application", "238411488": "Logout", "563257617": "Language:", "languageError": "An error occurred while changing language."}, "dataItemAside": {"1128754901": "User", "**********": "Setting", "**********": "Monitoring station", "*********": "Overview"}}, "commons": {"AccessDenied": {"**********": "Sorry, you currently do not have access to any features in this application. Please contact the administrator to verify your access rights.", "**********": "Access to this feature is denied"}, "ComingSoon": {"**********": "Feature is under development, expected to be released in the near future."}, "ContentChangedBase": {"*********": "Confirm", "*********": "Cancel"}, "FilterAndSort": {"applyfilteringAndSortingAccount": "Apply", "filterByRole": "Filter by role", "filterByStatus": "Filter by status", "filteringAndSortingAccount": "Filter & Sort", "newestToOldest": "Newest to oldest", "oldestToNewest": "Oldest to newest", "resetfilteringAndSortingAccount": "<PERSON><PERSON><PERSON>", "sortByCreateTime": "Sort by creation time", "sortByFromAZ": "From A -> Z", "sortByFromZA": "From Z -> A", "sortByNameAccount": "Sort by account name"}, "HeaderFilter": {"FilterSortContent": {"**********": "No data available to filter content.", "9716647": "There is not enough information in the current data to show this filter."}, "filtering": "Filter", "sorting": "Sort"}, "PaginationTable": {"**********": "Showing <strong>{value}</strong> of <strong>{total}</strong>"}, "SessionExpiredAlert": {"**********": "For security reasons, the system will log out your account from all devices. Please log in again to continue using the application.", "**********": "Your account information has been updated"}}, "container": {"account-managenment": {"SiderbarAccountManagement": {"*********": "Create group", "deleteGroupAccount": "Delete account group", "updateGroupAccount": "Update account group"}, "UserGroupNavBar": {"**********": "There are currently no account groups to manage. You do not have permission to create new account group."}, "dataSiderbar": {"*********": "Role", "**********": "Account management", "**********": "Account"}}, "accounts": {"AccountContainer": {"*********": "Create account", "**********": "Account"}, "CheckPassword": {"49be255b": "Enter password", "6b8826d8": "Password", "9893c634": "New password", "9f77a595": "Enter new password", "atLeastEightCharacters": "At least 8 characters.", "atLeastOneNumberOrSpecialCharacter": "At least 1 number or special character.", "atLeastOneUppercaseLetter": "At least 1 uppercase letter.", "confirmNewPassword": "Confirm new password", "confirmPasswordAndNewPassNotMatch": "Confirm password and new password", "enterCurrentPassword": "Enter current password", "levelSecurity": "Security level", "passwordCurrent": "Current password", "re-EnterNewPassword": "Re-enter new password", "suggestionsForSetingPass": "Suggestions for setting password", "toEnhanceSecurity": "To enhance security, your password should include:"}, "UserAccountPage": {"0ed1adb9": "Disable account", "153f7b5e": "Account name", "18f6fa07": "Activate account", "466e9033": "Status", "7fdbdb0d": "Change account password", "97d565ed": "Delete account", "accountGroup": "Account group", "ad8125d8": "Last login", "addAccToEasilyManage ": "Add accounts to easily manage and assign access permissions.", "c207467b": "Email", "eb9aedb7": "Role", "pleaseCreateGroupAccount": "Please create new account group to easily manage and assign account permissions."}, "components": {"AccountAction": {"areYouSureCancelChangePassAccount": "Are you sure you want to cancel changing password for this account? The information you just entered will not be saved.", "areYouSureCancelNewCreateAccount": "Are you sure you want to cancel new account creation? The information you just entered will not be saved.", "areYouSureCancelUpdateAccount": "Are you sure you want to cancel the account information update? The information you just entered will not be saved.", "cancelChangePassAccount": "Cancel change password", "cancelNewCreateAccount": "Cancel new account creation.", "cancelUpdateInformationAccount": "Cancel updating account information.", "donotDeleteAccountAction": "Account cannot be deleted.", "donotDeleteLastAdminAccountAction": "Last admin account cannot be deleted", "donotDeleteLastAdminDescription": "Sorry, you cannot delete the last admin account. To ensure stable and secure system operation, at least one admin account must be retained.", "donotDeleteMeAccount": "Sorry, you cannot delete your own account.", "donotDisableAccountAction": "Account cannot be disabled.", "donotDisableLastAdminAccountAction": "Last admin account cannot be disabled", "donotDisableLastAdminDescription": "Sorry, you cannot disable the last admin account. To ensure the system always has at least one active admin account, this action is not allowed.", "donotDisableMe": "Sorry, you cannot disable your own account.", "levelSecurityFair": "Fair", "levelSecurityGood": "Good", "levelSecurityWeak": "Weak"}, "AccountForm": {"1372d3c0": "Phone number", "1afbc45c": "Select role", "255fbb25": "Additional information", "29ee7312": "Enter first name", "384afe01": "Login information", "4072213d": "Email and phone number can be used to log into system and receive notifications and alerts via email and SMS.", "5517185b": "Add account notes.", "5b1e5596": "Notes", "5d7d978b": "First name", "6378d56c": "Select status", "6b8ea5ae": "Change account password", "ab9a0cc1": "Last and middle name", "account": "account", "af223b70": "Account information", "c50f0b6f": "Creation time", "changePassword": "Change password", "changingPasswordWillAffectAccount": "Changing password will affect account owner's usage. System will log this account out of all related devices. Please notify account owner to avoid any issues.", "ebba7d73": "Create new account", "eff648a4": "Enter email", "emailAlreadyExists": "Email already exists in system.", "enterPhoneNumber": "Enter phone number", "fb1a03fd": "Enter last and middle name", "noAccountGroupsCreatedYet": "No account groups created yet", "phoneAlreadyExists": "Phone number already exists in system.", "phoneNumberIsInvalid": "Phone number is invalid.", "selectGroupAccount": "Select account group"}, "ActivateAccount": {"areYouSureActivateAccount": "Are you sure you want to activate this account? This action will allow the account holder to access the account and use its features.", "confirmAccountActivate": "Confirm account activation"}, "ConfirmChangeLanguage": {"**********": "<span>Some languages ​​you don't have content set up yet. Would you like to quickly set them up with the content \"<strong>{valueSuggest}</strong>\" for these languages?</span>", "**********": "Some languages are still empty."}, "DeletedAccount": {"areYouSureDeleteAccount": "Are you sure you want to delete this account? This action will permanently delete all data related to account and cannot be restored. Please confirm if you would like to proceed.", "confirmAccountDelete": "Confirm account deletion"}, "DisableAccount": {"areYouSureDisableAccount": "Are you sure you want to disable this account? This action will set the account to inactive status.", "confirmAccountDisable": "Confirm account deactivation"}, "ListPermissionGroup": {"listOfRoles": "List of roles"}, "RemoveRoleAccount": {"areYouSureRoleRemoval": "Are you sure you want to remove the role from this account? Please confirm if you would like to proceed.", "cannotRemoveRole": "Role cannot be removed. Please check again later.", "confirmRoleRemoval": "Confirm role removal", "hasBeenRemoveRole": "Role has been removed successfully."}, "UpdatePassword": {"notice": "Notice", "password": "password"}}}, "authorization": {"AuthorizationContainer": {"**********": "Cancel new role creation", "**********": "Role name", "**********": "Are you sure you want to cancel new role creation? The information you just entered will not be saved.", "**********": "Access", "**********": "Account", "*********": "Delete role", "********": "Create role", "********": "Description"}, "AuthorizationTable": {"**********": "Cancel updating role", "**********": "Are you sure you want to cancel role update? The information you just entered will not be saved.", "291548126_1": "product", "291548126_2": "products", "*********": "{value, plural, one {{value, number} {application}} other {{value, number} {applications}}}"}, "components": {"BaseFormData": {"**********": "Overview information", "**********": "Enter role name", "**********": "Role description", "**********": "Update", "**********": "This is the highest administrative role, therefore, you will not be able to assign or configure permissions for this role.", "**********": "Create", "**********": "Role name", "**********": "role", "*********": "Configuration", "*********": "Access configuration", "*********": "Enter role description", "*********": "Create new role", "*********": "Role information"}, "ConfigApplication": {"**********": "Save configuration", "*********": "Access feature list"}, "DeletedRole": {"**********": "Are you sure you want to delete this role? This action will permanently delete role from the system. Please confirm if you would like to proceed.", "**********": "Confirm role deletion", "**********": "Please select another role for the accounts belonging to this role.", "**********": "Select another role", "*********": "Are you sure you want to delete this role? This action will permanently delete role and affect all accounts belonging to it. All permissions associated with these accounts will be revoked. Please confirm if you wish to proceed.", "*********": "Change accounts to another role", "*********": "Confirm", "*********": "{text}", "*********": "Cancel"}, "ModalConfigPermission": {"**********": "Cancel saving configuration", "*********": "Are you sure you want to cancel saving configuration? The information you just set will not be saved."}, "usePrepare": {"**********": "Update account", "**********": "Document template update history", "**********": "Update account group", "**********": "Delete account", "*********": "Create account", "**********": "Create document template", "*********": "Role management", "**********": "Share document", "**********": "Dashboard", "**********": "Update document", "**********": "Document template configuration", "**********": "View list of accounts", "**********": "Account management", "*********": "Delete account group", "**********": "Setting", "**********": "Share category", "**********": "Create account group", "**********": "Monitoring station", "**********": "Change account password", "**********": "Create document", "**********": "Document template history", "**********": "Delete category", "**********": "View list of account groups", "**********": "Update document template", "*********": "Delete document template", "*********": "View list of document templates", "*********": "Document template management", "**********": "Project management", "**********": "Category & document management", "*********": "View list of categories & documents", "*********": "Delete document", "*********": "Export PDF file of document", "*********": "Account group management", "*********": "Overview", "********": "View list of roles", "*********": "File management", "*********": "Delete role", "*********": "Document update history", "********": "Create role", "*********": "Update category", "*********": "Update role", "*********": "Create category"}}}, "categories": {"components": {"DeleteCategory": {"*********": "Confirm"}}}, "group-account": {"AccountGroupContainer": {"**********": "This group currently has no accounts. You do not have permission to add account.", "addAccForManagenmentAndAccess": "Add accounts to easily manage and assign access permissions.", "notAccountInGroupAccount": "No account in account group"}, "DeleteGroupAccount": {"areYouSureDeleteGroupAccount": "Are you sure you want to delete this account group? This action will permanently delete the account group but will not affect accounts within the group. Please confirm if you would like to proceed.", "confirmGroupAccountDelete": "Confirm account group deletion"}, "UserAccountGroupPage": {"removeFromAccGroup": "Remove from account group"}, "components": {"GroupAccountForm": {"accountGroupLower": "account group", "accountGroupName": "Account group name", "addAccToList": "Add account to list", "areYouSureCancelNewCreateGroupAccount": "Are you sure you want to cancel creating new account group? The information you just entered will not be saved.", "areYouSureCancelUpdateGroupAccount": "Are you sure you want to cancel the account group update? The information you just entered will not be saved.", "cancelNewCreateGroupAccount": "Cancel creating new account group", "cancelUpdateInfoGroupAccount": "Cancel updating account group", "createGroupAccountNew": "Create new account group", "descriptionAccountGroup": "Account group description", "enterAccountGroupName": "Enter account group name", "enterDesGroupAccount": "Enter account group description", "generalInfo": "Overview information", "infoGroupAccount": "Account group information", "listGroupAccount": "List of accounts", "noAccountsYet": "No accounts yet"}, "RemoveUseGroupAccount": {"areYouSureRemoveUserGroupAccount": "Are you sure you want to remove the account from this account group? Please confirm if you would like to proceed.", "confirmRemoveUserGroupAccount": "Confirm removal from account group"}, "RemoveUserGroupAccount": {"cannotRemoveUser": "Account cannot be removed from group. Please check again later.", "hasBeenRemoveUser": "Account has been removed from group successfully."}}}, "profile": {"ProfileForm": {"**********": "<PERSON><PERSON> lý do bả<PERSON> mậ<PERSON>, hệ thống sẽ đăng xuất tài khoản của bạn khỏi các thiết bị. <PERSON>ui lòng đăng nhập lại để tiếp tục sử dụng ứng dụng.", "**********": "Thông tin tài khoản của bạn đã đư<PERSON><PERSON> cập nhật", "addNoteAboutAccount": "Add account notes.", "birthdate": "Date of birth", "chooseBirthdate": "Select date of birth"}, "UpdatePasswordProfile": {"oldPasswordIncorrect": "Current password is incorrect. Please re-enter current password."}}, "settings": {"SettingsContainer": {"attachment": "Attachment"}, "SettingsPage": {"": {"limitedCapacityForEachFile": "Limited capacity for each file"}, "appProductActiveFail": "Applicable product cannot be activated. Please check again later.", "appProductActiveSuccess": "Applicable product has been activated successfully.", "appProductUnActiveFail": "Applicable product cannot be deactivated. Please check again later.", "appProductUnActiveSuccess": "Applicable product has been deactivated successfully.", "config": "Configuration", "format": "Format", "forrmatFileAccept": "Allowed file attachment format", "productApplicable": "Applicable product", "turnOffExtentionFileFail": "Allowed file attachment format cannot be deactivated. Please check again later.", "turnOffExtentionFileSuccess": "Allowed file attachment format has been deactivated successfully.", "turnOnExtentionFileFail": "Allowed file attachment format cannot be activated. Please check again later.", "turnOnExtentionFileSuccess": "Allowed file attachment format has been activated successfully.", "typeFile": "Type", "updateValue": "Updating value"}, "components": {"contentModalConfig": {"AreYouSureCancelUpdatingValue": "Are you sure you want to cancel value update? The value you just entered will not be saved.", "cancelUpdatingValue": "Cancel updating value"}, "contentModalFileConfig": {"enterValue": "Enter value", "limitPerFile": "Limit per file", "unit": "Unit", "updatedValueFail": "Value cannot be updated. Please check again later.", "updatedValueSuccess": "Value has been updated successfully.", "valueFileConfig": "Value"}, "contentModalFileType": {"descriptionExtention": "Format description", "infoExtention": "Format information", "nameExtention": "Format name", "productApply": "Applicable product", "typeExtention": "Format type", "updatedValueExtFail": "Format information cannot be updated. Please check again later.", "updatedValueExtSuccess": "Format information has been updated successfully."}, "contentModalType": {"AreYouSureCancelUpdatingValue": "Are you sure you want to cancel value update? The value you just entered will not be saved.", "cancelUpdatingValue": "Cancel updating value"}}}}, "settings": {"settings-managenment": {"dataSiderbarSettings": {"attachmentSidebar": "Attachment", "digitalSignature": "Digital signature", "notifications": "Notification", "packageManagement": "Package management", "storageCapacity": "Storage capacity"}}}, "ui": {"base-table": {"1237119840": "No results found", "notEmptyResult": "Sorry, no results match the keyword you searched for. Please check the keyword or try searching with a different keyword.", "notEmptyResultWithFilter": "Sorry, no results match the filter you searched for. Please select different filter."}, "input-language": {"1225600157": "Vietnamese", "1488782352": "Language settings", "1782945719": "Language", "216897238": "Save setting", "768903020": "Content", "901769905": "Enter content"}, "input-search": {"enterSearchKeyword": "Enter search keyword...", "search": "Search"}}}, "constants": {"defineMessages": {"1073665205": "{type} information cannot be updated. Please check again later.", "1278882097": "New {type} cannot be created. Please check again later.", "1280440072": "{type} information has been updated successfully.", "1790118527": "{field} cannot be empty.", "264910375": "{type} has been deleted successfully.", "424075352": "{field} is invalid.", "439996808": "{field} format is incorrect. Please re-enter.", "*********": "{type} cannot be deleted. Please check again later.", "75d334b5": "Activate", "80dcc7bb": "{field} do not match.", "********": "{field} must be at least {min} characters.", "*********": "Do not enter more than {max} characters.", "*********": "New {type} has been created successfully.", "activateFail": "{type} cannot be activated. Please check again later", "activateSuccess": "{type} has been activated successfully.", "changePassFail": "Account {type} cannot be changed. Please check again later.", "changePassSuccess": "Account {type} has been changed successfully.", "disableFail": "{type} cannot be disabled. Please check again later.", "disableSuccess": "{type} has been disabled successfully.", "f6fbf4ac": "Disable", "fileMustNotExceed": "Each file must not exceed {max} MB.", "searchAccount": "Search account", "updateGroupFail": "{type} cannot be updated. Please check again later.", "updateGroupSuccess": "{type} has been updated successfully."}}}