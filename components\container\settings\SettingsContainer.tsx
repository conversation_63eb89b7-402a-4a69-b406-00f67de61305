'use client';

import { FormattedMessage, useIntl } from 'react-intl';
import { cn } from '@/utils/tailwind';
import SettingsPage from './SettingsPage';

export default function SetingsContainer() {
  const intl = useIntl();

  return (
    <>
      <div className="flex-1 flex flex-col bg-white">
        <div
          className={cn(
            'h-[64px] w-full bg-white flex-shrink-0 px-5 py-3 flex flex-row justify-between items-center gap-5 border-b border-gray-200',
          )}
        >
          <div className="flex flex-row gap-3 items-center">
            <span className="text-lg text-gray-800 font-medium">
              <FormattedMessage
                defaultMessage="Tệp đính kèm"
                id="components.container.settings.SettingsContainer.attachment"
              />
            </span>
          </div>
        </div>
      </div>
      <div className="bg-gray-100 p-5 flex flex-col h-[calc(100vh_-_64px)]">
        <SettingsPage />
      </div>
    </>
  );
}
