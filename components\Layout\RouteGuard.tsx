'use client';

import { PropsWithChildren, useEffect } from 'react';
import { Spin } from 'ui-components';
import { useCreateClient } from '@/api/useCreateClient';
import { appStories } from '@/store';

/**
 * Component that guards routes based on user permissions
 * Displays a loading spinner while checking permissions
 */
export default function RouteGuard({ children, tenantId }: PropsWithChildren<{ tenantId?: string }>) {
  const { updateApplicationPermissions, updateUser, updateApplicationAccess, updateUserPermissions } = appStories(
    (state) => state,
  );
  const { applications, auth } = useCreateClient();

  // Fetch all required data
  const { data: appPermissions, isLoading: isLoadingPermissions } = applications.getApplicationWithPermissions();

  const { data: userProfile, isLoading: isLoadingUserProfile } = auth.getProfile();

  const { data: userPermissions, isLoading: isLoadingUserPermissions } = auth.getPermissions();

  const { data: applicationAccess, isLoading: isLoadingApp } = auth.getApplications(tenantId);

  // Update store with user permissions
  useEffect(() => {
    if (userPermissions?.data) {
      updateUserPermissions(userPermissions.data);
    }
  }, [userPermissions?.data, updateUserPermissions]);

  // Update store with application access
  useEffect(() => {
    if (applicationAccess) {
      updateApplicationAccess(applicationAccess);
    }
  }, [applicationAccess, updateApplicationAccess]);

  // Update store with user profile
  // Update authentication status when user profile is loaded
  useEffect(() => {
    if (userProfile?.data) {
      updateUser(userProfile.data);
    }
  }, [userProfile?.data, updateUser]);

  // Update store with application permissions
  useEffect(() => {
    if (appPermissions && !isLoadingPermissions) {
      updateApplicationPermissions(appPermissions);
    }
  }, [appPermissions, isLoadingPermissions, updateApplicationPermissions]);

  // Loading state
  const isLoading = isLoadingUserProfile || isLoadingUserPermissions || isLoadingPermissions || isLoadingApp;

  if (isLoading) {
    return (
      <div className="h-screen w-screen relative flex flex-row items-center justify-center overflow-hidden">
        <Spin loading />
      </div>
    );
  }

  // If not checking and has permission, render children
  // If no permission, the hook will have already redirected
  return <>{children}</>;
}
