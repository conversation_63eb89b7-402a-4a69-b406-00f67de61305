import { Fragment } from 'react';
import { ComingSoon } from '@/components/commons/ComingSoon';

type Params = Promise<{ nested: string }>;
export default async function Index({ params }: { params: Params }) {
  const { nested } = await params;
  return (
    <Fragment>
      <div className="bg-white h-full w-full flex flex-col justify-center items-center gap-4">
        <div></div>
        <span className="text-gray-500 text-4xl not-italic font-bold uppercase">{nested}</span>
        <ComingSoon />
      </div>
    </Fragment>
  );
}
