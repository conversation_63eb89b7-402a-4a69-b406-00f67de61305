import { Permission } from '@/api/dataType';
import { LIMIT_ITEM_PER_PAGE } from '@/constants';
import { Access } from '@/constants/scopes';

export const cleanObject = (obj: Record<string, any>) => {
  const result: Record<string, any> = {};
  if (obj) {
    Object.keys(obj).forEach((key) => {
      if ((!Array.isArray(obj[key]) && obj[key]) || typeof obj[key] === 'number' || obj[key]?.length)
        result[key] = obj[key];
    });
  }
  return result;
};

export const generateNums = (idString: string = '') => {
  let hash = 0;
  for (let i = 0; i < idString.length; i++) {
    hash = (hash << 5) - hash + idString.charCodeAt(i);
    hash |= 0;
  }

  const seed = Math.abs(hash);

  function seededRandom(seed: number) {
    const x = Math.sin(seed++) * 10000;
    return Math.floor((x - Math.floor(x)) * 6 + 1);
  }

  return seededRandom(seed);
};
export const getPagination = (page: number = 0, size: number = LIMIT_ITEM_PER_PAGE) => {
  const limit = +size;
  const from = page * limit;
  const to = page ? from + size - 1 : size - 1;

  return { from, to };
};

export const hasAccess = (access: Access, userPermissions: Partial<Permission>[]) => {
  if (Array.isArray(access)) {
    return access.some((a) => userPermissions.find((p) => p.keyName === a));
  }
  return userPermissions.find((p) => p.keyName === access);
};

const getParamsOnly = (fTmp: string) => {
  fTmp = ~fTmp.indexOf(`(`) ? fTmp.slice(fTmp.indexOf(`(`)) : fTmp;
  fTmp = (fTmp.match(/(^[a-z_](?=(=>|=>{)))|((^\([^)].+\)|\(\))(?=(=>|{)))/i) || [fTmp])[0];
  return !fTmp.startsWith(`(`) ? `(${fTmp})` : fTmp;
};

// eslint-disable-next-line @typescript-eslint/no-unsafe-function-type
export function getNumArgs(func: Function) {
  const str = func.toString();
  const fTmp = getParamsOnly(str.replace(/\s+/g, ``));

  // no parameters, no need for further parsing
  if (fTmp === `()`) {
    const matches = [...str.matchAll(/arguments\[(\d+)\]/g)];
    if (matches.length === 0) {
      return 0;
    } else {
      return Math.max(...matches.map((m) => parseInt(m[1]))) + 1;
    }
    // console.log(matches)
    // const maxIndex =
    // return 0;
  }

  let [paramStr, commaCount, bracketCount, lastParen, i, inStrSingle, inStrDouble, bOpen, bClose] = [
    fTmp,
    0,
    0,
    0,
    0,
    false,
    false,
    [...`([{`],
    [...`)]}`],
  ];

  for (; i < paramStr.length; i += 1) {
    if (bOpen.includes(paramStr[i]) && !inStrSingle && !inStrDouble) {
      bracketCount += 1;
      lastParen = i;
    }
    if (bClose.includes(paramStr[i]) && !inStrSingle && !inStrDouble) {
      bracketCount -= 1;
      if (bracketCount < 1) {
        break;
      }
    }
    if (paramStr[i] === "'" && !inStrDouble && paramStr[i - 1] !== '\\') {
      inStrSingle = !inStrSingle;
    }
    if (paramStr[i] === '"' && !inStrSingle && paramStr[i - 1] !== '\\') {
      inStrDouble = !inStrDouble;
    }
    if (paramStr[i] === ',' && bracketCount === 1 && !inStrSingle && !inStrDouble) {
      commaCount += 1;
    }
  }

  const matches = [...str.matchAll(/arguments\[(\d+)\]/g)];
  let argumentCount = 0;
  if (matches.length > 0) {
    argumentCount = Math.max(...matches.map((m) => parseInt(m[1]))) + 1;
  }

  return Math.max(
    commaCount === 0 && paramStr.substring(lastParen + 1, i).trim().length === 0 ? 0 : commaCount + 1,
    argumentCount,
  );
}
