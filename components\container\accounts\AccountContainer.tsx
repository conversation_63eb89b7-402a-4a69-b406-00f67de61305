'use client';

import IconCommon from '@/components/commons/IconLayout';

import { useState } from 'react';
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
} from 'ui-components';
import UserAccountPage from './UserAccountPage';
import { AccountForm } from './components/AccountForm';
import { cn } from '@/utils/tailwind';
import { Users } from '@/api/dataType';

import { useMemo } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { IdentificationCard, ToggleRight } from '@phosphor-icons/react';
import {
  FilterDataType,
  FormValuesQuery,
  IQueryOperations,
  SortDataType,
} from '@/components/commons/HeaderFilter/types';
import { useCreateClient } from '@/api/useCreateClient';
import { SortEnum, FilterStatusEnum } from '@/api/type';
import { messageStatusCommon } from '@/constants/defineMessages';
import HeaderFilter from '@/components/commons/HeaderFilter';
import { useFilterOrSearch } from '@/hook/useFilterOrSearch';
import { Options } from 'nuqs';
import { useSearchParams } from 'next/navigation';
import { QueryOperations } from '@/constants';
import { appStories } from '@/store';
import { usePermissionModule } from '@/hook/usePermissionModule';
import { moduleScope, Scopes } from '@/constants/scopes';

export type FilterAndSortProps = {
  filters: IQueryOperations<Users>[] | null;
  page: string;
  search: string;
  sort: IQueryOperations<Users> | null;
  setPage: (value: string | ((old: string) => string | null) | null, options?: Options) => Promise<URLSearchParams>;
  setSearch: (value: string | ((old: string) => string | null) | null, options?: Options) => Promise<URLSearchParams>;
  setFilters: (
    value: string | ((old: string | null) => string | null) | null,
    options?: Options,
  ) => Promise<URLSearchParams>;
  setSort: (
    value: string | ((old: string | null) => string | null) | null,
    options?: Options,
  ) => Promise<URLSearchParams>;
  getDefaultFiltersArray: () => {
    key: keyof Users;
    value: string[];
  }[];
  getDefaultSort: string | null;
  getDefaultSortObj: () => {
    key: string;
    value: string;
  };
};

export default function AccountContainer() {
  const [isOpenAccountForm, setIsOpenAccountForm] = useState(false);
  const [isChangedData, setIsChangeData] = useState(false);
  const [isOpenConfirm, setIsOpenConfirm] = useState(false);

  const actionUser = usePermissionModule(moduleScope.USERS, Scopes.UserConfigScopes);

  const intl = useIntl();
  const query = useSearchParams();
  const locale = intl.locale as 'vi' | 'en';
  const { roles } = useCreateClient();

  const {
    data: dataRoles,
    isLoading: isLoadingUserRole,
    isFetching: isFetchingUserRole,
  } = roles.find();

  const optionsRoles = useMemo(() => {
    return (
      dataRoles?.data?.list.map((item) => ({
        title: `${locale === 'vi' ? item.name.vi : item.name.en}`,
        value: item.id,
      })) ?? []
    );
  }, [dataRoles, isLoadingUserRole, isFetchingUserRole]);

  const filterData: FilterDataType<Users>[] = useMemo(() => {
    return [
      {
        key: 'roles',
        title: (
          <div className="flex flex-row items-center gap-2 text-current">
            <IdentificationCard className="flex-shrink-0 text-current" size={20} weight="regular" />
            <span className="text-current">
              <FormattedMessage
                defaultMessage="Lọc theo nhóm quyền"
                id="components.commons.FilterAndSort.filterByRole"
              />
            </span>
          </div>
        ),
        options: optionsRoles,
      },
      {
        key: 'isActive',
        title: (
          <div className="flex flex-row items-center gap-2 text-current">
            <ToggleRight className="flex-shrink-0 text-current" size={20} weight="regular" />
            <span className="text-current">
              <FormattedMessage
                defaultMessage="Lọc theo trạng thái"
                id="components.commons.FilterAndSort.filterByStatus"
              />
            </span>
          </div>
        ),
        options: [
          {
            value: FilterStatusEnum.ACTIVATE,
            title: intl.formatMessage(messageStatusCommon.activate),
          },
          {
            value: FilterStatusEnum.INACTIVATE,
            title: intl.formatMessage(messageStatusCommon.disable),
          },
        ],
      },
    ];
  }, [optionsRoles]);

  const sortData: SortDataType<Users>[] = [
    {
      key: 'createdAt',
      title: (
        <FormattedMessage
          defaultMessage="Sắp xếp theo thời gian tạo"
          id="components.commons.FilterAndSort.sortByCreateTime"
        />
      ),
      defaultValue: SortEnum.DESC,
      options: [
        {
          value: SortEnum.DESC,
          title: intl.formatMessage({
            defaultMessage: 'Mới nhất đến cũ nhất',
            id: 'components.commons.FilterAndSort.newestToOldest',
          }),
        },
        {
          value: SortEnum.ASC,
          title: intl.formatMessage({
            defaultMessage: 'Cũ nhất đến mới nhất',
            id: 'components.commons.FilterAndSort.oldestToNewest',
          }),
        },
      ],
    },
    {
      key: 'username',
      title: (
        <FormattedMessage
          defaultMessage="Sắp xếp theo tên tài khoản"
          id="components.commons.FilterAndSort.sortByNameAccount"
        />
      ),
      defaultValue: SortEnum.ASC,
      options: [
        {
          value: SortEnum.ASC,
          title: intl.formatMessage({
            defaultMessage: 'Từ A -> Z',
            id: 'components.commons.FilterAndSort.sortByFromAZ',
          }),
        },
        {
          value: SortEnum.DESC,
          title: intl.formatMessage({
            defaultMessage: 'Từ Z -> A',
            id: 'components.commons.FilterAndSort.sortByFromZA',
          }),
        },
      ],
    },
  ];

  const {
    filters,
    page,
    search,
    sort,
    setPage,
    getDefaultFiltersArray,
    setFilters,
    setSort,
    getDefaultSort,
    setSearch,
    getDefaultSortObj,
  } = useFilterOrSearch({
    filterData,
    sortData,
  });

  const dataFilterAndSortProps: FilterAndSortProps = {
    filters: filters,
    page: page,
    search: search,
    sort: sort,
    setPage: setPage,
    getDefaultFiltersArray: () => getDefaultFiltersArray,
    setFilters: setFilters,
    setSort: setSort,
    getDefaultSort: getDefaultSort,
    setSearch: setSearch,
    getDefaultSortObj: getDefaultSortObj,
  };

  const onOpenChange = (open: boolean) => {
    if (isChangedData) {
      setIsOpenConfirm(true);
    } else {
      setIsOpenAccountForm(open);
    }
  };

  const onSubmitForm = async (values: FormValuesQuery<Users>) => {
    setPage('1');

    if (values.filters.length > 0) {
      const isDefault = values.filters.every((item) => {
        const defaultFilter = getDefaultFiltersArray?.find((d) => d.key === item.key);
        if (!defaultFilter) return false;

        const defaultValue = defaultFilter.value || [];
        return JSON.stringify(item.value) === JSON.stringify(defaultValue);
      });
      if (!isDefault) {
        await setFilters(values.filters as any);
      } else {
        await setFilters(null);
      }
    } else {
      await setFilters(null);
    }

    // // Update sort - check if it's different from default
    if (values.sort.key && values.sort.value) {
      const [key, value] = getDefaultSort?.split('.') || [];
      const isDefault = key.length > 0 && value.length > 0 && values.sort.key === key && values.sort.value === value;

      if (!isDefault) {
        await setSort({
          key: values.sort.key,
          value: values.sort.value,
        } as any);
      } else {
        await setSort(null);
      }
    } else {
      await setSort(null);
    }
  };

  return (
    <>
      <div className="flex-1 flex flex-col bg-white">
        <div
          className={cn(
            'h-[64px] w-full bg-white flex-shrink-0 px-5 py-3 flex flex-row justify-between items-center gap-5 border-b border-gray-200',
          )}
        >
          <div className="flex flex-row gap-3 items-center">
            <span className="text-lg text-gray-800 font-medium">
              <FormattedMessage
                defaultMessage="Tài khoản"
                id="components.container.accounts.AccountContainer.**********"
              />
            </span>
          </div>
          <div className="flex gap-2">
            <HeaderFilter<Users>
              renderElement={[
                {
                  type: 'TOOL_BAR',
                  config: {
                    isSearch: true,
                    operations: {
                      filters: filterData,
                      sort: sortData,
                    },
                    defaultValues: {
                      search,
                      filters: filters!,
                      sort: sort!,
                    },
                    onSubmit: onSubmitForm,
                    onSubmitSearch: (value: string) => {
                      setTimeout(() => {
                        setSearch(value);
                        setPage('1');
                      }, 500);
                    },
                    isActiveFilter: query.has(QueryOperations.FILTERS),
                    isActiveSort: query.has(QueryOperations.SORT),
                  },
                },
              ]}
            />
            {actionUser.CREATE && (
              <Button
                onClick={async () => {
                  setIsOpenAccountForm(true);
                }}
                className="flex flex-row gap-2 text-white"
              >
                <IconCommon name="Plus" size={20} className="text-current" />
                <span className="font-medium text-current">
                  <FormattedMessage
                    defaultMessage="Tạo tài khoản"
                    id="components.container.accounts.AccountContainer.*********"
                  />
                </span>
              </Button>
            )}
          </div>
        </div>

        <div className="flex h-[calc(100vh_-_64px)]">
          <UserAccountPage dataFilterAndSortProps={dataFilterAndSortProps} />
        </div>
      </div>
      <Dialog open={isOpenAccountForm} onOpenChange={onOpenChange}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[1100px] max-h-[calc(100vh_-_100px)]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <AccountForm
              onClose={() => {
                setIsOpenAccountForm(false);
                setIsChangeData(false);
              }}
              isChangedData={isChangedData}
              setIsChangedData={setIsChangeData}
              isOpenConfirm={isOpenConfirm}
              setIsOpenConfirm={setIsOpenConfirm}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
}
