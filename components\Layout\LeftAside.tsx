'use client';

import { cn } from '@/utils/tailwind';
import Image from 'next/image';
import { AsideItemProps, dataItemAside } from './dataItemAside';

import { appStories } from '@/store';

import { Popover, PopoverContent, PopoverTrigger, Spin, toast } from 'ui-components';

import ItemAside from './ItemAside';
import { generateNums, hasAccess } from '@/utils';
import SkeletonComp from '../ui/skeleton';
import { DotsNine, List, SignOut } from '@phosphor-icons/react';
import SwitchLanguage from '../ui/switch-language';
import { useMemo, useState, useTransition } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import { BASE_PATH, NEXT_LOCALE, WEB_CORE_URL } from '@/constants';
import { Applications, Users } from '@/api/dataType';
import Link from 'next/link';
import { useNavigationLoading } from '@/hook/useNavigationLoading';
import { setCookie } from '@/actions/cookieActions';
import { useRouter } from 'next/navigation';

export default function LeftAside({ data }: { data?: Users | null }) {
  const intl = useIntl();
  const router = useRouter();
  const locale = intl.locale as 'vi' | 'en';
  const [isOpen, setIsOpen] = useState(false);
  const [isOpenApps, setIsOpenApps] = useState(false);
  const { expand, updateExpand, user, applicationAccess, userPermissions } = appStories((state) => state);
  const onToggle = () => updateExpand(expand);
  const onClose = () => setIsOpen(false);
  const indexImage = generateNums(user?.logtoId || '');
  const urlAvatar = `${BASE_PATH}/images/avatars/avatar-${indexImage}.svg`;
  const { isNavigating, startNavigation, stopNavigation } = useNavigationLoading();
  const [isPending, startTransition] = useTransition();

  const changeLocale = async (newLocale: string) => {
    if (newLocale === locale) return;

    try {
      startNavigation();
      setIsOpen(false);
      startTransition(async () => {
        const result = await setCookie(NEXT_LOCALE, newLocale);
        if (result.success) {
          router.refresh();
          setTimeout(() => {
            stopNavigation();
          }, 500);
        } else {
          toast({
            type: 'error',
            title: intl.formatMessage({
              defaultMessage: 'Có lỗi xảy ra khi thay đổi ngôn ngữ',
              id: 'components.Layout.LeftAside.languageError',
            }),
          });
        }
      });
    } catch (error) {
      console.error('Error changing language:', error);
      toast({
        type: 'error',
        title: intl.formatMessage({
          defaultMessage: 'Có lỗi xảy ra khi thay đổi ngôn ngữ',
          id: 'components.Layout.LeftAside.languageError',
        }),
      });
    }
  };

  const navSidebar = useMemo(() => {
    return dataItemAside.filter((item) => {
      return hasAccess(item.access, userPermissions);
    });
  }, [userPermissions]);

  const handleSignOut = () => {
    localStorage.removeItem('force_logout');
    localStorage.removeItem('session_expired');
    window.location.assign(`${WEB_CORE_URL}/api/logto/sign-out`);
  };

  const isLoading = isPending || isNavigating;

  return (
    <>
      <aside
        className={cn(
          'h-full w-[251px] flex flex-col flex-shrink-0 border-r border-solid border-neutral-300 transition-all overflow-hidden duration-300 z-10',
          expand && 'w-[69px] transition-all duration-300 ',
        )}
      >
        <div
          className={cn(
            'h-[69px] w-full bg-white flex-shrink-0 px-5 py-3 flex flex-row gap-[20px] justify-between items-center duration-700 transition-[flex]',
            expand && 'h-[113px] p-3 flex-col items-start duration-700 transition-[flex]',
          )}
        >
          <div
            className={cn('flex flex-row gap-[9px] w-[164px] items-center', {
              'px-[7px] ': expand,
            })}
          >
            <Image
              src={`${BASE_PATH}/images/branding/branding-config.svg`}
              alt="logo"
              width={32}
              height={32}
              unoptimized
              priority
              className="h-8 w-8 object-contain flex-shrink-0"
            />
            <div
              className={cn(
                'text-sm font-semibold text-gray-800 flex-1 min-w-0 visible opacity-100 transition-opacity duration-700 block',
                expand && 'invisible opacity-0 transition-opacity duration-700',
              )}
            >
              iLotusLand <br />
              for Config
            </div>
          </div>
          <div className="h-11 w-11 p-2.5">
            <List
              size={24}
              className={cn(
                'cursor-pointer transition-transform duration-500 text-gray-700',
                expand && 'w-full flex items-center transition-all duration-700',
              )}
              onClick={onToggle}
            />
          </div>
        </div>
        <div className="bg-white flex-1 py-5 px-3 h-full">
          <div className="flex flex-col gap-3">
            {navSidebar.map((itemAside: AsideItemProps, indexItem: number) => {
              return <ItemAside key={indexItem} itemAside={itemAside} isToggle={expand} />;
            })}
          </div>
        </div>
        <div
          className={cn(
            'h-auto w-full bg-white flex-shrink-0 border-t border-solid border-neutral-300 px-5 py-3 transition-[flex] duration-700',
            'flex flex-row justify-between items-center',
            expand && 'h-auto px-3 py-5 flex-col gap-5 transition-transform duration-700 border-transparent',
          )}
        >
          {user ? (
            <>
              <div className={cn(!expand && 'order-last')}>
                <Popover open={isOpen} onOpenChange={setIsOpen}>
                  <PopoverTrigger asChild>
                    <Image
                      src={user?.avatar || urlAvatar}
                      alt="logo-avt"
                      width={44}
                      height={44}
                      unoptimized
                      priority
                      className="h-11 w-11 object-contain cursor-pointer rounded-full border-2 border-gray-200"
                    />
                  </PopoverTrigger>
                  <PopoverContent align="end" side="right" className="bg-white w-[200px]">
                    <div className="text-sm leading-[21px] font-semibold text-gray-800 mb-3 truncate">
                      Hi {user?.username[locale]} {user?.givenName[locale]},
                    </div>
                    <div className="flex flex-row gap-2 items-center mb-3">
                      <div className="flex items-center justify-center text-sm leading-[21px] font-semibold text-gray-800 truncate">
                        <FormattedMessage defaultMessage="Ngôn ngữ:" id="components.Layout.LeftAside.563257617" />
                      </div>
                      <SwitchLanguage setLangCookie={changeLocale} onClose={onClose} />
                    </div>
                    <hr className="mb-2" />
                    <div
                      onClick={handleSignOut}
                      className="flex flex-row gap-2 items-center cursor-pointer px-3 py-2 hover:bg-gray-100 hover:rounded-lg"
                    >
                      <SignOut size={24} weight="regular" className="text-current" />
                      <span className="text-sm leading-[21px] font-medium text-gray-700">
                        <FormattedMessage defaultMessage="Đăng xuất" id="components.Layout.LeftAside.238411488" />
                      </span>
                    </div>
                  </PopoverContent>
                </Popover>
              </div>
              <div className={cn('flex flex-start gap-[7px]', expand && 'flex-col')}>
                <Popover open={isOpenApps} onOpenChange={setIsOpenApps}>
                  <PopoverTrigger asChild>
                    <div
                      className="h-11 w-11 flex justify-center items-center bg-gray-100 rounded-lg hover:bg-gray-200 cursor-pointer"
                      onClick={() => setIsOpenApps(!isOpenApps)}
                    >
                      <DotsNine className="text-gray-700" weight="regular" size={20} />
                    </div>
                  </PopoverTrigger>
                  <PopoverContent className="rounded-2xl p-0 max-w-[unset] w-auto" side="top" align="start">
                    {applicationAccess.length > 0 && (
                      <div className="flex flex-col p-3 gap-3">
                        <span className="font-semibold text-lg text-gray-800">
                          <FormattedMessage defaultMessage="Ứng dụng" id="components.Layout.LeftAside.194521447" />
                        </span>
                        <div className="flex flex-row h-[116px] w-auto">
                          {applicationAccess.map((application: Applications) => {
                            return (
                              <Link
                                href={WEB_CORE_URL + application.url}
                                key={application.id}
                                className="flex flex-col items-center justify-center gap-3 p-3 w-[105px] h-full rounded-xl hover:bg-gray-100"
                                prefetch={false}
                                onClick={() => {
                                  setIsOpenApps(false);
                                }}
                              >
                                <Image
                                  src={`${BASE_PATH}/images/branding/branding-${application.signature}.svg`}
                                  alt={application.signature}
                                  height={48}
                                  width={48}
                                  unoptimized
                                  priority
                                  className="bg-transparent h-[48px] w-[48px]"
                                />
                                <span className="font-medium text-xs text-center text-gray-800">
                                  {application.name}
                                </span>
                              </Link>
                            );
                          })}
                        </div>
                      </div>
                    )}
                  </PopoverContent>
                </Popover>
              </div>
            </>
          ) : (
            <>
              <SkeletonComp className="h-11 w-11 rounded-full bg-gray-100" />
              <SkeletonComp className="h-11 w-11 flex justify-center items-center bg-gray-100 rounded-lg" />
            </>
          )}
        </div>
      </aside>
      {isLoading && <Spin loading fullScreen className="h-screen w-screen bg-black/20 z-[1000]" />}
    </>
  );
}
