'use client';

import { LANGUEGES_OPTIONS, NEXT_LOCALE } from '@/constants';
import { AppActions, appStories } from '@/store';
import Image from 'next/image';
import { usePathname, useRouter } from 'next/navigation';
import { useIntl } from 'react-intl';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from 'ui-components';

export default function SwitchLanguage() {
  const intl = useIntl();
  const router = useRouter();
  const updateLoading = appStories((state: AppActions) => state.updateLoading);
  const currentLocale = intl.locale;
  const currentPathname = usePathname();
  const currentLang = LANGUEGES_OPTIONS.find(
    (lang) => lang.value === currentLocale,
  );
  const onSwitchLange = (value: string) => {
    updateLoading(true);
    const days = 30;
    const date = new Date();
    date.setTime(date.getTime() + days * 24 * 60 * 60 * 1000);
    const expires = date.toUTCString();
    document.cookie = `${NEXT_LOCALE}=${value};expires=${expires};path=/`;

    router.push(currentPathname.replace(`/${currentLocale}`, `/${value}`));

    router.refresh();
    setTimeout(() => {
      updateLoading(false);
    }, 500);
  };
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="w-20 flex flex-row gap-2 border border-solid p-2 rounded-lg cursor-pointer">
          <Image
            src={currentLang?.image as string}
            alt={currentLang?.label as string}
            width={24}
            height={24}
            unoptimized
            priority
          />
          <span>{currentLang?.value.toUpperCase()}</span>
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-20 min-w-20">
        {LANGUEGES_OPTIONS.filter((lang) => lang.value !== currentLocale).map(
          (lang) => (
            <DropdownMenuItem
              key={lang.value}
              onClick={() => onSwitchLange(lang.value)}
              className="cursor-pointer w-full flex flex-row gap-2"
            >
              <Image src={lang.image} alt={lang.label} width={24} height={24} unoptimized priority />{' '}
              <span>{lang.value.toUpperCase()}</span>
            </DropdownMenuItem>
          ),
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
