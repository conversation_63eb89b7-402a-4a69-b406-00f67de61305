import { renderHook, act } from '@testing-library/react';
import { usePathAccess } from '@/hook/usePathAccess';
import { appStories } from '@/store';
import { urlPathName } from '@/constants/urlPathName';

// Mock dependencies
jest.mock('@/store');
jest.mock('@/constants/urlPathName', () => ({
  urlPathName: {
    ROOT: '',
    HOME: '/',
    NOT_PERMISSION: '/not-permission',
    ACCESS_DENIED: '/access-denied',
    ACCOUNT: {
      ROOT: '/accounts',
      GROUP_ACCOUNT: '/accounts/group',
      GROUP_ACCOUNT_ID: '/accounts/group/:id',
      AUTHORIZATION: '/accounts/authorization',
    },
    SETTINGS: {
      ROOT: '/settings',
      ATTACHMENT: '/settings/attachment',
      STORAGECAPACITY: '/settings/storage-capacity',
      DIGITALSIGNATURE: '/settings/digital-signature',
      PACKAGEMANAGEMENT: '/settings/package-management',
    },
    MONITORING_STATION: {
      ROOT: '/monitoring-station',
    },
    HEALTH: '/health',
    PROFILE: '/profile',
  },
}));

jest.mock('@/constants/urlPathName', () => ({
  pathWithScopes: {
    '/': 'general:read',
    '/accounts': 'user:read',
    '/accounts/group': 'group:read',
    '/accounts/group/:id': 'group:read',
    '/accounts/authorization': 'role:read',
    '/settings': 'setting:read',
    '/settings/attachment': 'setting:read',
    '/settings/storage-capacity': 'setting:read',
    '/settings/digital-signature': 'setting:read',
    '/settings/package-management': 'setting:read',
    '/monitoring-station': 'monitoring:read',
  },
}));

const mockAppStories = appStories as jest.MockedFunction<typeof appStories>;

describe('usePathAccess Hook', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('🔐 Kiểm tra quyền truy cập cơ bản', () => {
    it('should return true for paths user has permission', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'general:read' },
          { keyName: 'user:read' },
          { keyName: 'setting:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      act(() => {
        // Wait for loading to complete
      });

      expect(result.current.canAccess('/')).toBe(true);
      expect(result.current.canAccess('/accounts')).toBe(true);
      expect(result.current.canAccess('/settings')).toBe(true);
    });

    it('should return false for paths user does not have permission', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'general:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      act(() => {
        // Wait for loading to complete
      });

      expect(result.current.canAccess('/accounts')).toBe(false);
      expect(result.current.canAccess('/settings')).toBe(false);
      expect(result.current.canAccess('/monitoring-station')).toBe(false);
    });

    it('should always allow access to public paths', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [],
      });

      const { result } = renderHook(() => usePathAccess());

      expect(result.current.canAccess('/access-denied')).toBe(true);
      expect(result.current.canAccess('/profile')).toBe(true);
      expect(result.current.canAccess('/health')).toBe(true);
    });
  });

  describe('🏗️ Module-based Access', () => {
    it('should return correct module default paths', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'general:read' },
          { keyName: 'user:read' },
          { keyName: 'setting:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      expect(result.current.getModuleDefaultPath('general')).toBe('/');
      expect(result.current.getModuleDefaultPath('accounts')).toBe('/accounts');
      expect(result.current.getModuleDefaultPath('settings')).toBe('/settings');
    });

    it('should return null for modules without permission', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'general:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      expect(result.current.getModuleDefaultPath('accounts')).toBe(null);
      expect(result.current.getModuleDefaultPath('settings')).toBe(null);
    });

    it('should get accessible paths in module', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'group:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      const accountsPaths = result.current.getAccessiblePathsInModule('accounts');
      expect(accountsPaths).toContain('/accounts');
      expect(accountsPaths).toContain('/accounts/group');
      expect(accountsPaths).not.toContain('/accounts/authorization');
    });
  });

  describe('🎯 Smart Redirect Logic', () => {
    it('should redirect to alternative path in same module', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'group:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      // User tries to access /accounts/authorization but only has group permission
      const redirectPath = result.current.redirectIfUnauthorized('/accounts/authorization');
      
      // Should redirect to /accounts (module default) or /accounts/group
      expect(['/accounts', '/accounts/group']).toContain(redirectPath);
    });

    it('should redirect to different module when no access in current module', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'setting:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // User tries to access accounts but only has settings permission
      const redirectPath = result.current.redirectIfUnauthorized('/accounts');
      
      expect(redirectPath).toBe('/settings');
    });

    it('should redirect to NOT_PERMISSION when no access anywhere', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [],
      });

      const { result } = renderHook(() => usePathAccess());

      const redirectPath = result.current.redirectIfUnauthorized('/accounts');
      
      expect(redirectPath).toBe('/not-permission');
    });

    it('should return null when user has access to current path', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      const redirectPath = result.current.redirectIfUnauthorized('/accounts');
      
      expect(redirectPath).toBe(null);
    });
  });

  describe('🔄 Navigation Logic', () => {
    it('should navigate to best path for module', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'group:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      const bestPath = result.current.getBestPathForModule('accounts');
      
      expect(bestPath).toBe('/accounts'); // Default path has priority
    });

    it('should return first available path when default is not accessible', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'group:read' }], // Only group permission
      });

      const { result } = renderHook(() => usePathAccess());

      const bestPath = result.current.getBestPathForModule('accounts');
      
      expect(bestPath).toBe('/accounts/group'); // First accessible path
    });

    it('should handle module navigation with fallback', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'setting:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Try to navigate to accounts but only have settings permission
      const navigationPath = result.current.navigateToModule('accounts');
      
      expect(navigationPath).toBe('/settings'); // Fallback to accessible module
    });
  });

  describe('📊 Path Similarity Calculation', () => {
    it('should calculate correct path similarity', () => {
      const { result } = renderHook(() => usePathAccess());

      // Test similarity calculation through findAlternativePathInModule
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'group:read' },
        ],
      });

      const alternative = result.current.findAlternativePathInModule('/accounts/authorization');
      
      // Should prefer /accounts/group over /accounts due to higher similarity
      expect(['/accounts', '/accounts/group']).toContain(alternative);
    });
  });

  describe('⚡ Performance & Caching', () => {
    it('should cache access results', async () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // First call
      const result1 = await result.current.canAccessWithLoading('/accounts', 100);
      
      // Second call should be faster (cached)
      const start = Date.now();
      const result2 = await result.current.canAccessWithLoading('/accounts', 100);
      const duration = Date.now() - start;

      expect(result1).toBe(result2);
      expect(duration).toBeLessThan(50); // Should be much faster due to caching
    });

    it('should handle multiple path checks efficiently', async () => {
      mockAppStories.mockReturnValue({
        userPermissions: [
          { keyName: 'user:read' },
          { keyName: 'setting:read' },
        ],
      });

      const { result } = renderHook(() => usePathAccess());

      const paths = ['/accounts', '/settings', '/monitoring-station'];
      const results = await result.current.canAccessMultiple(paths, 100);

      expect(results.get('/accounts')).toBe(true);
      expect(results.get('/settings')).toBe(true);
      expect(results.get('/monitoring-station')).toBe(false);
    });
  });

  describe('🚨 Edge Cases', () => {
    it('should handle dynamic paths correctly', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'group:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      // Test dynamic path matching
      expect(result.current.canAccess('/accounts/group/123')).toBe(true);
      expect(result.current.canAccess('/accounts/group/abc')).toBe(true);
    });

    it('should handle empty permissions gracefully', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [],
      });

      const { result } = renderHook(() => usePathAccess());

      expect(result.current.unauthorized).toBe(true);
      expect(result.current.accessiblePaths).toHaveLength(0);
      expect(result.current.canAccess('/not-permission')).toBe(true);
    });

    it('should handle invalid module names', () => {
      mockAppStories.mockReturnValue({
        userPermissions: [{ keyName: 'user:read' }],
      });

      const { result } = renderHook(() => usePathAccess());

      expect(result.current.getModuleDefaultPath('invalid-module')).toBe(null);
      expect(result.current.getBestPathForModule('invalid-module')).toBe(null);
    });
  });
});
