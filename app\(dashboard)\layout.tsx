import { getCookie } from '@/actions/cookieActions';
import RouteGuard from '@/components/Layout/RouteGuard';
import dynamic from 'next/dynamic';
import { PropsWithChildren } from 'react';

const LayoutDashboard = dynamic(() => import('@/components/Layout/LayoutDashboard'));

export default async function RootDashboardLayout({ children }: PropsWithChildren) {
  const tenantId = await getCookie('x-tenant-id');
  return (
    <RouteGuard tenantId={tenantId}>
      <LayoutDashboard>{children}</LayoutDashboard>
    </RouteGuard>
  );
}
