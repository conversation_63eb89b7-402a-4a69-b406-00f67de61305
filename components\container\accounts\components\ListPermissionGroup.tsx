'use client';

import { HeaderModal } from '@/components/ui/modal/header-modal';
import { generateNums } from '@/utils';
import { X } from '@phosphor-icons/react';
import Image from 'next/image';
import { FormattedMessage, useIntl } from 'react-intl';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Spin,
} from 'ui-components';
import { RemoveRoleAccount } from './RemoveRoleAccount';
import { useRef, useState } from 'react';
import { useCreateClient } from '@/api/useCreateClient';
import { Roles, Users } from '@/api/dataType';
import { usePermissionModule } from '@/hook/usePermissionModule';
import { moduleScope, Scopes } from '@/constants/scopes';

interface ListPermissionGroup {
  dataRow?: Users;
  onClose: () => void;
  adminUsers: Users[] | undefined;
}
export const ListPermissionGroup = ({ dataRow, onClose, adminUsers }: ListPermissionGroup) => {
  const intl = useIntl();
  const recordRoleRef = useRef<Roles>(null);
  const actionUser = usePermissionModule(moduleScope.USERS, Scopes.UserConfigScopes);
  const { users } = useCreateClient();

  const { data: rolesByUser, isLoading } = users.getRolesByUser(dataRow?.id!);

  const [openRemoveRole, setOpenRemoveRole] = useState(false);

  const locale = intl.locale as 'vi' | 'en';

  const num = generateNums(dataRow?.logtoId);
  const urlAvatar = dataRow?.avatar ? dataRow?.avatar : `/config/images/avatars/avatar-${num}.svg`;
  const titleModal = (
    <FormattedMessage
      defaultMessage="Danh sách nhóm quyền"
      id="components.container.accounts.components.ListPermissionGroup.listOfRoles"
    />
  );
  return (
    <Spin loading={isLoading}>
      <HeaderModal title={titleModal} showButton={false} onClose={onClose} />
      <div className="flex bg-gray-100 p-4 rounded-b-lg">
        <div className="flex flex-col rounded-xl p-4 gap-4 bg-white w-full">
          <div className="flex py-4 pr-6 pl-4 gap-2.5 rounded-lg border border-gray-200">
            <Image src={urlAvatar} alt="avatar" width={40} height={40} unoptimized priority />
            <div className="flex flex-col flex-1 w-full">
              <span className="text-base font-medium text-gray-800">{`${locale === 'vi' ? dataRow?.username.vi : dataRow?.username.en} ${locale === 'vi' ? dataRow?.givenName.vi : dataRow?.givenName.en}`}</span>
              <span className="text-xs font-normal text-gray-600">{dataRow?.email}</span>
            </div>
          </div>

          <div className="border border-gray-200 w-full rounded-lg overflow-hidden divide-y">
            <span className="flex p-4 uppercase bg-gray-50 text-xs font-semibold text-gray-700 max-h-[50px] border-b border-gray-100">
              <FormattedMessage
                defaultMessage="Tên nhóm quyền"
                id="components.container.authorization.components.BaseFormData.1666962850"
              />
            </span>
            <div className="w-full max-h-[250px] overflow-y-auto divide-y">
              {rolesByUser?.data.map((r) => {
                const roleAdmin = r.isAdmin;
                // Nếu là role admin và user này k tồn tại trong danh sách user admin thì => xóa được
                const deleteRole = roleAdmin && adminUsers?.map((it) => it.id)?.includes(dataRow?.id!);
                // Nếu là role admin và user này tồn tại trong danh sách admin và chỉ có 1 => không xóa
                const notDeleted = roleAdmin && adminUsers?.length === 1 && dataRow?.id === adminUsers?.[0]?.id;

                return (
                  <div className="flex w-full p-4 gap-0.5 items-center max-h-[52px] hover:bg-gray-50" key={r.id}>
                    <span className="flex flex-1 text-sm font-semibold text-gray-700 line-clamp-1">{`${locale === 'vi' ? r.name.vi : r.name.en}`}</span>
                    {actionUser.UPDATE && (
                      <>
                        {rolesByUser?.data.length > 1 && notDeleted ? (
                          ''
                        ) : rolesByUser?.data.length > 1 && deleteRole ? (
                          <button
                            className="bg-gray-100 w-7 h-7 text-gray-500 rounded-[30px] p-1 hover:bg-gray-100"
                            onClick={() => {
                              setOpenRemoveRole(true);
                              recordRoleRef.current = r;
                            }}
                            type="button"
                          >
                            <X name="X" className=" text-current hover:text-red-500" size={20} />
                          </button>
                        ) : (
                          rolesByUser?.data.length > 1 && (
                            <button
                              className="bg-gray-100 w-7 h-7 text-gray-500 rounded-[30px] p-1 hover:bg-gray-100"
                              onClick={() => {
                                setOpenRemoveRole(true);
                                recordRoleRef.current = r;
                              }}
                              type="button"
                            >
                              <X name="X" className=" text-current hover:text-red-500" size={20} />
                            </button>
                          )
                        )}
                      </>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </div>
      <Dialog open={openRemoveRole}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[360px]">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <RemoveRoleAccount
              dataRow={recordRoleRef.current!}
              userId={dataRow?.id!}
              onClose={() => setOpenRemoveRole(false)}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </Spin>
  );
};
