'use client';

import { FileConfig } from '@/api/fileConfigClient';
import { useCreateClient } from '@/api/useCreateClient';
import ContentChangedBase from '@/components/commons/ContentChangedBase';
import { HeaderModal } from '@/components/ui/modal/header-modal';
import { messageErrorCommon } from '@/constants/defineMessages';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Form,
  FormItem,
  Input,
  toast,
} from 'ui-components';

interface ContentModalFileConfigProps {
  dataFileConfig: FileConfig;
  onClose: () => void;
  pendingFileConfig: boolean;
  isChangedData: boolean;
  setIsChangedData: Dispatch<SetStateAction<boolean>>;
  isOpenModalConfirm: boolean;
  setIsOpenModalConfirm: Dispatch<SetStateAction<boolean>>;
}

export const ContentModalFileConfig = ({
  dataFileConfig,
  onClose,
  pendingFileConfig,
  isChangedData,
  setIsChangedData,
  isOpenModalConfirm,
  setIsOpenModalConfirm,
}: ContentModalFileConfigProps) => {
  const intl = useIntl();
  const [form] = Form.useForm();

  const initialValues = useMemo(() => {
    return {
      value: dataFileConfig?.value ?? 25,
      unit: dataFileConfig?.unit ?? 'MB',
    };
  }, [dataFileConfig?.id]);

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues]);

  const { fileConfig } = useCreateClient();

  const mutationOptions = {
    invalidateQueries: { enable: true },
  };

  const { mutateAsync: updateFileConfigAsync, isPending: pendingUpdateFileConfig } =
    fileConfig.updateFileConfig(mutationOptions);
  const handleClose = useCallback(() => {
    if (isChangedData) {
      setIsOpenModalConfirm(true);
    } else {
      onClose?.();
    }
  }, [isChangedData, onClose, setIsOpenModalConfirm]);

  const handleSubmit = async (values: Record<string, any>) => {
    try {
      const payload = {
        value: Math.floor(values?.value),
        unit: values?.unit,
        status: dataFileConfig?.status,
      };
      await updateFileConfigAsync([dataFileConfig?.id!, payload]);
      // console.log('valueSubmit:', values);
      toast({
        title: `${intl.formatMessage({
          defaultMessage: 'Đã cập nhật giá trị thành công.',
          id: 'components.container.settings.components.contentModalFileConfig.updatedValueSuccess',
        })}`,
        type: 'success',
        options: {
          position: 'top-center',
        },
      });
      onClose?.();
      setIsChangedData(false);
    } catch (error) {
      toast({
        title: `${intl.formatMessage({
          defaultMessage: 'Không thể cập nhật giá trị. Vui lòng thử lại sau.',
          id: 'components.container.settings.components.contentModalFileConfig.updatedValueFail',
        })}`,
        type: 'error',
        options: {
          position: 'top-center',
        },
      });
    }
  };
  return (
    <>
      <Form
        name="fileConfigForm"
        form={form}
        onFinish={handleSubmit}
        initialValues={initialValues}
        onFinishFailed={(err) => console.log(err)}
        onFieldsChange={(_, allFields) => {
          const isValid = allFields.some((field) => {
            const fieldName = field.name[0] as 'value';
            return initialValues[fieldName] !== field.value;
          });
          setIsChangedData(isValid);
        }}
      >
        <HeaderModal
          title={
            <FormattedMessage
              defaultMessage="Giới hạn mỗi tệp"
              id="components.container.settings.components.contentModalFileConfig.limitPerFile"
            />
          }
          buttonForm={{
            content: (
              <FormattedMessage
                defaultMessage="Cập nhật"
                id="components.container.authorization.components.BaseFormData.1416668261"
              />
            ),
            type: 'submit',
            isPending: pendingFileConfig || pendingUpdateFileConfig,
          }}
          onClose={handleClose}
        />
        <div className="bg-gray-100 p-4 rounded-b-xl">
          <div className="bg-white flex flex-col rounded-xl p-3">
            <div className="flex flex-col gap-2">
              <div className="flex flex-col items-start gap-2">
                <div className="items-center space-x-2">
                  <span className="text-sm text-gray-800 font-medium">
                    <FormattedMessage
                      defaultMessage="Giá trị"
                      id="components.container.settings.components.contentModalFileConfig.valueFileConfig"
                    />
                  </span>
                  <span className="text-sm text-red-500 font-medium">*</span>
                </div>
                <FormItem
                  name="value"
                  rules={[
                    {
                      validator(rule, value, callback) {
                        if (Number(value) > 25) {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage(messageErrorCommon.fieldFileMax, {
                                max: 25,
                              }),
                            ),
                          );
                        }

                        return Promise.resolve();
                      },
                    },
                  ]}
                  renderItem={({ control, meta, form, isError }) => (
                    <div className="w-full">
                      <Input
                        placeholder={intl.formatMessage({
                          defaultMessage: 'Nhập giá trị',
                          id: 'components.container.settings.components.contentModalFileConfig.enterValue',
                        })}
                        className="placeholder-gray-500 text-sm [&_input:disabled]:text-gray-700"
                        variant={isError ? 'error' : 'default'}
                        autoComplete="value"
                        onBlur={(e) => {
                          let val = Number(e.target.value);
                          if (isNaN(val) || val < 1) val = control.value;
                          val = Math.floor(val);
                          form.setFieldValue('value', val);
                        }}
                        {...control}
                      />
                    </div>
                  )}
                />
              </div>
              <div className="flex flex-col items-start gap-2">
                <div className="items-center space-x-2">
                  <span className="text-sm text-gray-800 font-medium">
                    <FormattedMessage
                      defaultMessage="Đơn vị"
                      id="components.container.settings.components.contentModalFileConfig.unit"
                    />
                  </span>
                </div>
                <FormItem
                  name="unit"
                  renderItem={({ control, meta, form, isError }) => (
                    <div className="w-full">
                      <Input
                        className="placeholder-gray-500 text-sm [&_input:disabled]:text-gray-700"
                        onSubmitValue={control.onChange}
                        variant={isError ? 'error' : 'default'}
                        autoComplete="unit"
                        disabled={true}
                        {...control}
                      />
                    </div>
                  )}
                />
              </div>
            </div>
          </div>
        </div>
      </Form>
      <Dialog open={isOpenModalConfirm}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="max-w-[420px] p-0">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onCancel={() => setIsOpenModalConfirm(false)}
              onSubmit={() => {
                setIsOpenModalConfirm(false);
                onClose?.();
              }}
              heading={intl.formatMessage({
                defaultMessage: 'Hủy cập nhật giá trị',
                id: 'components.container.settings.components.contentModalConfig.cancelUpdatingValue',
              })}
              description={intl.formatMessage({
                defaultMessage: 'Bạn có chắc muốn hủy cập nhật giá trị? Giá trị mà bạn vừa nhập sẽ không được lưu lại.',
                id: 'components.container.settings.components.contentModalConfig.AreYouSureCancelUpdatingValue',
              })}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
};
