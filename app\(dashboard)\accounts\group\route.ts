import apiPathName from '@/api/apiPathName';
import { BACKEND_API_ENDPOINT, BASE_PATH } from '@/constants';
import { urlPathName } from '@/constants/urlPathName';
import { cookies } from 'next/headers';
import { redirect } from 'next/navigation';

export async function GET() {
  const cookieStore = await cookies();
  const response = await fetch(`${BACKEND_API_ENDPOINT}${apiPathName.groupUser.root}/all`, {
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      'x-api-key-client': 'config:web',
      cookie: cookieStore.toString(),
    },
  });
  const results = await response.json();
  const groupId = results[0]?.id ?? 'not-found';
  const url = `${BASE_PATH}${urlPathName.ACCOUNT.GROUP_ACCOUNT}/${groupId}`;
  return redirect(url);
}
