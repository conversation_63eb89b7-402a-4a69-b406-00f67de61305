'use client';

import { GroupUser, Users } from '@/api/dataType';
import { SortEnum } from '@/api/type';
import { useCreateClient } from '@/api/useCreateClient';
import ContentChangedBase from '@/components/commons/ContentChangedBase';
import InputLanguage from '@/components/ui/input-language';
import { HeaderModal } from '@/components/ui/modal/header-modal';
import { BACKEND_API_ENDPOINT, BASE_PATH } from '@/constants';
import { messageAlertCommon, messageErrorCommon, messagePlaceholder } from '@/constants/defineMessages';
import { generateNums } from '@/utils';
import { capitalizeFirstLetter } from '@/utils/string';
import { cn } from '@/utils/tailwind';
import { Check } from '@phosphor-icons/react';
import { useQueryClient } from '@tanstack/react-query';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import { Dispatch, SetStateAction, useCallback, useEffect, useMemo, useState } from 'react';
import { FormattedMessage, useIntl } from 'react-intl';
import {
  Badge,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Form,
  FormItem,
  MultipleSelectGroup,
  toast,
} from 'ui-components';

interface GroupAccountFormProps {
  onClose?: () => void;
  dataGroupAccount?: GroupUser;
  isChangedData: boolean;
  setIsChangedData: Dispatch<SetStateAction<boolean>>;
  isOpenConfirm: boolean;
  setIsOpenConfirm: Dispatch<SetStateAction<boolean>>;
}

const dataEmpty = {
  imageEmpty: `${BASE_PATH}/images/commons/no-general-data.svg`,
  title: (
    <FormattedMessage
      defaultMessage="Chưa có tài khoản"
      id="components.container.group-account.components.GroupAccountForm.noAccountsYet"
    />
  ),
  description: (
    <FormattedMessage
      defaultMessage="Thêm các tài khoản để dễ dàng quản lý và phân quyền truy cập."
      id="components.container.accounts.UserAccountPage.addAccToEasilyManage "
    />
  ),
};

export const GroupAccountForm = ({
  onClose,
  dataGroupAccount,
  isChangedData,
  setIsChangedData,
  isOpenConfirm,
  setIsOpenConfirm,
}: GroupAccountFormProps) => {
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  const [form] = Form.useForm();
  const router = useRouter();
  const [selectAccountGroup, setSelectAccountGroup] = useState<string[] | []>(
    dataGroupAccount?.users.map((item) => item.id) || [],
  );

  const isUpdate = Boolean(dataGroupAccount?.id);

  const { users, groupUsers } = useCreateClient();
  const { data } = users.find();

  const dataAccount = data?.data?.list as Users[];

  const initValue = {
    name: {
      vi: '',
      en: '',
    },
    description: {
      vi: '',
      en: '',
    },
    groupUserId: [] as string[],
  };

  const initialValues = useMemo(() => {
    if (dataGroupAccount && dataGroupAccount?.id) {
      return {
        name: {
          vi: dataGroupAccount?.name?.vi,
          en: dataGroupAccount?.name?.en,
        },
        groupUserId: dataGroupAccount?.users.map((item) => item.id),
        description: {
          vi: dataGroupAccount?.description?.vi,
          en: dataGroupAccount?.description?.en,
        },
      };
    }
    return initValue;
  }, []);

  useEffect(() => {
    form.setFieldsValue(initialValues);
  }, [initialValues.description, initialValues.groupUserId, initialValues.name]);

  const dataAccountCustom = dataAccount?.map((item: any) => {
    const num = generateNums(item?.id);
    const urlAvatar = item?.avatar ? item?.avatar : `/config/images/avatars/avatar-${num}.svg`;
    return {
      title: `${locale === 'vi' ? item.username.vi : item.username.en} ${locale === 'vi' ? item.givenName.vi : item.givenName.en}`,
      value: item.id,
      email: item.email,
      avatar: item.avatar ? item.avatar : urlAvatar,
    };
  });
  const titleModal: any = isUpdate ? (
    <FormattedMessage
      defaultMessage="Thông tin nhóm tài khoản"
      id="components.container.group-account.components.GroupAccountForm.infoGroupAccount"
    />
  ) : (
    <FormattedMessage
      defaultMessage="Tạo nhóm tài khoản mới"
      id="components.container.group-account.components.GroupAccountForm.createGroupAccountNew"
    />
  );

  const handleClose = useCallback(() => {
    if (isChangedData) {
      setIsOpenConfirm(true);
    } else {
      onClose?.();
    }
  }, [isChangedData, onClose, setIsOpenConfirm]);

  const mutationOptions = {
    invalidateQueries: { enable: true },
  };

  const { mutateAsync: createUserAsync, isPending: isPendingCreate } = groupUsers.create(mutationOptions);

  const { mutateAsync: updateGroupUserAsync, isPending: isPendingUpdate } = users.updateGroupUser(
    mutationOptions,
    //   {
    //   invalidateQueries: [
    //     {
    //       enable: true,
    //     },
    //     {
    //       enable: true,
    //       queryKey: ['GroupUsersClient'],
    //       refetchType: 'all',
    //     },
    //   ],
    // }
  );
  const { mutate: groupUserUpdate } = groupUsers.update(mutationOptions);

  const onSubmit = async (value: GroupUser) => {
    try {
      const body = {
        name: value.name,
        groupUserId: value.groupUserId,
        description: value.description,
      };
      if (isUpdate) {
        await updateGroupUserAsync([dataGroupAccount?.id!, body]);
        groupUserUpdate([dataGroupAccount?.id!, body]);
      } else {
        const result = await createUserAsync(body);
        if ('data' in result) {
          const newId = result.data.id;
          router.push(`/accounts/group/${newId}`);
        }
      }
      const title = isUpdate ? messageAlertCommon.updateGroupSuccess : messageAlertCommon.createSuccess;
      toast({
        title: `${capitalizeFirstLetter(
          intl.formatMessage(title, {
            type: intl.formatMessage({
              defaultMessage: 'nhóm tài khoản',
              id: 'components.container.group-account.components.GroupAccountForm.accountGroupLower',
            }),
          }),
        )}`,
        type: 'success',
        options: {
          position: 'top-center',
        },
      });
      // queryClient.invalidateQueries({
      //   queryKey: ['UsersClient'],
      //   refetchType: 'all',
      // });
    } catch (error) {
      const title = isUpdate ? messageAlertCommon.updateGroupFail : messageAlertCommon.createFail;
      toast({
        title: `${capitalizeFirstLetter(
          intl.formatMessage(title, {
            type: intl.formatMessage({
              defaultMessage: 'nhóm tài khoản',
              id: 'components.container.group-account.components.GroupAccountForm.accountGroupLower',
            }),
          }),
        )}`,
        type: 'error',
        options: {
          position: 'top-center',
        },
      });
    }
    onClose?.();
  };

  return (
    <>
      <Form
        form={form}
        initialValues={initialValues}
        onFinish={onSubmit}
        onFieldsChange={(_, allFields) => {
          const isValid = allFields.map((field) => {
            const fieldName = field.name[0] as 'name' | 'description' | 'groupUserId';
            if (initialValues[fieldName] !== field.value) {
              setSelectAccountGroup(field.value);
            }
            return field.touched;
          });
          setIsChangedData(isValid.some(Boolean));
        }}
      >
        <HeaderModal
          title={titleModal}
          buttonForm={{
            content: isUpdate ? (
              <FormattedMessage
                defaultMessage="Cập nhật"
                id="components.container.authorization.components.BaseFormData.**********"
              />
            ) : (
              <FormattedMessage
                defaultMessage="Tạo mới"
                id="components.container.authorization.components.BaseFormData.**********"
              />
            ),
            type: 'submit',
            isPending: isPendingCreate || isPendingUpdate,
          }}
          onClose={handleClose}
        />
        {/* max-h-[calc(100vh_-_100px)] overflow-y-auto */}
        <div className="p-5 flex flex-row gap-4 self-stretch bg-gray-100 rounded-b-xl w-[900px] max-h-[calc(100vh_-_136px)] overflow-auto">
          <div className="flex flex-1 gap-4 h-full">
            <div className="flex flex-1 flex-col gap-4 p-4 rounded-xl bg-white h-auto overflow-hidden min-h-full">
              <span className="text-base font-semibold text-gray-700">
                <FormattedMessage
                  defaultMessage="Thông tin tổng quan"
                  id="components.container.group-account.components.GroupAccountForm.generalInfo"
                />
              </span>
              <div className="flex flex-row gap-4 ">
                <div className="flex flex-col items-start gap-2 flex-grow flex-shink-0 basis-0">
                  <div className="items-center space-x-2">
                    <span className="text-sm text-gray-800 font-medium">
                      <FormattedMessage
                        defaultMessage="Tên nhóm tài khoản"
                        id="components.container.group-account.components.GroupAccountForm.accountGroupName"
                      />
                    </span>
                    <span className="text-sm text-red-500 font-medium">*</span>
                  </div>
                  <Form.Field
                    name="name"
                    rules={[
                      {
                        validator(rule, value, callback) {
                          if (!(value.vi.length > 0)) {
                            return Promise.reject(
                              new Error(
                                intl.formatMessage(messageErrorCommon.fieldRequired, {
                                  field: intl.formatMessage({
                                    defaultMessage: 'Tên nhóm tài khoản',
                                    id: 'components.container.group-account.components.GroupAccountForm.accountGroupName',
                                  }),
                                }),
                              ),
                            );
                          }

                          if (value.vi.length > 64) {
                            return Promise.reject(
                              new Error(
                                intl.formatMessage(messageErrorCommon.fieldMax, {
                                  max: 64,
                                }),
                              ),
                            );
                          }

                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    {({ value, onChange }, meta) => (
                      <InputLanguage
                        placeholder={intl.formatMessage({
                          defaultMessage: 'Nhập tên nhóm tài khoản',
                          id: 'components.container.group-account.components.GroupAccountForm.enterAccountGroupName',
                        })}
                        name="givenName"
                        meta={meta}
                        value={value}
                        onChange={onChange}
                        maxLenght={64}
                      />
                    )}
                  </Form.Field>
                </div>
              </div>
              <div className="flex flex-col items-start gap-2 self-stretch h-full">
                <div className="space-x-2">
                  <span className="text-sm text-gray-800 font-medium">
                    <FormattedMessage
                      defaultMessage="Mô tả nhóm tài khoản"
                      id="components.container.group-account.components.GroupAccountForm.descriptionAccountGroup"
                    />
                  </span>
                </div>
                <Form.Field
                  name="description"
                  rules={[
                    {
                      validator(rule, value, callback) {
                        if (value?.vi?.length > 256) {
                          return Promise.reject(
                            new Error(
                              intl.formatMessage(messageErrorCommon.fieldMax, {
                                max: 256,
                              }),
                            ),
                          );
                        }

                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  {({ value, onChange }, meta) => (
                    <InputLanguage
                      placeholder={intl.formatMessage({
                        defaultMessage: 'Nhập mô tả nhóm tài khoản.',
                        id: 'components.container.group-account.components.GroupAccountForm.enterDesGroupAccount',
                      })}
                      name="description"
                      meta={meta}
                      value={value}
                      onChange={onChange}
                      type="textarea"
                      maxLenght={256}
                      maxHeight={true}
                    />
                  )}
                </Form.Field>
              </div>
            </div>
            <div className="flex flex-1 flex-col gap-4 p-4 rounded-xl bg-white  h-auto overflow-hidden min-h-full">
              <div className="flex flex-col items-start gap-2 self-stretch">
                <div className="flex justify-between items-center w-full">
                  <span className="text-base font-semibold text-gray-700">
                    <FormattedMessage
                      defaultMessage="Danh sách tài khoản"
                      id="components.container.group-account.components.GroupAccountForm.listGroupAccount"
                    />
                  </span>
                  <Badge
                    className={cn('shadow-none text-primary-500', {
                      'bg-gray-100 hover:bg-gray-100 text-gray-700': selectAccountGroup.length === 0,
                    })}
                  >
                    {selectAccountGroup.length > 0 ? selectAccountGroup.length : 0}
                  </Badge>
                </div>
                <FormItem
                  name="groupUserId"
                  renderItem={({ control, meta, form, isError }) => {
                    const title = messagePlaceholder.search;
                    return (
                      <MultipleSelectGroup
                        options={dataAccountCustom! || []}
                        variant={isError ? 'error' : 'default'}
                        className="text-center h-full"
                        onChange={control.onChange}
                        value={control.value}
                        size="md"
                        modalPopover
                        placeholder={intl.formatMessage({
                          defaultMessage: 'Thêm tài khoản vào danh sách',
                          id: 'components.container.group-account.components.GroupAccountForm.addAccToList',
                        })}
                        sideOffset={-80}
                        dataEmpty={dataEmpty}
                        searchEmpty={<SearchEmpty />}
                        searchInputOption={{
                          placeholderSearch: `${intl.formatMessage(title)}`,
                        }}
                        isGroupAccount={true}
                        isSearchable={true}
                        {...control}
                        customValue={(value, options) => {
                          const title = options.find((item) => item.value === value.id)?.title;
                          const email = options.find((item) => item.email === value.email)?.email;
                          const num = generateNums(value?.id);
                          const urlAvatar = value?.avatar ? value?.avatar : `/config/images/avatars/avatar-${num}.svg`;
                          return (
                            <>
                              <Image
                                src={urlAvatar}
                                alt="avatar"
                                width={26}
                                height={26}
                                className="rounded-full flex-shrink-0"
                                unoptimized
                                priority
                              />
                              <div className="flex gap-1 flex-col w-[80%] text-left">
                                <span className="truncate text-sm text-gray-700" title={title}>
                                  {title}
                                </span>
                                <span className="truncate text-xs text-gray-700" title={email}>
                                  {email}
                                </span>
                              </div>

                              <div
                                className={cn(
                                  'w-5 h-5 bg-gray-100 border border-gray-200 rounded group-hover:border-primary-200 flex items-end justify-end',
                                  'group-[&.selected]:bg-primary-500 group-[&.selected]:border-primary-500',
                                )}
                              >
                                <Check
                                  className="ml-auto hidden group-[&.selected]:block group-[&.selected]:text-white"
                                  size={20}
                                />
                              </div>
                            </>
                          );
                        }}
                      />
                    );
                  }}
                  validateTrigger="onBlur"
                />
              </div>
            </div>
          </div>
        </div>
      </Form>
      <Dialog open={isOpenConfirm}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="max-w-[420px] p-0">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onCancel={() => setIsOpenConfirm(false)}
              onSubmit={() => {
                setIsOpenConfirm(false);
                onClose?.();
              }}
              heading={
                isUpdate
                  ? intl.formatMessage({
                      defaultMessage: 'Hủy cập nhật nhóm tài khoản',
                      id: 'components.container.group-account.components.GroupAccountForm.cancelUpdateInfoGroupAccount',
                    })
                  : intl.formatMessage({
                      defaultMessage: 'Hủy tạo nhóm tài khoản mới',
                      id: 'components.container.group-account.components.GroupAccountForm.cancelNewCreateGroupAccount',
                    })
              }
              description={
                isUpdate
                  ? intl.formatMessage({
                      defaultMessage:
                        'Bạn có chắc muốn hủy cập nhật nhóm tài khoản? Các thông tin mà bạn vừa nhập sẽ không được lưu lại.',
                      id: 'components.container.group-account.components.GroupAccountForm.areYouSureCancelUpdateGroupAccount',
                    })
                  : intl.formatMessage({
                      defaultMessage:
                        'Bạn có chắc muốn hủy tạo nhóm tài khoản mới? Các thông tin mà bạn vừa nhập sẽ không được lưu lại.',
                      id: 'components.container.group-account.components.GroupAccountForm.areYouSureCancelNewCreateGroupAccount',
                    })
              }
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
};

const SearchEmpty = () => {
  return (
    <div className="flex flex-col justify-center items-center gap-5 flex-1 self-stretch">
      <div className="flex w-[350px] flex-col items-center gap-5">
        <div className="flex flex-col items-center">
          <Image
            src={`${BASE_PATH}/images/commons/no-search-result.svg`}
            width={180}
            height={180}
            alt="no-search-result"
            unoptimized
            priority
          />
          <div className="flex flex-col gap-2 self-stretch max-h-[114px]">
            <div className="text-gray-800 text-center text-lg font-semibold">
              <FormattedMessage defaultMessage="Không tìm thấy kết quả" id="components.ui.base-table.**********" />
            </div>
            <div className="text-gray-500 text-center text-sm font-normal">
              <FormattedMessage
                defaultMessage="Rất tiếc, không có kết quả nào phù hợp với từ khóa bạn đã tìm kiếm. Vui lòng kiểm tra lại từ khóa hoặc thử tìm kiếm với từ khóa khác."
                id="components.ui.base-table.notEmptyResult"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
