'use client';

import HeaderFilter from '@/components/commons/HeaderFilter';
import { ActionProp, ToolBarProp, UpdateTimeProp } from '@/constants/types';
import { cn } from '@/utils/tailwind';

type HeaderProp = {
  renderElement: (UpdateTimeProp | ToolBarProp | ActionProp)[];
  pageTitle?: React.JSX.Element | string;
  className?: string;
};
const ApplicationHeaderPage = ({ renderElement, pageTitle, className }: HeaderProp) => {
  return (
    <div
      className={cn(
        'h-[64px] w-full bg-white flex-shrink-0 px-5 py-3 flex flex-row justify-between items-center gap-5',
        className,
      )}
    >
      {pageTitle ? (
        pageTitle
      ) : (
        <div>
          <span>Tổng quan</span>
        </div>
      )}
      <HeaderFilter renderElement={renderElement} />
    </div>
  );
};
export { ApplicationHeaderPage };
