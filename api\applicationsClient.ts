import { BACKEND_API_ENDPOINT } from '@/constants';
import apiPathName from './apiPathName';
import { BaseClient } from './baseClient';
import { Applications } from './dataType';

export class ApplicationsClient extends BaseClient<Applications> {
  moduleName = 'ApplicationsClient';
  constructor() {
    super(BACKEND_API_ENDPOINT + apiPathName.application.root);
  }

  getApplicationWithPermissions(): Promise<Applications[]> {
    return this.api.get(apiPathName.application.applicationPermissions);
  }
}
