export type LangType = {
  vi: string;
  en: string;
};

export type ApplicationFileType = {
  id: string;
  application: Partial<Applications>;
  enabled: boolean;
  fileType: string;
  originalEnabled: string;
  createdAt: string;
  updatedAt: string;
};

export type Applications = {
  id: string;
  name: string;
  description?: LangType;
  isActive: boolean;
  logtoId: string;
  metadata: Record<string, any> | null;
  order: number;
  signature: string;
  type: string;
  url: string;
  version: string;
  createdAt: string;
  updatedAt: string;
  permissions?: Permission[];
  allowWeb?: boolean;
  allowMobile?: boolean;
};

export type Permission = {
  application: string;
  id: string;
  keyDescription: string;
  keyName: string;
  logtoId: string;
  parentKeyName: string;
  order: number;
};

export type Users = {
  id: string;
  username: LangType;
  email: string;
  description: LangType;
  avatar: string;
  phone: string;
  birthdate: string;
  customData: Record<string, any>;
  gender: string;
  givenName: LangType;
  roles: Roles[];
  groups: GroupUser[];
  isActive: boolean;
  isDeleted: boolean;
  role_id: string[];
  group_account: string[];
  lastSignInAt: string;
  logtoId: string;
  address: Record<string, any>;
  createdAt: string;
  updatedAt: string;
};

export type GroupUser = {
  id: string;
  description: LangType;
  name: LangType;
  groupUserId: string[];
  roles: Roles[];
  users: Users[];
  createdAt: string;
  updateAt: string;
};

export type Roles = {
  id: string;
  name: LangType;
  description: LangType;
  applications: (Partial<Applications> | { [key: string]: any })[];
  users: Users[];
  isAdmin: boolean;
  logtoId: string;
  createdAt: string;
  updatedAt: string;
};
