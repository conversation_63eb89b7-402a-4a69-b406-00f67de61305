import { BASE_PATH } from '@/constants';
import { cn } from '@/utils/tailwind';
import Image from 'next/image';
import { PropsWithChildren } from 'react';
import { FormattedMessage } from 'react-intl';
import { Button } from 'ui-components';

export default function ActionBaseContent({
  children,
  onCancel,
  onSubmit,
  className,
  disabled,
  imageName,
}: PropsWithChildren & {
  onCancel?: () => void;
  onSubmit?: () => void;
  className?: string;
  disabled?: boolean;
  imageName?: string;
}) {
  const checkDangerButton = imageName === undefined ? true : false;
  return (
    <div className="flex flex-col flex-1 min-w-0">
      <div
        className={cn('flex flex-col justify-center items-center p-5', {
          'gap-5': imageName === 'active-image' || imageName === 'inactive-image',
        })}
      >
        <Image
          src={`${BASE_PATH}/images/commons/${imageName || 'delete-image'}.svg`}
          alt="deleted-image"
          width={200}
          height={200}
          className="h-50 w-50 object-contain flex-shrink-0"
          unoptimized
          priority
        />
        <div className={cn('w-full h-full flex-1', className)}>{children}</div>
      </div>
      <div className="flex flex-row justify-end gap-3 p-[14px] border-t border-gray-200">
        <Button type="button" variant="gray" className="flex-1 text-primary-500 text-sm font-normal" onClick={onCancel}>
          <FormattedMessage
            defaultMessage="Hủy bỏ"
            id="components.container.authorization.components.DeletedRole.780985299"
          />
        </Button>
        <Button
          type="button"
          variant={`${checkDangerButton ? 'danger' : 'primary'}`}
          className="flex-1 text-sm font-normal"
          onClick={onSubmit}
          disabled={disabled}
        >
          <FormattedMessage
            defaultMessage="Xác nhận"
            id="components.container.authorization.components.DeletedRole.559188735"
          />
        </Button>
      </div>
    </div>
  );
}
