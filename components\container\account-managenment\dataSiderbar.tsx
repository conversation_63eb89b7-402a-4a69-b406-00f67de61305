'use client';

import { urlPathName } from '@/constants/urlPathName';
import { FormattedMessage } from 'react-intl';
import { IdentificationCard, User } from '@phosphor-icons/react';
import { Access, Scopes } from '@/constants/scopes';
import type { UrlObject } from 'url';

export type Url = string | UrlObject;
export type ChildTag = {
  label: string | React.JSX.Element;
  icon: React.JSX.Element;
  path: Url;
  access: Access;
};

export type DataTabs = {
  name: string | React.JSX.Element;
  childTag: ChildTag[];
};

export const dataTabsSetting: DataTabs[] = [
  {
    name: (
      <FormattedMessage
        defaultMessage="Quản lý tài khoản"
        id="components.container.account-managenment.dataSiderbar.**********"
      />
    ),
    childTag: [
      {
        label: (
          <FormattedMessage
            defaultMessage="Tài khoản"
            id="components.container.account-managenment.dataSiderbar.**********"
          />
        ),
        icon: <User size={20} className="text-current" weight="regular" />,
        path: urlPathName.ACCOUNT.ROOT,
        access: Scopes.UserConfigScopes.READ,
      },
      {
        label: (
          <FormattedMessage
            defaultMessage="Nhóm quyền"
            id="components.container.account-managenment.dataSiderbar.*********"
          />
        ),
        icon: <IdentificationCard size={20} className="text-current" weight="regular" />,
        path: urlPathName.ACCOUNT.AUTHORIZATION,
        access: Scopes.RoleConfigScopes.READ,
      },
    ],
  },
];
