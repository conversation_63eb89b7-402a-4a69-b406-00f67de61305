import { BaseTable } from '@/components/ui/base-table';
import { LIMIT_ITEM_PER_PAGE } from '@/constants';
import { cn } from '@/utils/tailwind';
import { DotsThreeVertical, Trash, UsersThree } from '@phosphor-icons/react';
import { PropsWithChildren, useMemo, useState } from 'react';
import { defineMessages, FormattedMessage, useIntl } from 'react-intl';
import {
  Button,
  ColumnsType,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
  Popover,
  PopoverContent,
  PopoverTrigger,
  Spin,
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from 'ui-components';
import BaseFormData from './components/BaseFormData';
import DeletedRole from './components/DeletedRole';
import ContentChangedBase from '@/components/commons/ContentChangedBase';
import PaginationTable from '@/components/commons/PaginationTable';
import { useCreateClient } from '@/api/useCreateClient';
import { useQueryState } from 'nuqs';
import { usePermissionModule } from '@/hook/usePermissionModule';
import { moduleScope, Scopes } from '@/constants/scopes';
import { Applications, Roles, Users } from '@/api/dataType';

const messagePage = defineMessages({
  roleName: {
    defaultMessage: 'Tên nhóm quyền',
    id: 'components.container.authorization.AuthorizationContainer.**********',
  },
  descriptions: {
    defaultMessage: 'Mô tả',
    id: 'components.container.authorization.AuthorizationContainer.********',
  },
  accounts: {
    defaultMessage: 'Tài khoản',
    id: 'components.container.authorization.AuthorizationContainer.**********',
  },
  connected: {
    defaultMessage: 'Truy cập',
    id: 'components.container.authorization.AuthorizationContainer.**********',
  },
});

export const AuthorizationTable = () => {
  const [stateModal, setStateModal] = useState({
    isOpenModalForm: false,
    isOpenModalChanged: false,
    isChangedData: false,
  });
  const [oTherState, setOtherState] = useState({
    isOpenModalDelete: false,
    data: {},
  });
  const intl = useIntl();
  const locale = intl.locale as 'vi' | 'en';
  const limit = LIMIT_ITEM_PER_PAGE;
  const [page, setPage] = useQueryState('page', {
    defaultValue: '1',
    clearOnDefault: true,
  });
  const actionRoles = usePermissionModule(moduleScope.ROLES, Scopes.RoleConfigScopes);

  const { roles } = useCreateClient();
  const {
    data: dataRole,
    isFetching,
    isLoading,
  } = roles.find(
    {},
    {
      pageSize: limit,
      pageIndex: Number(page) - 1,
    },
  );

  const columns: ColumnsType<Roles> = useMemo(
    () => [
      {
        title: intl.formatMessage(messagePage.roleName),
        dataIndex: 'name',
        key: 'name',
        width: '40%',
        render: (record: { vi: string; en: string }) => {
          return (
            <div className="flex flex-row gap-2 w-full items-center">
              <Tooltip>
                <TooltipTrigger asChild>
                  <span className="truncate font-semibold text-sm w-fit">{record[locale]}</span>
                </TooltipTrigger>
                <TooltipContent
                  className="px-3 py-2 text-sm font-normal text-gray-100 bg-gray-700 rounded-lg shadow max-w-[426px]"
                  side="top"
                  align="start"
                >
                  {record[locale]}
                </TooltipContent>
              </Tooltip>
            </div>
          );
        },
      },
      {
        title: intl.formatMessage(messagePage.descriptions),
        dataIndex: 'description',
        key: 'description',
        width: '60%',
        render: (record: { vi: string; en: string } | null) => {
          return (
            <div className="block">
              {record ? (
                <Tooltip>
                  <TooltipTrigger asChild>
                    <span className="truncate block w-fit">{record[locale]}</span>
                  </TooltipTrigger>
                  <TooltipContent
                    className="px-3 py-2 text-sm font-normal text-gray-100 bg-gray-700 rounded-lg shadow max-w-[426px]"
                    side="top"
                    align="start"
                  >
                    {record[locale]}
                  </TooltipContent>
                </Tooltip>
              ) : (
                <span className="truncate block w-fit"></span>
              )}
            </div>
          );
        },
      },
      {
        title: intl.formatMessage(messagePage.accounts),
        dataIndex: 'users',
        key: 'users',
        width: 150,
        render: (record: Users[]) => {
          return (
            <div className="flex flex-row items-center gap-2 text-primary-500">
              <UsersThree className="text-current" size={20} weight="regular" />
              <span>{record?.length}</span>
            </div>
          );
        },
      },
      {
        title: intl.formatMessage(messagePage.connected),
        dataIndex: 'applications',
        key: 'applications',
        width: 150,
        render: (record: Applications[]) => {
          const appConnect = record.filter((item) => item.allowMobile || item.allowWeb);
          return (
            <div>
              <FormattedMessage
                defaultMessage="{value, plural, one {{value, number} {application}} other {{value, number} {applications}}}"
                id="components.container.authorization.AuthorizationTable.*********"
                values={{
                  value: appConnect.length,
                  application: intl.formatMessage({
                    defaultMessage: 'sản phẩm',
                    id: 'components.container.authorization.AuthorizationTable.291548126_1',
                  }),
                  applications: intl.formatMessage({
                    defaultMessage: 'sản phẩm',
                    id: 'components.container.authorization.AuthorizationTable.291548126_2',
                  }),
                }}
              />
            </div>
          );
        },
      },
      ...(actionRoles.DELETE
        ? [
            {
              title: '',
              dataIndex: '',
              key: 'action',
              width: 60,
              className: 'h-full min-w-[60px]',
              render: (record: Roles) => {
                if (record.isAdmin) {
                  return (
                    <Button
                      type="button"
                      icon
                      disabled
                      variant="neutral"
                      className="h-6 min-w-6 w-6 flex items-center justify-center text-gray-500 hover:bg-gray-200 hover:rounded-lg p-0 m-[0_auto]"
                    >
                      <DotsThreeVertical size={24} weight="regular" className="block text-current" />
                    </Button>
                  );
                }
                return (
                  <Popover>
                    <PopoverTrigger onClick={(event) => event.stopPropagation()} asChild>
                      <div className="h-6 max-w-6 flex items-center justify-center text-gray-500 hover:bg-gray-200 hover:rounded-lg m-[0_auto]">
                        <DotsThreeVertical size={24} weight="regular" className="block text-current" />
                      </div>
                    </PopoverTrigger>
                    <PopoverContent
                      align="center"
                      side="left"
                      className="bg-white max-w-[260px] w-auto flex flex-col rounded-xl p-2"
                    >
                      <div
                        className="flex flex-row items-center gap-2 text-red-500 cursor-pointer p-2 hover:bg-gray-100 hover:rounded-lg"
                        onClick={(event) => {
                          event.stopPropagation();
                          setOtherState((prevState) => ({
                            ...prevState,
                            isOpenModalDelete: true,
                            data: record,
                          }));
                        }}
                      >
                        <Trash weight="regular" size={20} className="text-current" />
                        <span className="text-sm font-normal text-current">
                          <FormattedMessage
                            defaultMessage="Xóa nhóm quyền"
                            id="components.container.authorization.AuthorizationContainer.*********"
                          />
                        </span>
                      </div>
                    </PopoverContent>
                  </Popover>
                );
              },
            },
          ]
        : []),
    ],
    [actionRoles.DELETE],
  );

  const dataAccount = dataRole?.data?.list ?? [];

  const totalItems = dataRole?.data?.pagination?.totalItems ?? 0;

  const onClickRow = (record: Record<string, any>) => {
    setStateModal((prevState) => ({
      ...prevState,
      isOpenModalForm: true,
    }));
    setOtherState((prevState) => ({ ...prevState, data: record }));
  };

  const onOpenChange = (open: boolean) => {
    if (stateModal.isChangedData) {
      setStateModal((prevState) => ({
        ...prevState,
        isOpenModalForm: true,
        isOpenModalChanged: true,
      }));
      return;
    }
    return setStateModal((prevState) => ({
      ...prevState,
      isOpenModalChanged: false,
      isOpenModalForm: open,
    }));
  };

  const onOpenDelete = (open: boolean) => setOtherState((prevState) => ({ ...prevState, isOpenModalDelete: open }));

  if (isLoading) {
    return <Spin loading className="[&>div]:bg-white/50 [&>div]:rounded-xl" />;
  }

  return (
    <>
      <div className="bg-white h-[calc(100%_-_64px)] w-full flex flex-col">
        <div
          className={cn('flex flex-col h-full w-full', {
            'h-[calc(100%_-_68px)]': totalItems > limit,
          })}
        >
          <div className="w-full h-auto flex-shrink-0">
            <BaseTable<Roles>
              columns={columns}
              dataSource={dataAccount}
              tableLayout="fixed"
              striped={false}
              classNameRoot="flex-shink-0 h-auto"
              className="[&_.rc-table-content]:overflow-hidden [&_thead]:h-[49px] [&_.rc-table-thead]:border-0"
              rowKey={(record: Record<string, any>) => record?.id}
              showHeader={dataAccount?.length > 0}
              components={{
                header: {
                  cell: CustomHeader,
                },
                body: {
                  cell: () => null,
                },
              }}
            />
          </div>
          <div className="table-body-custom flex-1 overflow-y-auto">
            <BaseTable<Roles>
              columns={columns}
              loading={isFetching}
              dataSource={dataAccount}
              tableLayout="fixed"
              striped={false}
              className="h-full [&_.rc-table-row:nth-child(1)]:border-t-0 [&_.rc-table-row:hover_.rc-table-cell]:bg-gray-50 [&_.rc-table-row]:cursor-pointer"
              rowKey={(record: Record<string, any>) => record?.id ?? ''}
              showHeader={false}
              components={{
                header: {
                  cell: CustomHeader,
                },
                body: {
                  cell: CustomBody,
                },
              }}
              onRow={(record, index) => ({
                onClick: () => onClickRow(record),
              })}
            />
          </div>
        </div>
        {totalItems > limit && <PaginationTable totalItems={totalItems} limit={limit} />}
      </div>
      <Dialog open={stateModal.isOpenModalForm} onOpenChange={onOpenChange}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[680px] max-h-[610px] sm:rounded-xl">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <BaseFormData
              onClose={() => onOpenChange(false)}
              data={oTherState.data}
              stateModal={stateModal}
              setStateModal={setStateModal}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>

      <Dialog open={oTherState.isOpenModalDelete} onOpenChange={onOpenDelete}>
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[450px] max-h-[598px] sm:rounded-xl">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <DeletedRole onClose={() => onOpenDelete(false)} data={oTherState.data} />
          </DialogContent>
        </DialogPortal>
      </Dialog>

      <Dialog
        open={stateModal.isOpenModalChanged}
        onOpenChange={(open) =>
          setStateModal((prevState) => ({
            ...prevState,
            isOpenModalChanged: open,
          }))
        }
      >
        <DialogPortal>
          <DialogOverlay />
          <DialogContent className="p-0 border-none gap-0 max-w-[420px] max-h-[190px] sm:rounded-xl">
            <DialogTitle className="hidden"></DialogTitle>
            <DialogDescription className="hidden"></DialogDescription>
            <ContentChangedBase
              onCancel={() =>
                setStateModal((prevState) => ({
                  ...prevState,
                  isOpenModalChanged: false,
                }))
              }
              onSubmit={() =>
                setStateModal((prevState) => ({
                  ...prevState,
                  isOpenModalChanged: false,
                  isOpenModalForm: false,
                  isChangedData: false,
                }))
              }
              heading={intl.formatMessage({
                id: 'components.container.authorization.AuthorizationTable.1083435555',
                defaultMessage: 'Hủy cập nhật thông tin nhóm quyền',
              })}
              description={intl.formatMessage({
                id: 'components.container.authorization.AuthorizationTable.1155716017',
                defaultMessage:
                  'Bạn có chắc muốn hủy cập nhật thông tin nhóm quyền? Các thông tin mà bạn vừa nhập sẽ không được lưu lại.',
              })}
            />
          </DialogContent>
        </DialogPortal>
      </Dialog>
    </>
  );
};

const CustomHeader = ({ children, ...restProps }: PropsWithChildren) => {
  const { className }: { className?: string } = restProps;
  return (
    <th {...restProps} className={cn(className, '!bg-gray-50')}>
      <span className="w-full line-clamp-1">{children}</span>
    </th>
  );
};

const CustomBody = ({ children, ...restProps }: PropsWithChildren) => {
  const { title }: { title?: string; className?: string } = restProps;
  if (title) {
    return (
      <td {...restProps}>
        <span className="line-clamp-1">{children}</span>
      </td>
    );
  }
  return <td {...restProps}>{children}</td>;
};
