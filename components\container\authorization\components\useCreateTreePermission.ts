import { Permission } from '@/api/dataType';
import { Scopes } from '@/constants/scopes';
import { useIntl } from 'react-intl';
import { TreeDataType } from 'ui-components';

// <PERSON><PERSON><PERSON> ngh<PERSON>a các key cần gộp lại
const MERGED_PARENT_KEYS = new Set(['document:category-document', 'document:categories', 'document:documents']);

const MERGED_TARGET_KEY = 'document:category-document';

type GroupedByParent = {
  [parentKeyName: string]: Permission[];
};

const groupByParentKeyName = (data: Permission[]): GroupedByParent => {
  const grouped: GroupedByParent = {};

  for (const item of data.sort((a, b) => a.order - b.order)) {
    const originalParent = item.parentKeyName;
    const groupKey = MERGED_PARENT_KEYS.has(originalParent) ? MERGED_TARGET_KEY : originalParent;

    if (!grouped[groupKey]) {
      grouped[groupKey] = [];
    }

    grouped[groupKey].push(item);
  }

  return grouped;
};

function buildTreeData(data: GroupedByParent, moduleName: Record<string, string>, controlKey = 'read'): TreeDataType[] {
  const mergedData: Record<string, Permission[]> = {};

  // Gom nhóm các quyền cần gộp
  for (const [key, items] of Object.entries(data)) {
    const targetKey = MERGED_PARENT_KEYS.has(key) ? MERGED_TARGET_KEY : key;
    if (!mergedData[targetKey]) mergedData[targetKey] = [];
    mergedData[targetKey].push(...items);
  }

  // Tạo tree data
  const treeData: TreeDataType[] = Object.entries(mergedData)
    .map(([parentKey, permissions]) => {
      const children = permissions
        .sort((a, b) => a.order - b.order)
        .map((permission) => {
          const [app, module, feature] = permission.keyName.split(':');
          return {
            value: permission.keyName,
            label: moduleName[permission.keyName] || permission.keyDescription,
            isControl: feature === controlKey,
            checked: false,
          };
        })
        .sort();

      if (children.length === 1) {
        return {
          value: children[0].value,
          label: moduleName[parentKey] || parentKey,
          checked: false,
        };
      }
      return {
        value: parentKey,
        label: moduleName[parentKey] || parentKey,
        checked: false,
        children,
      };
    })
    .sort();

  return treeData;
}

export const useCreateTreePermission = (
  permission: Permission[],
  options: { controlKey?: string } = {
    controlKey: 'read',
  },
) => {
  const intl = useIntl();
  const moduleName = Object.freeze({
    //parent module
    'config:general': intl.formatMessage({
      defaultMessage: 'Tổng quan',
      id: 'components.container.authorization.components.usePrepare.457600201',
    }),
    'config:users': intl.formatMessage({
      defaultMessage: 'Quản lý tài khoản',
      id: 'components.container.authorization.components.usePrepare.1502564156',
    }),
    'config:roles': intl.formatMessage({
      defaultMessage: 'Quản lý nhóm quyền',
      id: 'components.container.authorization.components.usePrepare.122188756',
    }),
    'config:user-groups': intl.formatMessage({
      defaultMessage: 'Quản lý nhóm tài khoản',
      id: 'components.container.authorization.components.usePrepare.418725100',
    }),
    'config:monitoring-stations': intl.formatMessage({
      defaultMessage: 'Trạm quan trắc',
      id: 'components.container.authorization.components.usePrepare.1630238613',
    }),
    'config:settings': intl.formatMessage({
      defaultMessage: 'Cài đặt',
      id: 'components.container.authorization.components.usePrepare.1549596250',
    }),
    'document:dashboard': intl.formatMessage({
      defaultMessage: 'Trang chủ',
      id: 'components.container.authorization.components.usePrepare.1251504258',
    }),
    'document:category-document': intl.formatMessage({
      defaultMessage: 'Quản lý danh mục & hồ sơ',
      id: 'components.container.authorization.components.usePrepare.2101687191',
    }),
    'document:document-templates': intl.formatMessage({
      defaultMessage: 'Quản lý hồ sơ mẫu',
      id: 'components.container.authorization.components.usePrepare.202899676',
    }),
    'document:files': intl.formatMessage({
      defaultMessage: 'Quản lý tệp tin',
      id: 'components.container.authorization.components.usePrepare.500372936',
    }),
    'document:projects': intl.formatMessage({
      defaultMessage: 'Quản lý dự án',
      id: 'components.container.authorization.components.usePrepare.2064093282',
    }),
    //config permission
    [Scopes.UserConfigScopes.READ]: intl.formatMessage({
      defaultMessage: 'Xem danh sách tài khoản',
      id: 'components.container.authorization.components.usePrepare.1500229750',
    }),
    [Scopes.UserConfigScopes.CREATE]: intl.formatMessage({
      defaultMessage: 'Tạo tài khoản',
      id: 'components.container.authorization.components.usePrepare.110840860',
    }),
    [Scopes.UserConfigScopes.UPDATE]: intl.formatMessage({
      defaultMessage: 'Cập nhật tài khoản',
      id: 'components.container.authorization.components.usePrepare.1020568779',
    }),
    [Scopes.UserConfigScopes.DELETE]: intl.formatMessage({
      defaultMessage: 'Xóa tài khoản',
      id: 'components.container.authorization.components.usePrepare.1101755904',
    }),
    [Scopes.UserConfigScopes.CHANGE_PASSWORK]: intl.formatMessage({
      defaultMessage: 'Đổi mật khẩu tài khoản',
      id: 'components.container.authorization.components.usePrepare.1723038859',
    }),
    [Scopes.GroupUserConfigScopes.READ]: intl.formatMessage({
      defaultMessage: 'Xem danh sách nhóm tài khoản',
      id: 'components.container.authorization.components.usePrepare.1952018446',
    }),
    [Scopes.GroupUserConfigScopes.CREATE]: intl.formatMessage({
      defaultMessage: 'Tạo nhóm tài khoản',
      id: 'components.container.authorization.components.usePrepare.1581534220',
    }),
    [Scopes.GroupUserConfigScopes.UPDATE]: intl.formatMessage({
      defaultMessage: 'Cập nhật nhóm tài khoản',
      id: 'components.container.authorization.components.usePrepare.1089551213',
    }),
    [Scopes.GroupUserConfigScopes.DELETE]: intl.formatMessage({
      defaultMessage: 'Xóa nhóm tài khoản',
      id: 'components.container.authorization.components.usePrepare.150384808',
    }),
    [Scopes.RoleConfigScopes.READ]: intl.formatMessage({
      defaultMessage: 'Xem danh sách nhóm quyền',
      id: 'components.container.authorization.components.usePrepare.49822170',
    }),
    [Scopes.RoleConfigScopes.CREATE]: intl.formatMessage({
      defaultMessage: 'Tạo nhóm quyền',
      id: 'components.container.authorization.components.usePrepare.71560460',
    }),
    [Scopes.RoleConfigScopes.UPDATE]: intl.formatMessage({
      defaultMessage: 'Cập nhật nhóm quyền',
      id: 'components.container.authorization.components.usePrepare.785520901',
    }),
    [Scopes.RoleConfigScopes.DELETE]: intl.formatMessage({
      defaultMessage: 'Xóa nhóm quyền',
      id: 'components.container.authorization.components.usePrepare.582034832',
    }),
    //document permission
    [Scopes.CategoryAndDocumentScopes.READ]: intl.formatMessage({
      defaultMessage: 'Xem danh sách danh mục & hồ sơ',
      id: 'components.container.authorization.components.usePrepare.241399279',
    }),
    [Scopes.CategoryAndDocumentScopes.CREATE_CATEGORY]: intl.formatMessage({
      defaultMessage: 'Tạo danh mục',
      id: 'components.container.authorization.components.usePrepare.869550144',
    }),
    [Scopes.CategoryAndDocumentScopes.UPDATE_CATEGORY]: intl.formatMessage({
      defaultMessage: 'Cập nhật danh mục',
      id: 'components.container.authorization.components.usePrepare.767499897',
    }),
    [Scopes.CategoryAndDocumentScopes.DELETE_CATEGORY]: intl.formatMessage({
      defaultMessage: 'Xóa danh mục',
      id: 'components.container.authorization.components.usePrepare.1933361500',
    }),
    [Scopes.CategoryAndDocumentScopes.SHARE_CATEGORY]: intl.formatMessage({
      defaultMessage: 'Chia sẻ danh mục',
      id: 'components.container.authorization.components.usePrepare.1560805335',
    }),
    [Scopes.CategoryAndDocumentScopes.CONFIG_TEMPLATE]: intl.formatMessage({
      defaultMessage: 'Cấu hình hồ sơ mẫu',
      id: 'components.container.authorization.components.usePrepare.1472847237',
    }),
    [Scopes.CategoryAndDocumentScopes.HISTORY_TEMPLATE]: intl.formatMessage({
      defaultMessage: 'Lịch sử hồ sơ mẫu',
      id: 'components.container.authorization.components.usePrepare.1815517872',
    }),
    [Scopes.CategoryAndDocumentScopes.CREATE_DOCUMENT]: intl.formatMessage({
      defaultMessage: 'Tạo hồ sơ',
      id: 'components.container.authorization.components.usePrepare.1736463355',
    }),
    [Scopes.CategoryAndDocumentScopes.UPDATE_DOCUMENT]: intl.formatMessage({
      defaultMessage: 'Cập nhật hồ sơ',
      id: 'components.container.authorization.components.usePrepare.1390596194',
    }),
    [Scopes.CategoryAndDocumentScopes.DELETE_DOCUMENT]: intl.formatMessage({
      defaultMessage: 'Xóa hồ sơ',
      id: 'components.container.authorization.components.usePrepare.405248023',
    }),
    [Scopes.CategoryAndDocumentScopes.SHARE_DOCUMENT]: intl.formatMessage({
      defaultMessage: 'Chia sẻ hồ sơ',
      id: 'components.container.authorization.components.usePrepare.1245918030',
    }),
    [Scopes.CategoryAndDocumentScopes.EXPORT_FILE_DOCUMENT]: intl.formatMessage({
      defaultMessage: 'Xuất file PDF hồ sơ',
      id: 'components.container.authorization.components.usePrepare.415667643',
    }),
    [Scopes.CategoryAndDocumentScopes.HISTORY_DOCUMENT]: intl.formatMessage({
      defaultMessage: 'Lịch sử cập nhật hồ sơ',
      id: 'components.container.authorization.components.usePrepare.647363832',
    }),
    [Scopes.DocumentTemplateScopes.READ]: intl.formatMessage({
      defaultMessage: 'Xem danh sách hồ sơ mẫu',
      id: 'components.container.authorization.components.usePrepare.200565270',
    }),
    [Scopes.DocumentTemplateScopes.CREATE]: intl.formatMessage({
      defaultMessage: 'Tạo hồ sơ mẫu',
      id: 'components.container.authorization.components.usePrepare.1188823620',
    }),
    [Scopes.DocumentTemplateScopes.UPDATE]: intl.formatMessage({
      defaultMessage: 'Cập nhật hồ sơ mẫu',
      id: 'components.container.authorization.components.usePrepare.1974734037',
    }),
    [Scopes.DocumentTemplateScopes.DELETE]: intl.formatMessage({
      defaultMessage: 'Xóa hồ sơ mẫu',
      id: 'components.container.authorization.components.usePrepare.197908576',
    }),
    [Scopes.DocumentTemplateScopes.HISTORY]: intl.formatMessage({
      defaultMessage: 'Lịch sử cập nhật hồ sơ mẫu',
      id: 'components.container.authorization.components.usePrepare.1060444991',
    }),
  });

  const grouped = groupByParentKeyName(permission);

  const treePermission = buildTreeData(grouped, moduleName, options.controlKey);

  return treePermission;
};
