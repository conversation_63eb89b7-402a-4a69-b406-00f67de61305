import { Applications, Permission, Users } from '@/api/dataType';
import { create } from 'zustand';

type AppState = {
  expand: boolean;
  isLoading: boolean;
  user: Users | null;
  accessToken: string | null;
  applicationPermissions: Applications[];
  applicationAccess: Applications[];
  userPermissions: Permission[];
};

type AppActions = {
  updateExpand: (expand: AppState['expand']) => void;
  updateLoading: (isLoading: AppState['isLoading']) => void;
  updateUser: (user: AppState['user']) => void;
  updateAccessToken: (accessToken: AppState['accessToken']) => void;
  updateApplicationPermissions: (applicationPermissions: AppState['applicationPermissions']) => void;
  updateApplicationAccess: (applicationAccess: AppState['applicationAccess']) => void;
  updateUserPermissions: (userPermissions: AppState['userPermissions']) => void;
};

const appStories = create<AppState & AppActions>((set, get) => ({
  expand: true,
  updateExpand: (state: boolean) => set(() => ({ expand: !state })),

  isLoading: false,
  updateLoading: (state: boolean) => set(() => ({ isLoading: state })),

  user: null,
  updateUser: (state: AppState['user']) => set(() => ({ user: state })),

  accessToken: null,
  updateAccessToken: (accessToken: AppState['accessToken']) => set(() => ({ accessToken })),

  applicationPermissions: [],
  updateApplicationPermissions: (applicationPermissions: AppState['applicationPermissions']) =>
    set(() => ({ applicationPermissions })),

  applicationAccess: [],
  updateApplicationAccess: (applicationAccess: AppState['applicationAccess']) => set(() => ({ applicationAccess })),

  userPermissions: [],
  updateUserPermissions: (userPermissions: AppState['userPermissions']) => set(() => ({ userPermissions })),
}));

export { appStories, type AppActions, type AppState };

