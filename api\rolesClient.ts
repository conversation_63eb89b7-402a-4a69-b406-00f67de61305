import { BACKEND_API_ENDPOINT } from '@/constants';
import QueryString from 'qs';
import apiPathName from './apiPathName';
import { BaseClient } from './baseClient';
import { Roles } from './dataType';
import { DataResponse, DataWithPagination, FilterOptions, PaginationOptions, SortValue } from './type';

export class RolesClient extends BaseClient<Roles> {
  moduleName = 'RolesClient';
  constructor() {
    super(BACKEND_API_ENDPOINT + apiPathName.role.root);
  }

  find(
    filter?: FilterOptions<Pick<Roles, 'name'>>,
    pagination?: PaginationOptions,
    sort?: SortValue<Roles>,
  ): Promise<DataResponse<DataWithPagination<Roles>>> {
    const queryString = QueryString.stringify({
      page: pagination?.pageIndex,
      limit: pagination?.pageSize,
    });
    const query = queryString.length > 0 ? `?${queryString}` : '';
    return this.api.get(query);
  }

  deleteOrReplaceRole(id: string | number, replacementRoleId: string): Promise<Roles> {
    return this.api.post(`/${id}`, {
      roleReplacedId: replacementRoleId.length > 0 ? replacementRoleId : undefined,
    });
  }

  override getById(id: string | number): Promise<DataResponse<Roles>> {
    return super.getById(id) as Promise<DataResponse<Roles>>;
  }
}
