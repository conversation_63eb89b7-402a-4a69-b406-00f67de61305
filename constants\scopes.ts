enum GeneralConfigScopes {
  /**
   * Truy cập vào trang tổng quan
   */
  READ = 'config:general:read',
}

enum UserConfigScopes {
  /**
   * Xem danh sách tài khoản - Quyền mặc định khi bật chức năng Quản lý tài khoản
   */
  READ = 'config:users:read',
  /**
   * Tạo tài khoản
   */
  CREATE = 'config:users:create',
  /**
   * Cập nhật tài khoản
   */
  UPDATE = 'config:users:update',
  /**
   * Xóa tài khoản
   */
  DELETE = 'config:users:delete',
  /**
   * Đổi mật khẩu tài khoản
   */
  CHANGE_PASSWORK = 'config:users:change-password',
}

enum GroupUserConfigScopes {
  /**
   * Xem danh sách nhóm tài khoản - Quyền mặc định khi bật chức năng Quản lý nhóm tài khoản
   */
  READ = 'config:user-groups:read',
  /**
   * Tạo nhóm tài khoản
   */
  CREATE = 'config:user-groups:create',
  /**
   * Cập nhật nhóm tài khoản
   */
  UPDATE = 'config:user-groups:update',
  /**
   * Xóa nhóm tài khoản
   */
  DELETE = 'config:user-groups:delete',
}

enum RoleConfigScopes {
  /**
   * Xem danh sách nhóm quyền - Quyền mặc định khi bật chức năng Quản lý nhóm quyền
   */
  READ = 'config:roles:read',
  /**
   * Tạo nhóm quyền
   */
  CREATE = 'config:roles:create',
  /**
   * Cập nhật nhóm quyền
   */
  UPDATE = 'config:roles:update',
  /**
   * Xóa nhóm quyền
   */
  DELETE = 'config:roles:delete',
}

enum MonitoringStationConfigScopes {
  /**
   * Truy cập vào trang trạm quan trắc
   */
  READ = 'config:monitoring-stations:read',
}

enum SettingConfigScopes {
  /**
   * Truy cập vào trang cài đặt
   */
  READ = 'config:settings:read',
}

export type Access =
  | (typeof Scopes)[keyof typeof Scopes][keyof (typeof Scopes)[keyof typeof Scopes]]
  | (typeof Scopes)[keyof typeof Scopes][keyof (typeof Scopes)[keyof typeof Scopes]][];

export const moduleScope = Object.freeze({
  GENERAL: 'general',
  USERS: 'users',
  USER_GROUPS: 'user-groups',
  ROLES: 'roles',
  MONITORING_STATIONS: 'monitoring-stations',
  SETTINGS: 'settings',
});

enum DashboardDocumentScopes {
  /**
   * Truy cập vào trang Trang chủ
   * Quyền mặc định khi bật chức năng Trang chủ
   */
  READ = 'document:dashboard:read',
}

enum CategoryAndDocumentScopes {
  /**
   * Xem danh sách danh mục & hồ sơ
   * Quyền mặc định khi bật chức năng Quản lý danh mục & hồ sơ
   */
  READ = 'document:category-document:read',
  /**
   * Tạo danh mục
   */
  CREATE_CATEGORY = 'document:categories:create',
  /**
   * Cập nhật danh mục
   */
  UPDATE_CATEGORY = 'document:categories:update',
  /**
   * Xóa danh mục
   */
  DELETE_CATEGORY = 'document:categories:delete',
  /**
   * Chia sẻ danh mục
   */
  SHARE_CATEGORY = 'document:categories:share',
  /**
   * Cấu hình hồ sơ mẫu
   */
  CONFIG_TEMPLATE = 'document:categories:config-template',
  /**
   * Xem lịch sử hồ sơ mẫu
   */
  HISTORY_TEMPLATE = 'document:categories:history-template',
  /**
   * Tạo hồ sơ
   */
  CREATE_DOCUMENT = 'document:documents:create',
  /**
   * Cập nhật hồ sơ
   */
  UPDATE_DOCUMENT = 'document:documents:update',
  /**
   * Xóa hồ sơ
   */
  DELETE_DOCUMENT = 'document:documents:delete',
  /**
   * Chia sẻ hồ sơ
   */
  SHARE_DOCUMENT = 'document:documents:share',
  /**
   * Xuất file PDF hồ sơ
   */
  EXPORT_FILE_DOCUMENT = 'document:documents:export-file',
  /**
   * Xem lịch sử cập nhật hồ sơ
   */
  HISTORY_DOCUMENT = 'document:documents:history-document',
}

enum DocumentTemplateScopes {
  /**
   * Xem danh sách hồ sơ mẫu
   * Quyền mặc định khi bật chức năng Quản lý hồ sơ mẫu
   */
  READ = 'document:document-templates:read',
  /**
   * Tạo hồ sơ mẫu
   */
  CREATE = 'document:document-templates:create',
  /**
   * Cập nhật hồ sơ mẫu
   */
  UPDATE = 'document:document-templates:update',
  /**
   * Xóa hồ sơ mẫu
   */
  DELETE = 'document:document-templates:delete',
  /**
   * Xem lịch sử cập nhật hồ sơ mẫu
   */
  HISTORY = 'document:document-templates:history',
}

enum FileManagementScopes {
  /**
   * Xem danh sách quản lý file
   * Quyền mặc định khi bật chức năng Quản lý file
   */
  READ = 'document:files:read',
}

enum ProjectManagementScopes {
  /**
   * Xem danh sách dự án
   * Quyền mặc định khi bật chức năng Quản lý dự án
   */
  READ = 'document:projects:read',
}

export const moduleScopeDocument = Object.freeze({
  DASHBOARD: 'dashboard',
  CATEGORY_DOCUMENT: 'category-document',
  DOCUMENTS: 'documents',
  CATEGORIES: 'categories',
  DOCUMENT_TEMPLATES: 'document-templates',
  FILES: 'files',
  PROJECTS: 'projects',
});

export const Scopes = Object.freeze({
  GeneralConfigScopes,
  UserConfigScopes,
  GroupUserConfigScopes,
  RoleConfigScopes,
  MonitoringStationConfigScopes,
  SettingConfigScopes,
  DashboardDocumentScopes,
  CategoryAndDocumentScopes,
  DocumentTemplateScopes,
  FileManagementScopes,
  ProjectManagementScopes,
});
