'use client';

import { LANGUEGES_OPTIONS } from '@/constants';
import Image from 'next/image';
import { useIntl } from 'react-intl';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from 'ui-components';

export default function SwitchLanguage({
  setLangCookie,
  onClose,
}: {
  setLangCookie: (value: string) => void;
  onClose: () => void;
}) {
  const intl = useIntl();
  const lng = intl.locale;
  const currentLang = LANGUEGES_OPTIONS.find((lang) => lang.value === lng);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <div className="w-auto flex flex-row gap-2 border border-solid p-2 rounded-lg cursor-pointer">
          <Image
            src={currentLang?.image as string}
            alt={currentLang?.label as string}
            width={24}
            height={24}
            className="object-contain"
            unoptimized
            priority
          />
        </div>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-[42px] min-w-[42px] p-0 bg-white">
        {LANGUEGES_OPTIONS.filter((lang) => lang.value !== lng).map((lang) => (
          <DropdownMenuItem
            key={lang.value}
            onClick={() => setLangCookie(lang.value)}
            className="cursor-pointer w-full flex flex-row gap-2 p-2"
          >
            <Image
              src={lang.image}
              alt={lang.label}
              width={24}
              height={24}
              className="object-contain"
              unoptimized
              priority
            />
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
