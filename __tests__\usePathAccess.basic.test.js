// Test cơ bản cho usePathAccess logic
describe('usePathAccess Logic Tests', () => {
  describe('🔐 Path Similarity Calculation', () => {
    it('should calculate correct path similarity', () => {
      // Test function tính độ tương đồng path
      const calculatePathSimilarity = (path1, path2) => {
        const segments1 = path1.split('/').filter(Boolean);
        const segments2 = path2.split('/').filter(Boolean);
        
        let commonSegments = 0;
        const minLength = Math.min(segments1.length, segments2.length);
        
        for (let i = 0; i < minLength; i++) {
          if (segments1[i] === segments2[i]) {
            commonSegments++;
          } else {
            break;
          }
        }
        
        return commonSegments / Math.max(segments1.length, segments2.length);
      };

      // Test cases
      expect(calculatePathSimilarity('/accounts', '/accounts')).toBe(1); // Identical
      expect(calculatePathSimilarity('/accounts/group', '/accounts/authorization')).toBe(0.5); // 1 common / 2 max
      expect(calculatePathSimilarity('/accounts', '/settings')).toBe(0); // No common
      expect(calculatePathSimilarity('/accounts/group/123', '/accounts/group/456')).toBe(1); // Same structure
    });
  });

  describe('🏗️ Module Path Mapping', () => {
    it('should correctly map paths to modules', () => {
      const MODULE_PATHS = {
        general: ['/'],
        accounts: ['/accounts', '/accounts/group', '/accounts/authorization'],
        settings: ['/settings', '/settings/attachment', '/settings/storage-capacity'],
        'monitoring-station': ['/monitoring-station'],
      };

      const getModuleFromPath = (path) => {
        for (const [module, paths] of Object.entries(MODULE_PATHS)) {
          if (paths.some(p => path.startsWith(p.split(':')[0]))) {
            return module;
          }
        }
        return null;
      };

      // Test cases
      expect(getModuleFromPath('/accounts')).toBe('accounts');
      expect(getModuleFromPath('/accounts/group')).toBe('accounts');
      expect(getModuleFromPath('/accounts/authorization')).toBe('accounts');
      expect(getModuleFromPath('/settings')).toBe('settings');
      expect(getModuleFromPath('/settings/attachment')).toBe('settings');
      expect(getModuleFromPath('/monitoring-station')).toBe('monitoring-station');
      expect(getModuleFromPath('/invalid')).toBe(null);
    });
  });

  describe('🎯 Permission Checking Logic', () => {
    it('should check permissions correctly', () => {
      const pathWithScopes = {
        '/': 'general:read',
        '/accounts': 'user:read',
        '/accounts/group': 'group:read',
        '/accounts/authorization': 'role:read',
        '/settings': 'setting:read',
        '/monitoring-station': 'monitoring:read',
      };

      const hasPermission = (path, userPermissions) => {
        const requiredScope = pathWithScopes[path];
        if (!requiredScope) return false;
        
        return userPermissions.some(perm => perm.keyName === requiredScope);
      };

      const userPermissions = [
        { keyName: 'user:read' },
        { keyName: 'group:read' },
      ];

      // Test cases
      expect(hasPermission('/accounts', userPermissions)).toBe(true);
      expect(hasPermission('/accounts/group', userPermissions)).toBe(true);
      expect(hasPermission('/accounts/authorization', userPermissions)).toBe(false);
      expect(hasPermission('/settings', userPermissions)).toBe(false);
    });
  });

  describe('🔄 Redirect Logic', () => {
    it('should find best redirect path', () => {
      const MODULE_PATHS = {
        accounts: ['/accounts', '/accounts/group', '/accounts/authorization'],
        settings: ['/settings', '/settings/attachment'],
        'monitoring-station': ['/monitoring-station'],
      };

      const userPermissions = [
        { keyName: 'user:read' },
        { keyName: 'group:read' },
      ];

      const pathWithScopes = {
        '/accounts': 'user:read',
        '/accounts/group': 'group:read',
        '/accounts/authorization': 'role:read',
        '/settings': 'setting:read',
        '/monitoring-station': 'monitoring:read',
      };

      const hasAccess = (path) => {
        const requiredScope = pathWithScopes[path];
        if (!requiredScope) return false;
        return userPermissions.some(perm => perm.keyName === requiredScope);
      };

      const findBestRedirectPath = (currentPath) => {
        // Get current module
        const getModuleFromPath = (path) => {
          for (const [module, paths] of Object.entries(MODULE_PATHS)) {
            if (paths.some(p => path.startsWith(p))) {
              return module;
            }
          }
          return null;
        };

        const currentModule = getModuleFromPath(currentPath);
        
        if (currentModule) {
          // Find accessible paths in same module
          const modulePaths = MODULE_PATHS[currentModule];
          const accessiblePaths = modulePaths.filter(path => hasAccess(path));
          
          if (accessiblePaths.length > 0) {
            return accessiblePaths[0]; // Return first accessible path
          }
        }

        // Find any accessible path
        const allPaths = Object.values(MODULE_PATHS).flat();
        const accessiblePaths = allPaths.filter(path => hasAccess(path));
        
        return accessiblePaths.length > 0 ? accessiblePaths[0] : '/not-permission';
      };

      // Test cases
      expect(findBestRedirectPath('/accounts/authorization')).toBe('/accounts'); // Same module
      expect(findBestRedirectPath('/settings')).toBe('/accounts'); // Different module
      expect(findBestRedirectPath('/monitoring-station')).toBe('/accounts'); // Different module
    });
  });

  describe('📊 Real World Scenarios', () => {
    it('should handle Admin user scenario', () => {
      const adminPermissions = [
        { keyName: 'general:read' },
        { keyName: 'user:read' },
        { keyName: 'group:read' },
        { keyName: 'role:read' },
        { keyName: 'setting:read' },
        { keyName: 'monitoring:read' },
      ];

      const pathWithScopes = {
        '/': 'general:read',
        '/accounts': 'user:read',
        '/accounts/group': 'group:read',
        '/accounts/authorization': 'role:read',
        '/settings': 'setting:read',
        '/monitoring-station': 'monitoring:read',
      };

      const hasAccess = (path) => {
        const requiredScope = pathWithScopes[path];
        if (!requiredScope) return false;
        return adminPermissions.some(perm => perm.keyName === requiredScope);
      };

      // Admin should have access to all paths
      expect(hasAccess('/')).toBe(true);
      expect(hasAccess('/accounts')).toBe(true);
      expect(hasAccess('/accounts/group')).toBe(true);
      expect(hasAccess('/accounts/authorization')).toBe(true);
      expect(hasAccess('/settings')).toBe(true);
      expect(hasAccess('/monitoring-station')).toBe(true);
    });

    it('should handle limited user scenario', () => {
      const limitedPermissions = [
        { keyName: 'user:read' },
        { keyName: 'group:read' },
      ];

      const pathWithScopes = {
        '/': 'general:read',
        '/accounts': 'user:read',
        '/accounts/group': 'group:read',
        '/accounts/authorization': 'role:read',
        '/settings': 'setting:read',
        '/monitoring-station': 'monitoring:read',
      };

      const hasAccess = (path) => {
        const requiredScope = pathWithScopes[path];
        if (!requiredScope) return false;
        return limitedPermissions.some(perm => perm.keyName === requiredScope);
      };

      // Limited user should only have access to accounts
      expect(hasAccess('/')).toBe(false);
      expect(hasAccess('/accounts')).toBe(true);
      expect(hasAccess('/accounts/group')).toBe(true);
      expect(hasAccess('/accounts/authorization')).toBe(false);
      expect(hasAccess('/settings')).toBe(false);
      expect(hasAccess('/monitoring-station')).toBe(false);
    });

    it('should handle no permissions scenario', () => {
      const noPermissions = [];

      const pathWithScopes = {
        '/': 'general:read',
        '/accounts': 'user:read',
        '/settings': 'setting:read',
      };

      const hasAccess = (path) => {
        const requiredScope = pathWithScopes[path];
        if (!requiredScope) return false;
        return noPermissions.some(perm => perm.keyName === requiredScope);
      };

      // User with no permissions should not have access to anything
      expect(hasAccess('/')).toBe(false);
      expect(hasAccess('/accounts')).toBe(false);
      expect(hasAccess('/settings')).toBe(false);
    });
  });

  describe('🚨 Edge Cases', () => {
    it('should handle dynamic paths', () => {
      const matchDynamicPath = (pattern, path) => {
        const patternSegments = pattern.split('/');
        const pathSegments = path.split('/');
        
        if (patternSegments.length !== pathSegments.length) {
          return false;
        }
        
        for (let i = 0; i < patternSegments.length; i++) {
          if (patternSegments[i].startsWith(':')) {
            continue; // Dynamic segment matches anything
          }
          if (patternSegments[i] !== pathSegments[i]) {
            return false;
          }
        }
        
        return true;
      };

      // Test dynamic path matching
      expect(matchDynamicPath('/accounts/group/:id', '/accounts/group/123')).toBe(true);
      expect(matchDynamicPath('/accounts/group/:id', '/accounts/group/abc')).toBe(true);
      expect(matchDynamicPath('/accounts/group/:id', '/accounts/group')).toBe(false);
      expect(matchDynamicPath('/accounts/group/:id', '/accounts/group/123/extra')).toBe(false);
    });

    it('should handle empty and invalid inputs', () => {
      const calculatePathSimilarity = (path1, path2) => {
        if (!path1 || !path2) return 0;
        
        const segments1 = path1.split('/').filter(Boolean);
        const segments2 = path2.split('/').filter(Boolean);
        
        if (segments1.length === 0 && segments2.length === 0) return 1;
        if (segments1.length === 0 || segments2.length === 0) return 0;
        
        let commonSegments = 0;
        const minLength = Math.min(segments1.length, segments2.length);
        
        for (let i = 0; i < minLength; i++) {
          if (segments1[i] === segments2[i]) {
            commonSegments++;
          } else {
            break;
          }
        }
        
        return commonSegments / Math.max(segments1.length, segments2.length);
      };

      // Test edge cases
      expect(calculatePathSimilarity('', '')).toBe(1);
      expect(calculatePathSimilarity('', '/accounts')).toBe(0);
      expect(calculatePathSimilarity('/accounts', '')).toBe(0);
      expect(calculatePathSimilarity(null, '/accounts')).toBe(0);
      expect(calculatePathSimilarity('/accounts', null)).toBe(0);
    });
  });
});
