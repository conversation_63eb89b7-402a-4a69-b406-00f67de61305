import { useEffect, useState } from 'react';
import {
  Button,
  Dialog,
  DialogContent,
  DialogDescription,
  DialogOverlay,
  DialogPortal,
  DialogTitle,
} from 'ui-components';
import { WEB_CORE_URL } from '@/constants';
import { FormattedMessage } from 'react-intl';

export default function SessionExpiredAlert() {
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    const sessionExpired = localStorage.getItem('session_expired');
    if (sessionExpired === 'true') {
      setIsVisible(true);

      localStorage.removeItem('session_expired');
    }

    const handleSessionExpired = (event: CustomEvent) => {
      setIsVisible(true);
    };

    const handleBeforeUnload = () => {
      if (isVisible) {
        localStorage.setItem('force_logout', 'true');
      }
    };

    window.addEventListener('session_expired', handleSessionExpired as EventListener);
    window.addEventListener('beforeunload', handleBeforeUnload);

    const forceLogout = localStorage.getItem('force_logout');
    if (forceLogout === 'true') {
      localStorage.removeItem('force_logout');
      handleSignOut();
    }

    return () => {
      window.removeEventListener('session_expired', handleSessionExpired as EventListener);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isVisible]);

  const handleSignOut = () => {
    localStorage.removeItem('force_logout');
    localStorage.removeItem('session_expired');
    window.location.assign(`${WEB_CORE_URL}/api/logto/sign-out`);
  };

  return (
    <Dialog open={isVisible}>
      <DialogPortal>
        <DialogOverlay />
        <DialogContent className="p-0 border-none gap-0 max-w-[450px] sm:rounded-xl">
          <DialogTitle className="hidden"></DialogTitle>
          <DialogDescription className="hidden"></DialogDescription>
          <div className="flex flex-col">
            <div className="flex flex-col p-5 gap-5">
              <div className="flex flex-col gap-2">
                <span className="text-lg font-semibold leading-7 text-center text-gray-800">
                  <FormattedMessage
                    defaultMessage="Thông tin tài khoản của bạn đã được cập nhật"
                    id="components.commons.SessionExpiredAlert.1539686550"
                  />
                </span>
                <span className="text-sm font-normal leading-5 text-center text-gray-700">
                  <FormattedMessage
                    defaultMessage="Vì lý do bảo mật, hệ thống sẽ đăng xuất tài khoản của bạn khỏi các thiết bị. Vui lòng đăng nhập lại để tiếp tục sử dụng ứng dụng."
                    id="components.commons.SessionExpiredAlert.1537149278"
                  />
                </span>
              </div>
            </div>
            <div className="flex flex-row justify-end gap-3 p-[14px] border-t border-gray-200">
              <Button type="button" className="flex-1 text-sm font-normal" onClick={handleSignOut}>
                <FormattedMessage
                  defaultMessage="Xác nhận"
                  id="components.container.categories.components.DeleteCategory.559188735"
                />
              </Button>
            </div>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
}
