import { API_RESOURCE_PATH } from '@/constants';
import { logtoClientEdge } from '@/libraries/logto';
import { LogtoContext } from '@logto/next';
import { cookies } from 'next/headers';
import { NextRequest, NextResponse } from 'next/server';
// export const runtime = "edge";

export async function GET(
  request: NextRequest,
): Promise<NextResponse<LogtoContext & { orgId?: string }>> {
  const resource = API_RESOURCE_PATH;
  const cookieStore = await cookies();
  let orgId = cookieStore.get('x-tenant-id')?.value ?? '';

  if (!orgId) {
    const { claims, isAuthenticated } =
      await logtoClientEdge.getLogtoContext(request);

    if (claims?.organizations && claims.organizations.length > 0) {
      orgId = claims.organizations[0] ?? '';
    }
  }

  const context = await logtoClientEdge.getLogtoContext(request, {
    getAccessToken: true,
    fetchUserInfo: true,
    organizationId: orgId,
    resource,
  });

  const { isAuthenticated, accessToken, claims } = context;

  return NextResponse.json({
    accessToken,
    claims,
    isAuthenticated,
    orgId,
  });
}