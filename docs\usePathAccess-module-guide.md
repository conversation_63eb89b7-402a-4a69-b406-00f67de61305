# usePathAccess Hook - Module-based Access Guide

## Tổng quan

Hook `usePathAccess` đã được cập nhật để hỗ trợ quản lý quyền truy cập theo module. Điều này cho phép bạn:

1. **<PERSON>h<PERSON><PERSON> các đường dẫn theo module** (accounts, settings, monitoring-station)
2. **Lấy đường dẫn mặc định** cho mỗi module dựa trên quyền của user
3. **Chuyển hướng thông minh** trong cùng module khi user không có quyền truy cập đường dẫn cụ thể

## Các tính năng mới

### 1. Module Mapping

```typescript
const MODULE_PATHS = {
  accounts: [
    '/accounts',           // Đường dẫn đầu tiên = mặc định ưu tiên
    '/accounts/group',
    '/accounts/group/:id',
    '/accounts/authorization',
  ],
  settings: [
    '/settings',           // Đường dẫn đầu tiên = mặc định ưu tiên
    '/settings/attachment',
    '/settings/storage-capacity',
    '/settings/digital-signature',
    '/settings/package-management',
  ],
  'monitoring-station': ['/monitoring-station'],
};
```

### 2. Các function mới

#### `getModuleDefaultPath(moduleName: string): string | null`
Lấy đường dẫn mặc định đầu tiên mà user có quyền truy cập trong module.

```typescript
const { getModuleDefaultPath } = usePathAccess();

// Lấy đường dẫn mặc định cho module accounts
const accountsDefault = getModuleDefaultPath('accounts');
// Trả về: '/accounts' nếu có quyền, hoặc '/accounts/group' nếu không có quyền '/accounts'
```

#### `getDefaultPathForCurrentModule(currentPath: string): string | null`
Lấy đường dẫn mặc định cho module của đường dẫn hiện tại.

```typescript
const { getDefaultPathForCurrentModule } = usePathAccess();

// Nếu đang ở '/accounts/authorization' nhưng không có quyền
const defaultPath = getDefaultPathForCurrentModule('/accounts/authorization');
// Trả về: '/accounts' hoặc đường dẫn đầu tiên có quyền trong module accounts
```

#### `moduleDefaultPaths: Map<string, string>`
Map chứa đường dẫn mặc định cho từng module.

```typescript
const { moduleDefaultPaths } = usePathAccess();

// Hiển thị tất cả đường dẫn mặc định
moduleDefaultPaths.forEach((path, module) => {
  console.log(`${module}: ${path}`);
});
```

### 3. Cải tiến redirectIfUnauthorized

Function này giờ sẽ ưu tiên chuyển hướng đến đường dẫn mặc định trong cùng module trước khi chuyển đến module khác.

```typescript
const { redirectIfUnauthorized } = usePathAccess();

// Nếu user truy cập '/settings/digital-signature' nhưng không có quyền
const redirectPath = redirectIfUnauthorized('/settings/digital-signature');
// Sẽ trả về '/settings' nếu có quyền, thay vì chuyển đến module khác
```

## Cách sử dụng

### Ví dụ 1: Navigation Menu

```typescript
const NavigationMenu = () => {
  const router = useRouter();
  const { getModuleDefaultPath, canAccess } = usePathAccess();

  const navigateToModule = (moduleName: string) => {
    const defaultPath = getModuleDefaultPath(moduleName);
    if (defaultPath) {
      router.push(defaultPath);
    }
  };

  return (
    <nav>
      <button onClick={() => navigateToModule('accounts')}>
        Quản lý tài khoản
      </button>
      <button onClick={() => navigateToModule('settings')}>
        Cài đặt hệ thống
      </button>
    </nav>
  );
};
```

### Ví dụ 2: Route Guard với Module Support

```typescript
const RouteGuard = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const { 
    loading, 
    canAccess, 
    redirectIfUnauthorized,
    getDefaultPathForCurrentModule 
  } = usePathAccess();

  useEffect(() => {
    if (!loading) {
      const currentPath = router.pathname;
      
      if (!canAccess(currentPath)) {
        const redirectPath = redirectIfUnauthorized(currentPath);
        if (redirectPath) {
          router.replace(redirectPath);
        }
      }
    }
  }, [loading, router.pathname]);

  if (loading) return <div>Loading...</div>;
  
  return <>{children}</>;
};
```

### Ví dụ 3: Breadcrumb với Module Default

```typescript
const Breadcrumb = () => {
  const router = useRouter();
  const { getModuleDefaultPath } = usePathAccess();
  
  const currentPath = router.pathname;
  const pathSegments = currentPath.split('/').filter(Boolean);
  
  return (
    <nav>
      {pathSegments.map((segment, index) => {
        const isModule = ['accounts', 'settings', 'monitoring-station'].includes(segment);
        
        if (isModule) {
          const defaultPath = getModuleDefaultPath(segment);
          return (
            <span key={segment}>
              <Link href={defaultPath || `/${segment}`}>
                {segment}
              </Link>
              {index < pathSegments.length - 1 && ' > '}
            </span>
          );
        }
        
        return <span key={segment}>{segment}</span>;
      })}
    </nav>
  );
};
```

## Lợi ích

1. **UX tốt hơn**: User được chuyển đến trang có quyền gần nhất thay vì bị đá ra ngoài
2. **Navigation thông minh**: Menu có thể tự động điều hướng đến trang phù hợp
3. **Quản lý quyền linh hoạt**: Dễ dàng thêm/sửa module và đường dẫn mặc định
4. **Performance**: Sử dụng Map để tra cứu nhanh đường dẫn mặc định

## Migration từ phiên bản cũ

Tất cả API cũ vẫn hoạt động bình thường. Bạn chỉ cần thêm các function mới khi cần:

```typescript
// Cũ - vẫn hoạt động
const { canAccess, redirectIfUnauthorized } = usePathAccess();

// Mới - thêm các tính năng module
const { 
  canAccess, 
  redirectIfUnauthorized,
  getModuleDefaultPath,           // Mới
  getDefaultPathForCurrentModule, // Mới
  moduleDefaultPaths             // Mới
} = usePathAccess();
```
