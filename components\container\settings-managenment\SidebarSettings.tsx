'use client';

import IconCommon from '@/components/commons/IconLayout';
import { cn } from '@/utils/tailwind';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { FormattedMessage, useIntl } from 'react-intl';
import type { UrlObject } from 'url';
import React from 'react';
import { appStories } from '@/store';
import { dataTabsSetting } from './dataSidebarSettings';

type Url = string | UrlObject;
export type ChildTag = {
  label: string | React.JSX.Element;
  icon: React.JSX.Element;
  path: Url;
};

export type DataTabs = {
  name: string | React.JSX.Element;
  childTag: ChildTag[];
  hidden?: boolean;
};

export default function SiderbarSettings() {
  const intl = useIntl();
  const pathname = usePathname();
  const { user } = appStories((state) => state);

  const locale = intl.locale as 'vi' | 'en';

  if (!user) return null;

  return (
    <div className="w-[260px] min-w-[260px] bg-gray-50 h-full p-5 border-r flex-shrink-0 flex flex-col gap-[28px]">
      {dataTabsSetting.map(({ name, childTag }, indexTab) => {
        return (
          <div key={`${name}_${indexTab}`} className="flex flex-col gap-2">
            <span className="text-base font-medium text-gray-700">{name}</span>
            {childTag.map(({ label, path, icon }, index) => {
              const isActive = pathname === path;
              return (
                <Link
                  key={`${label}_${index}`}
                  href={path}
                  className={cn(
                    'flex flex-row gap-2.5 items-center p-2.5 text-gray-700',
                    'hover:bg-gray-100 hover:rounded-lg',
                    {
                      'rounded-lg bg-primary-500 text-white hover:bg-primary-500': isActive,
                    },
                  )}
                  prefetch={false}
                >
                  {icon}
                  <span className="text-sm font-normal flex-1">{label}</span>
                  <IconCommon name="CaretRight" size={20} className="text-current flex-shrink-0" />
                </Link>
              );
            })}
          </div>
        );
      })}
    </div>
  );
}
