'use client';

import { useCreateClient } from '@/api/useCreateClient';
import { WEB_CORE_URL } from '@/constants';
import { urlPathName } from '@/constants/urlPathName';
import { usePathname, useRouter } from 'next/navigation';
import { PropsWithChildren, useEffect } from 'react';
import { Spin, TooltipProvider } from 'ui-components';

export default function GuardAuth({ children }: PropsWithChildren) {
  const { auth } = useCreateClient();
  const pathname = usePathname();
  const router = useRouter();

  const { data, isLoading, refetch } = auth.getAuthAccess();

  useEffect(() => {
    refetch();
  }, [pathname]);

  useEffect(() => {
    if (!isLoading && !data?.access) {
      router.push(`${WEB_CORE_URL}${urlPathName.ACCESS_DENIED}`);
    }
  }, [isLoading, data, pathname]);

  if (isLoading) {
    return (
      <div className="h-screen w-screen relative flex flex-row items-center justify-center overflow-hidden">
        <Spin loading />
      </div>
    );
  }

  return <TooltipProvider delayDuration={100}>{children}</TooltipProvider>;
}
