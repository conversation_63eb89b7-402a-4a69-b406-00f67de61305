import { NextRequest, NextResponse } from 'next/server';
import { initLanguage } from './actions/detectLanguage';
import { signOut } from './actions/logtoActions';
import { BASE_URL, WEB_CORE_URL } from './constants';

export async function middleware(request: NextRequest) {
  const response = NextResponse.next();
  // Initialize language
  await initLanguage(request, response);

  const tenantId = request.cookies.has('x-tenant-id') && request.cookies.has('token:base');
  const hasToken = request.cookies.getAll().some((token) => token.name.startsWith('token:org:'));

  if (!hasToken) {
    return await signOut().then(() => {
      return NextResponse.redirect(`${WEB_CORE_URL}/auth?redirect_to=${BASE_URL}`);
    });
  }

  if (!tenantId) {
    return NextResponse.redirect(`${WEB_CORE_URL}`);
  }

  return response;
}

export const config = {
  matcher: ['/((?!api|_next/static|_next/image|images|icons|favicon.ico|health).*)', { source: '/' }],
};
