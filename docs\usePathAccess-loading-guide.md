# usePathAccess Hook - In-Progress Loading Guide

## Tổng quan

Hook `usePathAccess` đã được cập nhật để hỗ trợ **in-progress loading states** khi xử lý các pathname. Điều này cho phép bạn:

1. **Theo dõi trạng thái loading** cho từng path riêng biệt
2. **Xử lý bất đồng bộ** với simulation delay cho UX tốt hơn
3. **Batch processing** nhiều paths cùng lúc
4. **Cache kết quả** để tránh xử lý lại không cần thiết

## Các tính năng Loading mới

### 1. Path Loading State Management

```typescript
const {
  pathLoadingState,        // Map<string, boolean> - trạng thái loading của từng path
  isPathLoading,          // (path: string) => boolean - kiểm tra path có đang loading
  canAccessWithLoading,   // async function với loading simulation
  canAccessMultiple,      // batch processing nhiều paths
} = usePathAccess();
```

### 2. Synchronous vs Asynchronous Access Check

#### `canAccess(path: string): boolean` - Đồng bộ (không loading)
```typescript
const { canAccess } = usePathAccess();

// Kiểm tra ngay lập tức, không có loading state
const hasAccess = canAccess('/accounts');
console.log('Có quyền:', hasAccess); // true/false ngay lập tức
```

#### `canAccessWithLoading(path: string, delay?: number): Promise<boolean>` - Bất đồng bộ (có loading)
```typescript
const { canAccessWithLoading, isPathLoading } = usePathAccess();

// Kiểm tra với loading simulation
const checkAccess = async () => {
  console.log('Loading:', isPathLoading('/accounts')); // false
  
  const hasAccess = await canAccessWithLoading('/accounts', 500); // 500ms delay
  
  console.log('Có quyền:', hasAccess);
  console.log('Loading:', isPathLoading('/accounts')); // false (đã hoàn thành)
};
```

### 3. Batch Processing

```typescript
const { canAccessMultiple } = usePathAccess();

const checkMultiplePaths = async () => {
  const paths = ['/accounts', '/settings', '/monitoring-station'];
  
  // Xử lý tất cả paths song song
  const results = await canAccessMultiple(paths, 300);
  
  // results là Map<string, boolean>
  results.forEach((hasAccess, path) => {
    console.log(`${path}: ${hasAccess ? 'Có quyền' : 'Không có quyền'}`);
  });
};
```

### 4. Loading State Tracking

```typescript
const { pathLoadingState, isPathLoading } = usePathAccess();

// Kiểm tra path cụ thể
const isAccountsLoading = isPathLoading('/accounts');

// Lấy tất cả paths đang loading
const loadingPaths = Array.from(pathLoadingState.entries())
  .filter(([path, isLoading]) => isLoading)
  .map(([path]) => path);

console.log('Đang loading:', loadingPaths);
```

## Cách sử dụng trong Components

### 1. Loading Indicator Component

```typescript
const PathLoadingIndicator: React.FC<{ path: string }> = ({ path }) => {
  const { isPathLoading } = usePathAccess();
  
  if (isPathLoading(path)) {
    return <span className="loading-spinner">🔄 Đang kiểm tra...</span>;
  }
  
  return null;
};

// Sử dụng
<button onClick={() => handleNavigate('/accounts')}>
  Accounts
  <PathLoadingIndicator path="/accounts" />
</button>
```

### 2. Navigation với Loading States

```typescript
const NavigationMenu: React.FC = () => {
  const { canAccessWithLoading, isPathLoading } = usePathAccess();
  const router = useRouter();
  
  const handleNavigate = async (path: string) => {
    // Kiểm tra quyền với loading
    const hasAccess = await canAccessWithLoading(path);
    
    if (hasAccess) {
      router.push(path);
    } else {
      alert('Bạn không có quyền truy cập trang này');
    }
  };
  
  return (
    <nav>
      {['/accounts', '/settings', '/monitoring-station'].map(path => (
        <button
          key={path}
          onClick={() => handleNavigate(path)}
          disabled={isPathLoading(path)}
          className={isPathLoading(path) ? 'loading' : ''}
        >
          {path}
          {isPathLoading(path) && <span>🔄</span>}
        </button>
      ))}
    </nav>
  );
};
```

### 3. Batch Loading cho Dashboard

```typescript
const Dashboard: React.FC = () => {
  const { canAccessMultiple } = usePathAccess();
  const [accessResults, setAccessResults] = useState<Map<string, boolean>>(new Map());
  const [loading, setLoading] = useState(false);
  
  useEffect(() => {
    const checkAllAccess = async () => {
      setLoading(true);
      
      const paths = ['/accounts', '/settings', '/monitoring-station', '/health'];
      const results = await canAccessMultiple(paths, 200);
      
      setAccessResults(results);
      setLoading(false);
    };
    
    checkAllAccess();
  }, [canAccessMultiple]);
  
  if (loading) {
    return <div>🔄 Đang kiểm tra quyền truy cập...</div>;
  }
  
  return (
    <div>
      <h2>Dashboard</h2>
      {Array.from(accessResults.entries()).map(([path, hasAccess]) => (
        <div key={path}>
          <strong>{path}</strong>: {hasAccess ? '✅ Có quyền' : '❌ Không có quyền'}
        </div>
      ))}
    </div>
  );
};
```

### 4. Smart Loading với Cache

```typescript
const SmartPathChecker: React.FC = () => {
  const { canAccessWithLoading, pathLoadingState } = usePathAccess();
  const [results, setResults] = useState<Map<string, boolean>>(new Map());
  
  const checkPath = async (path: string) => {
    // Nếu đã có kết quả và không đang loading, không cần check lại
    if (results.has(path) && !pathLoadingState.get(path)) {
      console.log('Sử dụng kết quả đã cache:', results.get(path));
      return;
    }
    
    // Check với loading
    const hasAccess = await canAccessWithLoading(path, 300);
    setResults(prev => new Map(prev).set(path, hasAccess));
  };
  
  return (
    <div>
      {['/accounts', '/settings'].map(path => (
        <div key={path}>
          <button onClick={() => checkPath(path)}>
            Check {path}
          </button>
          {pathLoadingState.get(path) && <span>🔄</span>}
          {results.has(path) && (
            <span>{results.get(path) ? '✅' : '❌'}</span>
          )}
        </div>
      ))}
    </div>
  );
};
```

## Advanced Usage

### Custom Hook với Loading Management

```typescript
export const usePathAccessWithLoading = () => {
  const pathAccess = usePathAccess();
  
  const checkMultiplePathsWithStatus = async (paths: string[]) => {
    const results = await pathAccess.canAccessMultiple(paths);
    return {
      results,
      isAnyLoading: paths.some(path => pathAccess.isPathLoading(path)),
      loadingPaths: paths.filter(path => pathAccess.isPathLoading(path)),
      completedPaths: paths.filter(path => !pathAccess.isPathLoading(path)),
    };
  };
  
  return {
    ...pathAccess,
    checkMultiplePathsWithStatus,
  };
};
```

## Performance & Best Practices

### 1. Cache Management
- Kết quả được cache tự động để tránh xử lý lại
- Cache được xóa khi `userPermissions` thay đổi
- Sử dụng `canAccess` cho kiểm tra nhanh, `canAccessWithLoading` cho UX tốt hơn

### 2. Memory Management
- Tất cả timeouts được cleanup tự động
- Loading states được xóa khi component unmount
- Không có memory leaks

### 3. Concurrent Requests
- Nếu cùng một path được check nhiều lần, chỉ có 1 request thực sự
- Các request khác sẽ đợi kết quả của request đầu tiên

### 4. Error Handling
```typescript
const safeCheckAccess = async (path: string) => {
  try {
    const hasAccess = await canAccessWithLoading(path);
    return hasAccess;
  } catch (error) {
    console.error('Error checking access:', error);
    // Fallback to synchronous check
    return canAccess(path);
  }
};
```

## Migration từ phiên bản cũ

Tất cả API cũ vẫn hoạt động bình thường:

```typescript
// Cũ - vẫn hoạt động
const { canAccess } = usePathAccess();
const hasAccess = canAccess('/accounts'); // Đồng bộ, không loading

// Mới - thêm loading states
const { canAccessWithLoading, isPathLoading } = usePathAccess();
const hasAccessAsync = await canAccessWithLoading('/accounts'); // Bất đồng bộ, có loading
```
