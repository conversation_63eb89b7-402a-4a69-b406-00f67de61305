import { renderHook } from '@testing-library/react';
import { usePathAccess } from '../../hook/usePathAccess';
import { appStories } from '../../store';

// Mock store
jest.mock('../../store');
const mockAppStories = appStories as jest.MockedFunction<typeof appStories>;

// Test utilities for usePathAccess
export const createMockPermissions = (permissions: string[]) => {
  return permissions.map((keyName) => ({ keyName }));
};

export const setupUserWithPermissions = (permissions: string[]) => {
  mockAppStories.mockReturnValue({
    userPermissions: createMockPermissions(permissions),
  });
};

export const renderPathAccessHook = () => {
  return renderHook(() => usePathAccess());
};

// Common permission sets for testing
export const PERMISSION_SETS = {
  ADMIN: ['general:read', 'user:read', 'group:read', 'role:read', 'setting:read', 'monitoring:read'],
  ACCOUNT_MANAGER: ['user:read', 'group:read', 'role:read'],
  SETTINGS_MANAGER: ['setting:read'],
  VIEWER: ['user:read', 'group:read'],
  MONITORING_ONLY: ['monitoring:read'],
  NO_PERMISSIONS: [],
};

// Common test paths
export const TEST_PATHS = {
  PUBLIC: ['/access-denied', '/profile', '/health'],
  GENERAL: ['/'],
  ACCOUNTS: ['/accounts', '/accounts/group', '/accounts/group/123', '/accounts/authorization'],
  SETTINGS: [
    '/settings',
    '/settings/attachment',
    '/settings/storage-capacity',
    '/settings/digital-signature',
    '/settings/package-management',
  ],
  MONITORING: ['/monitoring-station'],
  INVALID: ['/invalid-path', '/accounts/invalid', '/settings/invalid'],
};

// Test scenarios
export const TEST_SCENARIOS = [
  {
    name: 'Admin User',
    permissions: PERMISSION_SETS.ADMIN,
    shouldHaveAccess: [
      ...TEST_PATHS.PUBLIC,
      ...TEST_PATHS.GENERAL,
      ...TEST_PATHS.ACCOUNTS,
      ...TEST_PATHS.SETTINGS,
      ...TEST_PATHS.MONITORING,
    ],
    shouldNotHaveAccess: TEST_PATHS.INVALID,
    expectedRedirects: {},
  },
  {
    name: 'Account Manager',
    permissions: PERMISSION_SETS.ACCOUNT_MANAGER,
    shouldHaveAccess: [...TEST_PATHS.PUBLIC, ...TEST_PATHS.ACCOUNTS],
    shouldNotHaveAccess: [
      ...TEST_PATHS.GENERAL,
      ...TEST_PATHS.SETTINGS,
      ...TEST_PATHS.MONITORING,
      ...TEST_PATHS.INVALID,
    ],
    expectedRedirects: {
      '/': '/accounts',
      '/settings': '/accounts',
      '/monitoring-station': '/accounts',
    },
  },
  {
    name: 'Settings Manager',
    permissions: PERMISSION_SETS.SETTINGS_MANAGER,
    shouldHaveAccess: [...TEST_PATHS.PUBLIC, ...TEST_PATHS.SETTINGS],
    shouldNotHaveAccess: [
      ...TEST_PATHS.GENERAL,
      ...TEST_PATHS.ACCOUNTS,
      ...TEST_PATHS.MONITORING,
      ...TEST_PATHS.INVALID,
    ],
    expectedRedirects: {
      '/': '/settings',
      '/accounts': '/settings',
      '/monitoring-station': '/settings',
    },
  },
  {
    name: 'Viewer',
    permissions: PERMISSION_SETS.VIEWER,
    shouldHaveAccess: [...TEST_PATHS.PUBLIC, '/accounts', '/accounts/group', '/accounts/group/123'],
    shouldNotHaveAccess: [
      ...TEST_PATHS.GENERAL,
      '/accounts/authorization',
      ...TEST_PATHS.SETTINGS,
      ...TEST_PATHS.MONITORING,
      ...TEST_PATHS.INVALID,
    ],
    expectedRedirects: {
      '/': '/accounts',
      '/accounts/authorization': '/accounts', // or '/accounts/group'
      '/settings': '/accounts',
    },
  },
  {
    name: 'No Permissions',
    permissions: PERMISSION_SETS.NO_PERMISSIONS,
    shouldHaveAccess: TEST_PATHS.PUBLIC,
    shouldNotHaveAccess: [
      ...TEST_PATHS.GENERAL,
      ...TEST_PATHS.ACCOUNTS,
      ...TEST_PATHS.SETTINGS,
      ...TEST_PATHS.MONITORING,
      ...TEST_PATHS.INVALID,
    ],
    expectedRedirects: {
      '/': '/not-permission',
      '/accounts': '/not-permission',
      '/settings': '/not-permission',
    },
  },
];

// Helper function to run scenario tests
export const runScenarioTest = (scenario: (typeof TEST_SCENARIOS)[0]) => {
  describe(`Scenario: ${scenario.name}`, () => {
    beforeEach(() => {
      setupUserWithPermissions(scenario.permissions);
    });

    it('should have correct access permissions', () => {
      const { result } = renderPathAccessHook();

      scenario.shouldHaveAccess.forEach((path) => {
        expect(result.current.canAccess(path)).toBe(true);
      });

      scenario.shouldNotHaveAccess.forEach((path) => {
        expect(result.current.canAccess(path)).toBe(false);
      });
    });

    it('should redirect correctly', () => {
      const { result } = renderPathAccessHook();

      Object.entries(scenario.expectedRedirects).forEach(([fromPath, toPath]) => {
        const redirectPath = result.current.redirectIfUnauthorized(fromPath);
        expect(redirectPath).toBe(toPath);
      });
    });
  });
};

// Performance test helpers
export const measureExecutionTime = async (fn: () => Promise<any>) => {
  const start = performance.now();
  await fn();
  return performance.now() - start;
};

export const createLargePermissionSet = (count: number) => {
  return Array.from({ length: count }, (_, i) => `permission:${i}`);
};

export const createLargePathSet = (count: number) => {
  return Array.from({ length: count }, (_, i) => `/path/${i}`);
};

// Mock data generators
export const generateDynamicPaths = (base: string, count: number) => {
  return Array.from({ length: count }, (_, i) => `${base}/${i}`);
};

export const generateRandomPermissions = (count: number) => {
  const scopes = ['read', 'write', 'delete', 'admin'];
  const modules = ['user', 'group', 'role', 'setting', 'monitoring'];

  return Array.from({ length: count }, () => {
    const module = modules[Math.floor(Math.random() * modules.length)];
    const scope = scopes[Math.floor(Math.random() * scopes.length)];
    return `${module}:${scope}`;
  });
};

// Assertion helpers
export const expectPathAccess = (result: any, path: string, expected: boolean) => {
  expect(result.current.canAccess(path)).toBe(expected);
};

export const expectRedirect = (result: any, fromPath: string, toPath: string | null) => {
  expect(result.current.redirectIfUnauthorized(fromPath)).toBe(toPath);
};

export const expectModuleDefault = (result: any, module: string, expectedPath: string | null) => {
  expect(result.current.getModuleDefaultPath(module)).toBe(expectedPath);
};

// Test data validation
export const validateTestData = () => {
  // Ensure all test paths are valid
  const allPaths = [
    ...TEST_PATHS.PUBLIC,
    ...TEST_PATHS.GENERAL,
    ...TEST_PATHS.ACCOUNTS,
    ...TEST_PATHS.SETTINGS,
    ...TEST_PATHS.MONITORING,
  ];

  allPaths.forEach((path) => {
    expect(typeof path).toBe('string');
    expect(path.startsWith('/')).toBe(true);
  });

  // Ensure all permission sets are valid
  Object.values(PERMISSION_SETS).forEach((permissions) => {
    expect(Array.isArray(permissions)).toBe(true);
    permissions.forEach((permission) => {
      expect(typeof permission).toBe('string');
      expect(permission.includes(':')).toBe(true);
    });
  });
};

export default {
  createMockPermissions,
  setupUserWithPermissions,
  renderPathAccessHook,
  PERMISSION_SETS,
  TEST_PATHS,
  TEST_SCENARIOS,
  runScenarioTest,
  measureExecutionTime,
  expectPathAccess,
  expectRedirect,
  expectModuleDefault,
  validateTestData,
};
