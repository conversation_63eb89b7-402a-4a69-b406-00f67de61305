'use client';

import { urlPathName } from '@/constants/urlPathName';
import { FormattedMessage } from 'react-intl';
import {
  Bell,
  HardDrives,
  IdentificationCard,
  Paperclip,
  ShoppingBagOpen,
  Signature,
  User,
} from '@phosphor-icons/react';
import { DataTabs } from './SidebarSettings';

export const dataTabsSetting: DataTabs[] = [
  {
    name: <FormattedMessage defaultMessage="Cài đặt" id="components.Layout.dataItemAside.1549596250" />,
    childTag: [
      {
        label: (
          <FormattedMessage
            defaultMessage="Thông báo"
            id="components.settings.settings-managenment.dataSiderbarSettings.notifications"
          />
        ),
        icon: <Bell size={20} className="text-current" weight="regular" />,
        path: urlPathName.SETTINGS.ROOT,
      },
      {
        label: (
          <FormattedMessage
            defaultMessage="Tệp đính kèm"
            id="components.settings.settings-managenment.dataSiderbarSettings.attachmentSidebar"
          />
        ),
        icon: <Paperclip size={20} className="text-current" weight="regular" />,
        path: urlPathName.SETTINGS.ATTACHMENT,
      },
      {
        label: (
          <FormattedMessage
            defaultMessage="Dung lượng lưu trữ"
            id="components.settings.settings-managenment.dataSiderbarSettings.storageCapacity"
          />
        ),
        icon: <HardDrives size={20} className="text-current" weight="regular" />,
        path: urlPathName.SETTINGS.STORAGECAPACITY,
      },
      {
        label: (
          <FormattedMessage
            defaultMessage="Chữ ký số"
            id="components.settings.settings-managenment.dataSiderbarSettings.digitalSignature"
          />
        ),
        icon: <Signature size={20} className="text-current" weight="regular" />,
        path: urlPathName.SETTINGS.DIGITALSIGNATURE,
      },
      {
        label: (
          <FormattedMessage
            defaultMessage="Quản lý gói"
            id="components.settings.settings-managenment.dataSiderbarSettings.packageManagement"
          />
        ),
        icon: <ShoppingBagOpen size={20} className="text-current" weight="regular" />,
        path: urlPathName.SETTINGS.PACKAGEMANAGEMENT,
      },
    ],
  },
];
